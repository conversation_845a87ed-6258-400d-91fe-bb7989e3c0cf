import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'

export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    if (!session?.user?.id) {
      return NextResponse.json(
        { success: false, error: 'يجب تسجيل الدخول أولاً' },
        { status: 401 }
      )
    }

    // جلب بيانات المستخدم مع الإحصائيات
    const user = await prisma.user.findUnique({
      where: { id: session.user.id },
      select: {
        id: true,
        name: true,
        email: true,
        avatar: true,
        phone: true,
        city: true,
        createdAt: true,
        freeAdsCount: true,
        paidAdsCount: true,
        ratingAverage: true,
        ratingCount: true,
        _count: {
          select: {
            ads: true,
            sentMessages: true,
            receivedMessages: true,
            favorites: true,
            givenRatings: true,
            receivedRatings: true
          }
        }
      }
    })

    if (!user) {
      return NextResponse.json(
        { success: false, error: 'المستخدم غير موجود' },
        { status: 404 }
      )
    }

    return NextResponse.json({
      success: true,
      data: user
    })

  } catch (error) {
    console.error('Error fetching user profile:', error)
    return NextResponse.json(
      { success: false, error: 'حدث خطأ في جلب بيانات المستخدم' },
      { status: 500 }
    )
  }
}

export async function PUT(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    if (!session?.user?.id) {
      return NextResponse.json(
        { success: false, error: 'يجب تسجيل الدخول أولاً' },
        { status: 401 }
      )
    }

    const body = await request.json()
    const { name, phone, city, avatar } = body

    // التحقق من صحة البيانات
    if (!name || name.trim().length < 2) {
      return NextResponse.json(
        { success: false, error: 'الاسم مطلوب ويجب أن يكون أكثر من حرفين' },
        { status: 400 }
      )
    }

    // تحديث بيانات المستخدم
    const updatedUser = await prisma.user.update({
      where: { id: session.user.id },
      data: {
        name: name.trim(),
        ...(phone && { phone: phone.trim() }),
        ...(city && { city: city.trim() }),
        ...(avatar && { avatar: avatar.trim() })
      },
      select: {
        id: true,
        name: true,
        email: true,
        avatar: true,
        phone: true,
        city: true,
        createdAt: true,
        freeAdsCount: true,
        paidAdsCount: true,
        ratingAverage: true,
        ratingCount: true,
        _count: {
          select: {
            ads: true,
            sentMessages: true,
            receivedMessages: true,
            favorites: true,
            givenRatings: true,
            receivedRatings: true
          }
        }
      }
    })

    return NextResponse.json({
      success: true,
      data: updatedUser,
      message: 'تم تحديث البيانات بنجاح'
    })

  } catch (error) {
    console.error('Error updating user profile:', error)
    return NextResponse.json(
      { success: false, error: 'حدث خطأ في تحديث البيانات' },
      { status: 500 }
    )
  }
}
