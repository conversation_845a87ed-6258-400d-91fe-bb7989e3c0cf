{"version": 3, "sources": [], "sections": [{"offset": {"line": 31, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/kaliha/khalihatanbaa/src/components/layout/Header.tsx"], "sourcesContent": ["\"use client\";\n\nimport { useState } from \"react\";\nimport Link from \"next/link\";\nimport { useSession, signOut } from \"next-auth/react\";\nimport {\n  MagnifyingGlassIcon,\n  PlusIcon,\n  ChatBubbleLeftRightIcon,\n  HeartIcon,\n  UserIcon,\n  Bars3Icon,\n  XMarkIcon,\n  ChartBarIcon,\n} from \"@heroicons/react/24/outline\";\n\nexport function Header() {\n  const { data: session } = useSession();\n  const [isMenuOpen, setIsMenuOpen] = useState(false);\n  const [searchQuery, setSearchQuery] = useState(\"\");\n\n  const handleSearch = (e: React.FormEvent) => {\n    e.preventDefault();\n    if (searchQuery.trim()) {\n      window.location.href = `/search?q=${encodeURIComponent(searchQuery)}`;\n    }\n  };\n\n  return (\n    <header className=\"bg-white shadow-sm border-b border-gray-200 sticky top-0 z-50\">\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n        <div className=\"flex items-center justify-between h-16\">\n          {/* الشعار */}\n          <Link\n            href=\"/\"\n            className=\"flex items-center space-x-2 space-x-reverse\"\n          >\n            <div className=\"w-10 h-10 bg-primary-500 rounded-lg flex items-center justify-center\">\n              <span className=\"text-white font-bold text-lg\">خ</span>\n            </div>\n            <div className=\"hidden sm:block\">\n              <h1 className=\"text-xl font-bold text-dark-800\">\n                خَلّيها تنْباع\n              </h1>\n              <p className=\"text-xs text-dark-500\">منصة البيع والشراء</p>\n            </div>\n          </Link>\n\n          {/* شريط البحث */}\n          <div className=\"flex-1 max-w-lg mx-4\">\n            <form onSubmit={handleSearch} className=\"relative\">\n              <input\n                type=\"text\"\n                placeholder=\"ابحث عن أي شيء...\"\n                value={searchQuery}\n                onChange={(e) => setSearchQuery(e.target.value)}\n                className=\"w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent\"\n              />\n              <MagnifyingGlassIcon className=\"absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400\" />\n            </form>\n          </div>\n\n          {/* أزرار التنقل */}\n          <div className=\"flex items-center space-x-4 space-x-reverse\">\n            {session ? (\n              <>\n                {/* نشر إعلان */}\n                <Link\n                  href=\"/ads/create\"\n                  className=\"bg-primary-500 text-white px-4 py-2 rounded-lg hover:bg-primary-600 transition-colors flex items-center space-x-2 space-x-reverse\"\n                >\n                  <PlusIcon className=\"h-5 w-5\" />\n                  <span className=\"hidden sm:inline\">نشر إعلان</span>\n                </Link>\n\n                {/* الرسائل */}\n                <Link\n                  href=\"/messages\"\n                  className=\"p-2 text-dark-600 hover:text-primary-500 transition-colors relative\"\n                >\n                  <ChatBubbleLeftRightIcon className=\"h-6 w-6\" />\n                  {/* نقطة الإشعارات */}\n                  <span className=\"absolute -top-1 -right-1 h-3 w-3 bg-primary-500 rounded-full\"></span>\n                </Link>\n\n                {/* المفضلة */}\n                <Link\n                  href=\"/favorites\"\n                  className=\"p-2 text-dark-600 hover:text-primary-500 transition-colors\"\n                >\n                  <HeartIcon className=\"h-6 w-6\" />\n                </Link>\n\n                {/* لوحة التحكم */}\n                <Link\n                  href=\"/dashboard\"\n                  className=\"p-2 text-dark-600 hover:text-primary-500 transition-colors\"\n                >\n                  <ChartBarIcon className=\"h-6 w-6\" />\n                </Link>\n\n                {/* قائمة المستخدم */}\n                <div className=\"relative\">\n                  <button\n                    onClick={() => setIsMenuOpen(!isMenuOpen)}\n                    className=\"flex items-center space-x-2 space-x-reverse p-2 text-dark-600 hover:text-primary-500 transition-colors\"\n                  >\n                    <UserIcon className=\"h-6 w-6\" />\n                    <span className=\"hidden sm:inline\">\n                      {session.user.name}\n                    </span>\n                  </button>\n\n                  {isMenuOpen && (\n                    <div className=\"absolute right-0 mt-2 w-48 bg-white rounded-lg shadow-lg border border-gray-200 py-2\">\n                      <Link\n                        href=\"/profile\"\n                        className=\"block px-4 py-2 text-dark-700 hover:bg-gray-50\"\n                      >\n                        الملف الشخصي\n                      </Link>\n                      <Link\n                        href=\"/profile/ads\"\n                        className=\"block px-4 py-2 text-dark-700 hover:bg-gray-50\"\n                      >\n                        إعلاناتي\n                      </Link>\n                      <Link\n                        href=\"/profile/settings\"\n                        className=\"block px-4 py-2 text-dark-700 hover:bg-gray-50\"\n                      >\n                        الإعدادات\n                      </Link>\n                      <hr className=\"my-2\" />\n                      <button\n                        onClick={() => signOut()}\n                        className=\"block w-full text-right px-4 py-2 text-red-600 hover:bg-gray-50\"\n                      >\n                        تسجيل الخروج\n                      </button>\n                    </div>\n                  )}\n                </div>\n              </>\n            ) : (\n              <>\n                <Link\n                  href=\"/auth/login\"\n                  className=\"text-dark-600 hover:text-primary-500 transition-colors\"\n                >\n                  تسجيل الدخول\n                </Link>\n                <Link\n                  href=\"/auth/register\"\n                  className=\"bg-primary-500 text-white px-4 py-2 rounded-lg hover:bg-primary-600 transition-colors\"\n                >\n                  إنشاء حساب\n                </Link>\n              </>\n            )}\n\n            {/* قائمة الموبايل */}\n            <button\n              onClick={() => setIsMenuOpen(!isMenuOpen)}\n              className=\"sm:hidden p-2 text-dark-600\"\n            >\n              {isMenuOpen ? (\n                <XMarkIcon className=\"h-6 w-6\" />\n              ) : (\n                <Bars3Icon className=\"h-6 w-6\" />\n              )}\n            </button>\n          </div>\n        </div>\n\n        {/* قائمة الموبايل */}\n        {isMenuOpen && (\n          <div className=\"sm:hidden border-t border-gray-200 py-4\">\n            <div className=\"space-y-2\">\n              <Link\n                href=\"/ads\"\n                className=\"block px-4 py-2 text-dark-700 hover:bg-gray-50 rounded\"\n              >\n                جميع الإعلانات\n              </Link>\n              <Link\n                href=\"/categories\"\n                className=\"block px-4 py-2 text-dark-700 hover:bg-gray-50 rounded\"\n              >\n                جميع الفئات\n              </Link>\n              {session && (\n                <>\n                  <Link\n                    href=\"/ads/new\"\n                    className=\"block px-4 py-2 text-primary-600 hover:bg-gray-50 rounded\"\n                  >\n                    نشر إعلان جديد\n                  </Link>\n                  <Link\n                    href=\"/messages\"\n                    className=\"block px-4 py-2 text-dark-700 hover:bg-gray-50 rounded\"\n                  >\n                    الرسائل\n                  </Link>\n                  <Link\n                    href=\"/favorites\"\n                    className=\"block px-4 py-2 text-dark-700 hover:bg-gray-50 rounded\"\n                  >\n                    المفضلة\n                  </Link>\n                  <Link\n                    href=\"/dashboard\"\n                    className=\"block px-4 py-2 text-dark-700 hover:bg-gray-50 rounded\"\n                  >\n                    لوحة التحكم\n                  </Link>\n                </>\n              )}\n            </div>\n          </div>\n        )}\n      </div>\n    </header>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AALA;;;;;;AAgBO,SAAS;IACd,MAAM,EAAE,MAAM,OAAO,EAAE,GAAG,CAAA,GAAA,8IAAA,CAAA,aAAU,AAAD;IACnC,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE/C,MAAM,eAAe,CAAC;QACpB,EAAE,cAAc;QAChB,IAAI,YAAY,IAAI,IAAI;YACtB,OAAO,QAAQ,CAAC,IAAI,GAAG,CAAC,UAAU,EAAE,mBAAmB,cAAc;QACvE;IACF;IAEA,qBACE,8OAAC;QAAO,WAAU;kBAChB,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC,4JAAA,CAAA,UAAI;4BACH,MAAK;4BACL,WAAU;;8CAEV,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCAAK,WAAU;kDAA+B;;;;;;;;;;;8CAEjD,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAG,WAAU;sDAAkC;;;;;;sDAGhD,8OAAC;4CAAE,WAAU;sDAAwB;;;;;;;;;;;;;;;;;;sCAKzC,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAK,UAAU;gCAAc,WAAU;;kDACtC,8OAAC;wCACC,MAAK;wCACL,aAAY;wCACZ,OAAO;wCACP,UAAU,CAAC,IAAM,eAAe,EAAE,MAAM,CAAC,KAAK;wCAC9C,WAAU;;;;;;kDAEZ,8OAAC,qOAAA,CAAA,sBAAmB;wCAAC,WAAU;;;;;;;;;;;;;;;;;sCAKnC,8OAAC;4BAAI,WAAU;;gCACZ,wBACC;;sDAEE,8OAAC,4JAAA,CAAA,UAAI;4CACH,MAAK;4CACL,WAAU;;8DAEV,8OAAC,+MAAA,CAAA,WAAQ;oDAAC,WAAU;;;;;;8DACpB,8OAAC;oDAAK,WAAU;8DAAmB;;;;;;;;;;;;sDAIrC,8OAAC,4JAAA,CAAA,UAAI;4CACH,MAAK;4CACL,WAAU;;8DAEV,8OAAC,6OAAA,CAAA,0BAAuB;oDAAC,WAAU;;;;;;8DAEnC,8OAAC;oDAAK,WAAU;;;;;;;;;;;;sDAIlB,8OAAC,4JAAA,CAAA,UAAI;4CACH,MAAK;4CACL,WAAU;sDAEV,cAAA,8OAAC,iNAAA,CAAA,YAAS;gDAAC,WAAU;;;;;;;;;;;sDAIvB,8OAAC,4JAAA,CAAA,UAAI;4CACH,MAAK;4CACL,WAAU;sDAEV,cAAA,8OAAC,uNAAA,CAAA,eAAY;gDAAC,WAAU;;;;;;;;;;;sDAI1B,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDACC,SAAS,IAAM,cAAc,CAAC;oDAC9B,WAAU;;sEAEV,8OAAC,+MAAA,CAAA,WAAQ;4DAAC,WAAU;;;;;;sEACpB,8OAAC;4DAAK,WAAU;sEACb,QAAQ,IAAI,CAAC,IAAI;;;;;;;;;;;;gDAIrB,4BACC,8OAAC;oDAAI,WAAU;;sEACb,8OAAC,4JAAA,CAAA,UAAI;4DACH,MAAK;4DACL,WAAU;sEACX;;;;;;sEAGD,8OAAC,4JAAA,CAAA,UAAI;4DACH,MAAK;4DACL,WAAU;sEACX;;;;;;sEAGD,8OAAC,4JAAA,CAAA,UAAI;4DACH,MAAK;4DACL,WAAU;sEACX;;;;;;sEAGD,8OAAC;4DAAG,WAAU;;;;;;sEACd,8OAAC;4DACC,SAAS,IAAM,CAAA,GAAA,8IAAA,CAAA,UAAO,AAAD;4DACrB,WAAU;sEACX;;;;;;;;;;;;;;;;;;;iEAQT;;sDACE,8OAAC,4JAAA,CAAA,UAAI;4CACH,MAAK;4CACL,WAAU;sDACX;;;;;;sDAGD,8OAAC,4JAAA,CAAA,UAAI;4CACH,MAAK;4CACL,WAAU;sDACX;;;;;;;;8CAOL,8OAAC;oCACC,SAAS,IAAM,cAAc,CAAC;oCAC9B,WAAU;8CAET,2BACC,8OAAC,iNAAA,CAAA,YAAS;wCAAC,WAAU;;;;;6DAErB,8OAAC,iNAAA,CAAA,YAAS;wCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;gBAO5B,4BACC,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,4JAAA,CAAA,UAAI;gCACH,MAAK;gCACL,WAAU;0CACX;;;;;;0CAGD,8OAAC,4JAAA,CAAA,UAAI;gCACH,MAAK;gCACL,WAAU;0CACX;;;;;;4BAGA,yBACC;;kDACE,8OAAC,4JAAA,CAAA,UAAI;wCACH,MAAK;wCACL,WAAU;kDACX;;;;;;kDAGD,8OAAC,4JAAA,CAAA,UAAI;wCACH,MAAK;wCACL,WAAU;kDACX;;;;;;kDAGD,8OAAC,4JAAA,CAAA,UAAI;wCACH,MAAK;wCACL,WAAU;kDACX;;;;;;kDAGD,8OAAC,4JAAA,CAAA,UAAI;wCACH,MAAK;wCACL,WAAU;kDACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAWnB", "debugId": null}}, {"offset": {"line": 473, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/kaliha/khalihatanbaa/src/types/index.ts"], "sourcesContent": ["// أنواع البيانات الأساسية للتطبيق\n\nexport interface User {\n  id: string;\n  name: string;\n  email: string;\n  phone: string;\n  role: \"user\" | \"admin\";\n  avatar?: string;\n  isActive: boolean;\n  freeAdsCount: number;\n  freeAdsExpiresAt?: Date;\n  paidAdsCount: number;\n  paidAdsExpiresAt?: Date;\n  ratingAverage: number;\n  ratingCount: number;\n  createdAt: Date;\n  updatedAt: Date;\n}\n\nexport interface Ad {\n  id: string;\n  title: string;\n  description: string;\n  price: number;\n  category: string;\n  subCategory?: string;\n  condition: \"جديد\" | \"مستعمل\";\n  city: string;\n  region?: string;\n  addressDetail?: string;\n  imageUrls: string[];\n  specifications?: Record<string, any>;\n  views: number;\n  isActive: boolean;\n  isFreeAd: boolean;\n  isPromoted: boolean;\n  expiresAt?: Date;\n  createdAt: Date;\n  updatedAt: Date;\n  userId: string;\n  user?: User;\n}\n\nexport interface Message {\n  id: string;\n  content: string;\n  isRead: boolean;\n  createdAt: Date;\n  fromId: string;\n  toId: string;\n  adId?: string;\n  from?: User;\n  to?: User;\n  ad?: Ad;\n}\n\nexport interface Favorite {\n  id: string;\n  userId: string;\n  adId: string;\n  createdAt: Date;\n  user?: User;\n  ad?: Ad;\n}\n\nexport interface Rating {\n  id: string;\n  rating: number;\n  comment?: string;\n  createdAt: Date;\n  userId: string;\n  sellerId: string;\n  giver?: User;\n  receiver?: User;\n}\n\nexport interface AdPackage {\n  id: string;\n  name: string;\n  adsCount: number;\n  price: number;\n  duration: number;\n  isActive: boolean;\n  createdAt: Date;\n  userId: string;\n  user?: User;\n}\n\n// أنواع الفئات\nexport const CATEGORIES = {\n  عقارات: {\n    name: \"عقارات\",\n    icon: \"🏠\",\n    subCategories: [\"شقة\", \"فيلا\", \"أرض\", \"محل تجاري\", \"مكتب\"],\n  },\n  سيارات: {\n    name: \"سيارات\",\n    icon: \"🚗\",\n    subCategories: [\n      \"تويوتا\",\n      \"نيسان\",\n      \"هيونداي\",\n      \"كيا\",\n      \"مرسيدس\",\n      \"BMW\",\n      \"أخرى\",\n    ],\n  },\n  إلكترونيات: {\n    name: \"إلكترونيات\",\n    icon: \"📱\",\n    subCategories: [\"هاتف ذكي\", \"لابتوب\", \"تلفاز\", \"كاميرا\", \"أجهزة منزلية\"],\n  },\n  أثاث: {\n    name: \"أثاث\",\n    icon: \"🪑\",\n    subCategories: [\"غرفة نوم\", \"غرفة جلوس\", \"مطبخ\", \"مكتب\", \"ديكور\"],\n  },\n  ملابس: {\n    name: \"ملابس\",\n    icon: \"👕\",\n    subCategories: [\"رجالي\", \"نسائي\", \"أطفال\", \"أحذية\", \"إكسسوارات\"],\n  },\n  رياضة: {\n    name: \"رياضة\",\n    icon: \"⚽\",\n    subCategories: [\"كرة قدم\", \"كرة سلة\", \"جيم\", \"دراجات\", \"أخرى\"],\n  },\n} as const;\n\nexport type CategoryKey = keyof typeof CATEGORIES;\n\n// أنواع المدن السورية\nexport const SYRIAN_CITIES = [\n  \"دمشق\",\n  \"حلب\",\n  \"حمص\",\n  \"حماة\",\n  \"اللاذقية\",\n  \"طرطوس\",\n  \"درعا\",\n  \"السويداء\",\n  \"القنيطرة\",\n  \"دير الزور\",\n  \"الرقة\",\n  \"الحسكة\",\n  \"إدلب\",\n  \"ريف دمشق\",\n] as const;\n\nexport type SyrianCity = (typeof SYRIAN_CITIES)[number];\n\n// أنواع الاستعلامات\nexport interface SearchFilters {\n  keyword?: string;\n  category?: CategoryKey;\n  subCategory?: string;\n  city?: SyrianCity;\n  minPrice?: number;\n  maxPrice?: number;\n  condition?: \"جديد\" | \"مستعمل\";\n  sortBy?: \"newest\" | \"oldest\" | \"price_low\" | \"price_high\";\n}\n\nexport interface PaginationParams {\n  page: number;\n  limit: number;\n}\n\nexport interface ApiResponse<T> {\n  success: boolean;\n  data?: T;\n  message?: string;\n  error?: string;\n}\n\nexport interface PaginatedResponse<T> extends ApiResponse<T[]> {\n  pagination?: {\n    page: number;\n    limit: number;\n    total: number;\n    totalPages: number;\n  };\n}\n"], "names": [], "mappings": "AAAA,kCAAkC;;;;;AA0F3B,MAAM,aAAa;IACxB,QAAQ;QACN,MAAM;QACN,MAAM;QACN,eAAe;YAAC;YAAO;YAAQ;YAAO;YAAa;SAAO;IAC5D;IACA,QAAQ;QACN,MAAM;QACN,MAAM;QACN,eAAe;YACb;YACA;YACA;YACA;YACA;YACA;YACA;SACD;IACH;IACA,YAAY;QACV,MAAM;QACN,MAAM;QACN,eAAe;YAAC;YAAY;YAAU;YAAS;YAAU;SAAe;IAC1E;IACA,MAAM;QACJ,MAAM;QACN,MAAM;QACN,eAAe;YAAC;YAAY;YAAa;YAAQ;YAAQ;SAAQ;IACnE;IACA,OAAO;QACL,MAAM;QACN,MAAM;QACN,eAAe;YAAC;YAAS;YAAS;YAAS;YAAS;SAAY;IAClE;IACA,OAAO;QACL,MAAM;QACN,MAAM;QACN,eAAe;YAAC;YAAW;YAAW;YAAO;YAAU;SAAO;IAChE;AACF;AAKO,MAAM,gBAAgB;IAC3B;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;CACD", "debugId": null}}, {"offset": {"line": 570, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/kaliha/khalihatanbaa/src/components/home/<USER>"], "sourcesContent": ["'use client'\n\nimport { useState } from 'react'\nimport { MagnifyingGlassIcon } from '@heroicons/react/24/outline'\nimport { CATEGORIES } from '@/types'\n\nexport function Hero() {\n  const [searchQuery, setSearchQuery] = useState('')\n  const [selectedCategory, setSelectedCategory] = useState('')\n\n  const handleSearch = (e: React.FormEvent) => {\n    e.preventDefault()\n    const params = new URLSearchParams()\n    if (searchQuery.trim()) params.set('q', searchQuery)\n    if (selectedCategory) params.set('category', selectedCategory)\n    \n    window.location.href = `/search?${params.toString()}`\n  }\n\n  return (\n    <section className=\"bg-gradient-to-br from-primary-50 to-secondary-50 py-16 lg:py-24\">\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n        <div className=\"text-center\">\n          {/* العنوان الرئيسي */}\n          <h1 className=\"text-4xl lg:text-6xl font-bold text-dark-800 mb-6\">\n            <span className=\"text-primary-500\">خَلّيها</span>{' '}\n            <span className=\"text-secondary-500\">تنْباع</span>\n          </h1>\n          \n          <p className=\"text-xl lg:text-2xl text-dark-600 mb-8 max-w-3xl mx-auto\">\n            منصة البيع والشراء الأولى في سوريا\n            <br />\n            <span className=\"text-lg text-dark-500\">\n              اعثر على أفضل العروض أو بع منتجاتك بسهولة\n            </span>\n          </p>\n\n          {/* شريط البحث المتقدم */}\n          <div className=\"max-w-4xl mx-auto\">\n            <form onSubmit={handleSearch} className=\"bg-white rounded-2xl shadow-lg p-6\">\n              <div className=\"flex flex-col lg:flex-row gap-4\">\n                {/* حقل البحث */}\n                <div className=\"flex-1 relative\">\n                  <input\n                    type=\"text\"\n                    placeholder=\"ابحث عن أي شيء... (سيارة، شقة، هاتف، إلخ)\"\n                    value={searchQuery}\n                    onChange={(e) => setSearchQuery(e.target.value)}\n                    className=\"w-full pl-12 pr-4 py-4 text-lg border border-gray-300 rounded-xl focus:ring-2 focus:ring-primary-500 focus:border-transparent\"\n                  />\n                  <MagnifyingGlassIcon className=\"absolute left-4 top-1/2 transform -translate-y-1/2 h-6 w-6 text-gray-400\" />\n                </div>\n\n                {/* اختيار الفئة */}\n                <div className=\"lg:w-64\">\n                  <select\n                    value={selectedCategory}\n                    onChange={(e) => setSelectedCategory(e.target.value)}\n                    className=\"w-full px-4 py-4 text-lg border border-gray-300 rounded-xl focus:ring-2 focus:ring-primary-500 focus:border-transparent\"\n                  >\n                    <option value=\"\">جميع الفئات</option>\n                    {Object.entries(CATEGORIES).map(([key, category]) => (\n                      <option key={key} value={key}>\n                        {category.icon} {category.name}\n                      </option>\n                    ))}\n                  </select>\n                </div>\n\n                {/* زر البحث */}\n                <button\n                  type=\"submit\"\n                  className=\"lg:w-32 bg-primary-500 text-white px-8 py-4 rounded-xl hover:bg-primary-600 transition-colors font-semibold text-lg\"\n                >\n                  بحث\n                </button>\n              </div>\n            </form>\n          </div>\n\n          {/* إحصائيات سريعة */}\n          <div className=\"mt-16 grid grid-cols-2 lg:grid-cols-4 gap-8\">\n            <div className=\"text-center\">\n              <div className=\"text-3xl lg:text-4xl font-bold text-primary-500 mb-2\">\n                1000+\n              </div>\n              <div className=\"text-dark-600\">إعلان نشط</div>\n            </div>\n            <div className=\"text-center\">\n              <div className=\"text-3xl lg:text-4xl font-bold text-secondary-500 mb-2\">\n                500+\n              </div>\n              <div className=\"text-dark-600\">مستخدم مسجل</div>\n            </div>\n            <div className=\"text-center\">\n              <div className=\"text-3xl lg:text-4xl font-bold text-primary-500 mb-2\">\n                14\n              </div>\n              <div className=\"text-dark-600\">محافظة</div>\n            </div>\n            <div className=\"text-center\">\n              <div className=\"text-3xl lg:text-4xl font-bold text-secondary-500 mb-2\">\n                24/7\n              </div>\n              <div className=\"text-dark-600\">دعم متواصل</div>\n            </div>\n          </div>\n\n          {/* الكلمات المفتاحية الشائعة */}\n          <div className=\"mt-12\">\n            <p className=\"text-dark-600 mb-4\">البحث الشائع:</p>\n            <div className=\"flex flex-wrap justify-center gap-3\">\n              {[\n                'سيارات مستعملة',\n                'شقق للبيع',\n                'هواتف ذكية',\n                'أثاث منزلي',\n                'لابتوب',\n                'دراجات نارية'\n              ].map((keyword) => (\n                <button\n                  key={keyword}\n                  onClick={() => {\n                    setSearchQuery(keyword)\n                    handleSearch({ preventDefault: () => {} } as React.FormEvent)\n                  }}\n                  className=\"px-4 py-2 bg-white text-dark-600 rounded-full border border-gray-300 hover:border-primary-500 hover:text-primary-500 transition-colors\"\n                >\n                  {keyword}\n                </button>\n              ))}\n            </div>\n          </div>\n        </div>\n      </div>\n    </section>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAJA;;;;;AAMO,SAAS;IACd,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEzD,MAAM,eAAe,CAAC;QACpB,EAAE,cAAc;QAChB,MAAM,SAAS,IAAI;QACnB,IAAI,YAAY,IAAI,IAAI,OAAO,GAAG,CAAC,KAAK;QACxC,IAAI,kBAAkB,OAAO,GAAG,CAAC,YAAY;QAE7C,OAAO,QAAQ,CAAC,IAAI,GAAG,CAAC,QAAQ,EAAE,OAAO,QAAQ,IAAI;IACvD;IAEA,qBACE,8OAAC;QAAQ,WAAU;kBACjB,cAAA,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;;kCAEb,8OAAC;wBAAG,WAAU;;0CACZ,8OAAC;gCAAK,WAAU;0CAAmB;;;;;;4BAAe;0CAClD,8OAAC;gCAAK,WAAU;0CAAqB;;;;;;;;;;;;kCAGvC,8OAAC;wBAAE,WAAU;;4BAA2D;0CAEtE,8OAAC;;;;;0CACD,8OAAC;gCAAK,WAAU;0CAAwB;;;;;;;;;;;;kCAM1C,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAK,UAAU;4BAAc,WAAU;sCACtC,cAAA,8OAAC;gCAAI,WAAU;;kDAEb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDACC,MAAK;gDACL,aAAY;gDACZ,OAAO;gDACP,UAAU,CAAC,IAAM,eAAe,EAAE,MAAM,CAAC,KAAK;gDAC9C,WAAU;;;;;;0DAEZ,8OAAC,qOAAA,CAAA,sBAAmB;gDAAC,WAAU;;;;;;;;;;;;kDAIjC,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CACC,OAAO;4CACP,UAAU,CAAC,IAAM,oBAAoB,EAAE,MAAM,CAAC,KAAK;4CACnD,WAAU;;8DAEV,8OAAC;oDAAO,OAAM;8DAAG;;;;;;gDAChB,OAAO,OAAO,CAAC,qHAAA,CAAA,aAAU,EAAE,GAAG,CAAC,CAAC,CAAC,KAAK,SAAS,iBAC9C,8OAAC;wDAAiB,OAAO;;4DACtB,SAAS,IAAI;4DAAC;4DAAE,SAAS,IAAI;;uDADnB;;;;;;;;;;;;;;;;kDAQnB,8OAAC;wCACC,MAAK;wCACL,WAAU;kDACX;;;;;;;;;;;;;;;;;;;;;;kCAQP,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;kDAAuD;;;;;;kDAGtE,8OAAC;wCAAI,WAAU;kDAAgB;;;;;;;;;;;;0CAEjC,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;kDAAyD;;;;;;kDAGxE,8OAAC;wCAAI,WAAU;kDAAgB;;;;;;;;;;;;0CAEjC,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;kDAAuD;;;;;;kDAGtE,8OAAC;wCAAI,WAAU;kDAAgB;;;;;;;;;;;;0CAEjC,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;kDAAyD;;;;;;kDAGxE,8OAAC;wCAAI,WAAU;kDAAgB;;;;;;;;;;;;;;;;;;kCAKnC,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAE,WAAU;0CAAqB;;;;;;0CAClC,8OAAC;gCAAI,WAAU;0CACZ;oCACC;oCACA;oCACA;oCACA;oCACA;oCACA;iCACD,CAAC,GAAG,CAAC,CAAC,wBACL,8OAAC;wCAEC,SAAS;4CACP,eAAe;4CACf,aAAa;gDAAE,gBAAgB,KAAO;4CAAE;wCAC1C;wCACA,WAAU;kDAET;uCAPI;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAgBvB", "debugId": null}}, {"offset": {"line": 923, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/kaliha/khalihatanbaa/src/components/ui/PlaceholderImage.tsx"], "sourcesContent": ["interface PlaceholderImageProps {\n  width?: number\n  height?: number\n  text?: string\n  className?: string\n}\n\nexport function PlaceholderImage({ \n  width = 300, \n  height = 200, \n  text = 'صورة', \n  className = '' \n}: PlaceholderImageProps) {\n  return (\n    <div \n      className={`bg-gray-200 flex items-center justify-center text-gray-500 ${className}`}\n      style={{ width, height }}\n    >\n      <div className=\"text-center\">\n        <svg \n          className=\"mx-auto h-12 w-12 text-gray-400 mb-2\" \n          fill=\"none\" \n          stroke=\"currentColor\" \n          viewBox=\"0 0 24 24\"\n        >\n          <path \n            strokeLinecap=\"round\" \n            strokeLinejoin=\"round\" \n            strokeWidth={2} \n            d=\"M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z\" \n          />\n        </svg>\n        <p className=\"text-sm\">{text}</p>\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;;AAOO,SAAS,iBAAiB,EAC/B,QAAQ,GAAG,EACX,SAAS,GAAG,EACZ,OAAO,MAAM,EACb,YAAY,EAAE,EACQ;IACtB,qBACE,8OAAC;QACC,WAAW,CAAC,2DAA2D,EAAE,WAAW;QACpF,OAAO;YAAE;YAAO;QAAO;kBAEvB,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBACC,WAAU;oBACV,MAAK;oBACL,QAAO;oBACP,SAAQ;8BAER,cAAA,8OAAC;wBACC,eAAc;wBACd,gBAAe;wBACf,aAAa;wBACb,GAAE;;;;;;;;;;;8BAGN,8OAAC;oBAAE,WAAU;8BAAW;;;;;;;;;;;;;;;;;AAIhC", "debugId": null}}, {"offset": {"line": 984, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/kaliha/khalihatanbaa/src/components/ui/FavoriteButton.tsx"], "sourcesContent": ["'use client'\n\nimport { useState, useEffect } from 'react'\nimport { useSession } from 'next-auth/react'\nimport { useRouter } from 'next/navigation'\nimport { HeartIcon } from '@heroicons/react/24/outline'\nimport { HeartIcon as HeartSolidIcon } from '@heroicons/react/24/solid'\n\ninterface FavoriteButtonProps {\n  adId: string\n  initialIsFavorite?: boolean\n  size?: 'sm' | 'md' | 'lg'\n  showText?: boolean\n  className?: string\n}\n\nexport function FavoriteButton({ \n  adId, \n  initialIsFavorite = false, \n  size = 'md',\n  showText = false,\n  className = ''\n}: FavoriteButtonProps) {\n  const { data: session, status } = useSession()\n  const router = useRouter()\n  const [isFavorite, setIsFavorite] = useState(initialIsFavorite)\n  const [loading, setLoading] = useState(false)\n\n  // التحقق من حالة المفضلة عند تحميل المكون\n  useEffect(() => {\n    if (status === 'authenticated' && session?.user?.id) {\n      checkFavoriteStatus()\n    }\n  }, [status, session, adId])\n\n  const checkFavoriteStatus = async () => {\n    try {\n      const response = await fetch(`/api/favorites/check?adId=${adId}`)\n      const data = await response.json()\n      \n      if (data.success) {\n        setIsFavorite(data.isFavorite)\n      }\n    } catch (error) {\n      console.error('Error checking favorite status:', error)\n    }\n  }\n\n  const toggleFavorite = async (e: React.MouseEvent) => {\n    e.preventDefault()\n    e.stopPropagation()\n\n    if (status !== 'authenticated') {\n      router.push('/auth/login')\n      return\n    }\n\n    if (loading) return\n\n    setLoading(true)\n    \n    try {\n      if (isFavorite) {\n        // إزالة من المفضلة\n        const response = await fetch(`/api/favorites?adId=${adId}`, {\n          method: 'DELETE'\n        })\n        \n        const data = await response.json()\n        \n        if (data.success) {\n          setIsFavorite(false)\n        } else {\n          console.error('Error removing from favorites:', data.error)\n        }\n      } else {\n        // إضافة إلى المفضلة\n        const response = await fetch('/api/favorites', {\n          method: 'POST',\n          headers: {\n            'Content-Type': 'application/json'\n          },\n          body: JSON.stringify({ adId })\n        })\n        \n        const data = await response.json()\n        \n        if (data.success) {\n          setIsFavorite(true)\n        } else {\n          console.error('Error adding to favorites:', data.error)\n        }\n      }\n    } catch (error) {\n      console.error('Error toggling favorite:', error)\n    } finally {\n      setLoading(false)\n    }\n  }\n\n  const getSizeClasses = () => {\n    switch (size) {\n      case 'sm':\n        return 'h-4 w-4'\n      case 'lg':\n        return 'h-8 w-8'\n      default:\n        return 'h-6 w-6'\n    }\n  }\n\n  const getButtonClasses = () => {\n    const baseClasses = 'inline-flex items-center justify-center rounded-lg transition-all duration-200'\n    const sizeClasses = size === 'sm' ? 'p-1' : size === 'lg' ? 'p-3' : 'p-2'\n    \n    if (isFavorite) {\n      return `${baseClasses} ${sizeClasses} bg-red-50 text-red-600 hover:bg-red-100 border border-red-200`\n    } else {\n      return `${baseClasses} ${sizeClasses} bg-gray-50 text-gray-600 hover:bg-gray-100 border border-gray-200 hover:border-red-200 hover:text-red-600`\n    }\n  }\n\n  if (status === 'loading') {\n    return (\n      <div className={`${getButtonClasses()} ${className}`}>\n        <div className={`${getSizeClasses()} animate-pulse bg-gray-300 rounded`}></div>\n        {showText && <span className=\"mr-2 text-sm\">...</span>}\n      </div>\n    )\n  }\n\n  return (\n    <button\n      onClick={toggleFavorite}\n      disabled={loading}\n      className={`${getButtonClasses()} ${className} ${loading ? 'opacity-50 cursor-not-allowed' : ''}`}\n      title={isFavorite ? 'إزالة من المفضلة' : 'إضافة إلى المفضلة'}\n    >\n      {loading ? (\n        <div className={`${getSizeClasses()} animate-spin border-2 border-current border-t-transparent rounded-full`}></div>\n      ) : isFavorite ? (\n        <HeartSolidIcon className={`${getSizeClasses()} text-red-600`} />\n      ) : (\n        <HeartIcon className={getSizeClasses()} />\n      )}\n      \n      {showText && (\n        <span className=\"mr-2 text-sm font-medium\">\n          {isFavorite ? 'مضاف للمفضلة' : 'إضافة للمفضلة'}\n        </span>\n      )}\n    </button>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AANA;;;;;;;AAgBO,SAAS,eAAe,EAC7B,IAAI,EACJ,oBAAoB,KAAK,EACzB,OAAO,IAAI,EACX,WAAW,KAAK,EAChB,YAAY,EAAE,EACM;IACpB,MAAM,EAAE,MAAM,OAAO,EAAE,MAAM,EAAE,GAAG,CAAA,GAAA,8IAAA,CAAA,aAAU,AAAD;IAC3C,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEvC,0CAA0C;IAC1C,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,WAAW,mBAAmB,SAAS,MAAM,IAAI;YACnD;QACF;IACF,GAAG;QAAC;QAAQ;QAAS;KAAK;IAE1B,MAAM,sBAAsB;QAC1B,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,CAAC,0BAA0B,EAAE,MAAM;YAChE,MAAM,OAAO,MAAM,SAAS,IAAI;YAEhC,IAAI,KAAK,OAAO,EAAE;gBAChB,cAAc,KAAK,UAAU;YAC/B;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,mCAAmC;QACnD;IACF;IAEA,MAAM,iBAAiB,OAAO;QAC5B,EAAE,cAAc;QAChB,EAAE,eAAe;QAEjB,IAAI,WAAW,iBAAiB;YAC9B,OAAO,IAAI,CAAC;YACZ;QACF;QAEA,IAAI,SAAS;QAEb,WAAW;QAEX,IAAI;YACF,IAAI,YAAY;gBACd,mBAAmB;gBACnB,MAAM,WAAW,MAAM,MAAM,CAAC,oBAAoB,EAAE,MAAM,EAAE;oBAC1D,QAAQ;gBACV;gBAEA,MAAM,OAAO,MAAM,SAAS,IAAI;gBAEhC,IAAI,KAAK,OAAO,EAAE;oBAChB,cAAc;gBAChB,OAAO;oBACL,QAAQ,KAAK,CAAC,kCAAkC,KAAK,KAAK;gBAC5D;YACF,OAAO;gBACL,oBAAoB;gBACpB,MAAM,WAAW,MAAM,MAAM,kBAAkB;oBAC7C,QAAQ;oBACR,SAAS;wBACP,gBAAgB;oBAClB;oBACA,MAAM,KAAK,SAAS,CAAC;wBAAE;oBAAK;gBAC9B;gBAEA,MAAM,OAAO,MAAM,SAAS,IAAI;gBAEhC,IAAI,KAAK,OAAO,EAAE;oBAChB,cAAc;gBAChB,OAAO;oBACL,QAAQ,KAAK,CAAC,8BAA8B,KAAK,KAAK;gBACxD;YACF;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,4BAA4B;QAC5C,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,iBAAiB;QACrB,OAAQ;YACN,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,MAAM,mBAAmB;QACvB,MAAM,cAAc;QACpB,MAAM,cAAc,SAAS,OAAO,QAAQ,SAAS,OAAO,QAAQ;QAEpE,IAAI,YAAY;YACd,OAAO,GAAG,YAAY,CAAC,EAAE,YAAY,8DAA8D,CAAC;QACtG,OAAO;YACL,OAAO,GAAG,YAAY,CAAC,EAAE,YAAY,0GAA0G,CAAC;QAClJ;IACF;IAEA,IAAI,WAAW,WAAW;QACxB,qBACE,8OAAC;YAAI,WAAW,GAAG,mBAAmB,CAAC,EAAE,WAAW;;8BAClD,8OAAC;oBAAI,WAAW,GAAG,iBAAiB,kCAAkC,CAAC;;;;;;gBACtE,0BAAY,8OAAC;oBAAK,WAAU;8BAAe;;;;;;;;;;;;IAGlD;IAEA,qBACE,8OAAC;QACC,SAAS;QACT,UAAU;QACV,WAAW,GAAG,mBAAmB,CAAC,EAAE,UAAU,CAAC,EAAE,UAAU,kCAAkC,IAAI;QACjG,OAAO,aAAa,qBAAqB;;YAExC,wBACC,8OAAC;gBAAI,WAAW,GAAG,iBAAiB,uEAAuE,CAAC;;;;;uBAC1G,2BACF,8OAAC,+MAAA,CAAA,YAAc;gBAAC,WAAW,GAAG,iBAAiB,aAAa,CAAC;;;;;qCAE7D,8OAAC,iNAAA,CAAA,YAAS;gBAAC,WAAW;;;;;;YAGvB,0BACC,8OAAC;gBAAK,WAAU;0BACb,aAAa,iBAAiB;;;;;;;;;;;;AAKzC", "debugId": null}}, {"offset": {"line": 1162, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/kaliha/khalihatanbaa/src/components/home/<USER>"], "sourcesContent": ["\"use client\";\n\nimport { useState, useEffect } from \"react\";\nimport Link from \"next/link\";\nimport { MapPinIcon, ClockIcon } from \"@heroicons/react/24/outline\";\nimport { Ad } from \"@/types\";\nimport { PlaceholderImage } from \"@/components/ui/PlaceholderImage\";\nimport { FavoriteButton } from \"@/components/ui/FavoriteButton\";\n\n// بيانات تجريبية للإعلانات\nconst sampleAds: Ad[] = [\n  {\n    id: \"1\",\n    title: \"تويوتا كامري 2018 فل كامل\",\n    description: \"سيارة بحالة ممتازة، صيانة دورية، لون أبيض\",\n    price: 45000000,\n    category: \"سيارات\",\n    subCategory: \"تويوتا\",\n    condition: \"مستعمل\",\n    city: \"دمشق\",\n    region: \"المزة\",\n    imageUrls: [\"/placeholder-car.jpg\"],\n    views: 125,\n    isActive: true,\n    isFreeAd: false,\n    isPromoted: true,\n    createdAt: new Date(\"2024-01-15\"),\n    updatedAt: new Date(\"2024-01-15\"),\n    userId: \"user1\",\n    specifications: {\n      year: 2018,\n      mileage: 85000,\n      fuel: \"بنزين\",\n      color: \"أبيض\",\n    },\n  },\n  {\n    id: \"2\",\n    title: \"شقة 3 غرف في المالكي\",\n    description: \"شقة مفروشة بالكامل، إطلالة رائعة، قريبة من الخدمات\",\n    price: 120000000,\n    category: \"عقارات\",\n    subCategory: \"شقة\",\n    condition: \"مستعمل\",\n    city: \"دمشق\",\n    region: \"المالكي\",\n    imageUrls: [\"/placeholder-apartment.jpg\"],\n    views: 89,\n    isActive: true,\n    isFreeAd: true,\n    isPromoted: false,\n    createdAt: new Date(\"2024-01-14\"),\n    updatedAt: new Date(\"2024-01-14\"),\n    userId: \"user2\",\n    specifications: {\n      rooms: 3,\n      bathrooms: 2,\n      area: 120,\n      floor: 3,\n    },\n  },\n  {\n    id: \"3\",\n    title: \"آيفون 14 برو ماكس 256 جيجا\",\n    description: \"جهاز جديد لم يستعمل، مع الكرتونة والشاحن الأصلي\",\n    price: 8500000,\n    category: \"إلكترونيات\",\n    subCategory: \"هاتف ذكي\",\n    condition: \"جديد\",\n    city: \"حلب\",\n    region: \"الفرقان\",\n    imageUrls: [\"/placeholder-phone.jpg\"],\n    views: 234,\n    isActive: true,\n    isFreeAd: false,\n    isPromoted: true,\n    createdAt: new Date(\"2024-01-13\"),\n    updatedAt: new Date(\"2024-01-13\"),\n    userId: \"user3\",\n    specifications: {\n      brand: \"Apple\",\n      model: \"iPhone 14 Pro Max\",\n      storage: \"256GB\",\n      color: \"أسود\",\n    },\n  },\n  {\n    id: \"4\",\n    title: \"طقم غرفة نوم خشب زان\",\n    description: \"طقم كامل: سرير + دولاب + تسريحة، خشب زان طبيعي\",\n    price: 15000000,\n    category: \"أثاث\",\n    subCategory: \"غرفة نوم\",\n    condition: \"مستعمل\",\n    city: \"حمص\",\n    region: \"الوعر\",\n    imageUrls: [\"/placeholder-furniture.jpg\"],\n    views: 67,\n    isActive: true,\n    isFreeAd: true,\n    isPromoted: false,\n    createdAt: new Date(\"2024-01-12\"),\n    updatedAt: new Date(\"2024-01-12\"),\n    userId: \"user4\",\n    specifications: {\n      material: \"خشب زان\",\n      pieces: 3,\n      condition: \"ممتاز\",\n    },\n  },\n];\n\nexport function FeaturedAds() {\n  const [ads, setAds] = useState<Ad[]>([]);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState<string | null>(null);\n\n  useEffect(() => {\n    const fetchFeaturedAds = async () => {\n      try {\n        setLoading(true);\n        const response = await fetch(\"/api/ads?featured=true&limit=8\");\n        const data = await response.json();\n\n        if (data.success && data.data.length > 0) {\n          setAds(data.data);\n        } else {\n          // استخدام البيانات التجريبية كاحتياطي\n          console.log(\"لا توجد إعلانات حقيقية، استخدام البيانات التجريبية\");\n          setAds(sampleAds);\n        }\n      } catch (error) {\n        console.error(\"خطأ في جلب الإعلانات:\", error);\n        setError(\"حدث خطأ في تحميل الإعلانات\");\n        // استخدام البيانات التجريبية في حالة الخطأ\n        setAds(sampleAds);\n      } finally {\n        setLoading(false);\n      }\n    };\n\n    fetchFeaturedAds();\n  }, []);\n\n  const formatPrice = (price: number) => {\n    return new Intl.NumberFormat(\"ar-SY\").format(price);\n  };\n\n  const formatDate = (date: Date) => {\n    const now = new Date();\n    const diffTime = Math.abs(now.getTime() - date.getTime());\n    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));\n\n    if (diffDays === 1) return \"منذ يوم واحد\";\n    if (diffDays < 7) return `منذ ${diffDays} أيام`;\n    if (diffDays < 30) return `منذ ${Math.ceil(diffDays / 7)} أسابيع`;\n    return `منذ ${Math.ceil(diffDays / 30)} شهر`;\n  };\n\n  // حالة التحميل\n  if (loading) {\n    return (\n      <section className=\"py-16 bg-gray-50\">\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n          <div className=\"text-center mb-12\">\n            <h2 className=\"text-3xl lg:text-4xl font-bold text-dark-800 mb-4\">\n              أحدث الإعلانات\n            </h2>\n            <p className=\"text-lg text-dark-600 max-w-2xl mx-auto\">\n              اكتشف أحدث العروض والمنتجات المتاحة في جميع أنحاء سوريا\n            </p>\n          </div>\n\n          {/* مؤشر التحميل */}\n          <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6\">\n            {[...Array(8)].map((_, index) => (\n              <div\n                key={index}\n                className=\"bg-white rounded-2xl shadow-sm border border-gray-200 overflow-hidden animate-pulse\"\n              >\n                <div className=\"h-48 bg-gray-300\"></div>\n                <div className=\"p-4\">\n                  <div className=\"h-4 bg-gray-300 rounded mb-2\"></div>\n                  <div className=\"h-3 bg-gray-300 rounded mb-2 w-3/4\"></div>\n                  <div className=\"h-3 bg-gray-300 rounded mb-3 w-1/2\"></div>\n                  <div className=\"h-5 bg-gray-300 rounded w-1/3\"></div>\n                </div>\n              </div>\n            ))}\n          </div>\n        </div>\n      </section>\n    );\n  }\n\n  return (\n    <section className=\"py-16 bg-gray-50\">\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n        <div className=\"text-center mb-12\">\n          <h2 className=\"text-3xl lg:text-4xl font-bold text-dark-800 mb-4\">\n            أحدث الإعلانات\n          </h2>\n          <p className=\"text-lg text-dark-600 max-w-2xl mx-auto\">\n            اكتشف أحدث العروض والمنتجات المتاحة في جميع أنحاء سوريا\n          </p>\n        </div>\n\n        {/* رسالة خطأ */}\n        {error && (\n          <div className=\"text-center mb-8\">\n            <div className=\"bg-red-50 border border-red-200 rounded-lg p-4 max-w-md mx-auto\">\n              <p className=\"text-red-600\">{error}</p>\n            </div>\n          </div>\n        )}\n\n        <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6\">\n          {ads.map((ad) => (\n            <div\n              key={ad.id}\n              className=\"bg-white rounded-2xl shadow-sm border border-gray-200 overflow-hidden hover:shadow-lg transition-shadow duration-300\"\n            >\n              {/* صورة الإعلان */}\n              <div className=\"relative h-48 bg-gray-200\">\n                <PlaceholderImage\n                  width={300}\n                  height={192}\n                  text={`صورة ${ad.category}`}\n                  className=\"w-full h-full rounded-t-2xl\"\n                />\n\n                {/* شارة الإعلان المميز */}\n                {ad.isPromoted && (\n                  <div className=\"absolute top-3 right-3 bg-primary-500 text-white px-2 py-1 rounded-full text-xs font-semibold\">\n                    مميز\n                  </div>\n                )}\n\n                {/* زر المفضلة */}\n                <div className=\"absolute top-3 left-3\">\n                  <FavoriteButton\n                    adId={ad.id}\n                    size=\"sm\"\n                    className=\"bg-white/80 hover:bg-white\"\n                  />\n                </div>\n              </div>\n\n              {/* محتوى الإعلان */}\n              <div className=\"p-4\">\n                <Link href={`/ads/${ad.id}`}>\n                  <h3 className=\"font-semibold text-dark-800 mb-2 hover:text-primary-500 transition-colors line-clamp-2\">\n                    {ad.title}\n                  </h3>\n                </Link>\n\n                <div className=\"flex items-center text-sm text-dark-500 mb-2\">\n                  <MapPinIcon className=\"h-4 w-4 ml-1\" />\n                  {ad.city} - {ad.region}\n                </div>\n\n                <div className=\"flex items-center text-sm text-dark-500 mb-3\">\n                  <ClockIcon className=\"h-4 w-4 ml-1\" />\n                  {formatDate(ad.createdAt)}\n                </div>\n\n                <div className=\"flex items-center justify-between\">\n                  <div className=\"text-lg font-bold text-primary-500\">\n                    {formatPrice(ad.price)} <span className=\"text-sm\">ل.س</span>\n                  </div>\n                  <div className=\"text-sm text-dark-500\">{ad.views} مشاهدة</div>\n                </div>\n\n                <div className=\"mt-3 flex items-center justify-between\">\n                  <span\n                    className={`px-2 py-1 rounded-full text-xs font-medium ${\n                      ad.condition === \"جديد\"\n                        ? \"bg-green-100 text-green-800\"\n                        : \"bg-blue-100 text-blue-800\"\n                    }`}\n                  >\n                    {ad.condition}\n                  </span>\n\n                  {ad.isFreeAd && (\n                    <span className=\"px-2 py-1 bg-yellow-100 text-yellow-800 rounded-full text-xs font-medium\">\n                      إعلان مجاني\n                    </span>\n                  )}\n                </div>\n              </div>\n            </div>\n          ))}\n        </div>\n\n        {/* رابط عرض المزيد */}\n        <div className=\"text-center mt-12\">\n          <Link\n            href=\"/ads\"\n            className=\"inline-flex items-center px-8 py-3 bg-primary-500 text-white rounded-lg hover:bg-primary-600 transition-colors font-semibold\"\n          >\n            عرض جميع الإعلانات\n            <svg\n              className=\"mr-2 h-5 w-5\"\n              fill=\"none\"\n              stroke=\"currentColor\"\n              viewBox=\"0 0 24 24\"\n            >\n              <path\n                strokeLinecap=\"round\"\n                strokeLinejoin=\"round\"\n                strokeWidth={2}\n                d=\"M15 19l-7-7 7-7\"\n              />\n            </svg>\n          </Link>\n        </div>\n      </div>\n    </section>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAAA;AAEA;AACA;AAPA;;;;;;;AASA,2BAA2B;AAC3B,MAAM,YAAkB;IACtB;QACE,IAAI;QACJ,OAAO;QACP,aAAa;QACb,OAAO;QACP,UAAU;QACV,aAAa;QACb,WAAW;QACX,MAAM;QACN,QAAQ;QACR,WAAW;YAAC;SAAuB;QACnC,OAAO;QACP,UAAU;QACV,UAAU;QACV,YAAY;QACZ,WAAW,IAAI,KAAK;QACpB,WAAW,IAAI,KAAK;QACpB,QAAQ;QACR,gBAAgB;YACd,MAAM;YACN,SAAS;YACT,MAAM;YACN,OAAO;QACT;IACF;IACA;QACE,IAAI;QACJ,OAAO;QACP,aAAa;QACb,OAAO;QACP,UAAU;QACV,aAAa;QACb,WAAW;QACX,MAAM;QACN,QAAQ;QACR,WAAW;YAAC;SAA6B;QACzC,OAAO;QACP,UAAU;QACV,UAAU;QACV,YAAY;QACZ,WAAW,IAAI,KAAK;QACpB,WAAW,IAAI,KAAK;QACpB,QAAQ;QACR,gBAAgB;YACd,OAAO;YACP,WAAW;YACX,MAAM;YACN,OAAO;QACT;IACF;IACA;QACE,IAAI;QACJ,OAAO;QACP,aAAa;QACb,OAAO;QACP,UAAU;QACV,aAAa;QACb,WAAW;QACX,MAAM;QACN,QAAQ;QACR,WAAW;YAAC;SAAyB;QACrC,OAAO;QACP,UAAU;QACV,UAAU;QACV,YAAY;QACZ,WAAW,IAAI,KAAK;QACpB,WAAW,IAAI,KAAK;QACpB,QAAQ;QACR,gBAAgB;YACd,OAAO;YACP,OAAO;YACP,SAAS;YACT,OAAO;QACT;IACF;IACA;QACE,IAAI;QACJ,OAAO;QACP,aAAa;QACb,OAAO;QACP,UAAU;QACV,aAAa;QACb,WAAW;QACX,MAAM;QACN,QAAQ;QACR,WAAW;YAAC;SAA6B;QACzC,OAAO;QACP,UAAU;QACV,UAAU;QACV,YAAY;QACZ,WAAW,IAAI,KAAK;QACpB,WAAW,IAAI,KAAK;QACpB,QAAQ;QACR,gBAAgB;YACd,UAAU;YACV,QAAQ;YACR,WAAW;QACb;IACF;CACD;AAEM,SAAS;IACd,MAAM,CAAC,KAAK,OAAO,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAQ,EAAE;IACvC,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IAElD,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,mBAAmB;YACvB,IAAI;gBACF,WAAW;gBACX,MAAM,WAAW,MAAM,MAAM;gBAC7B,MAAM,OAAO,MAAM,SAAS,IAAI;gBAEhC,IAAI,KAAK,OAAO,IAAI,KAAK,IAAI,CAAC,MAAM,GAAG,GAAG;oBACxC,OAAO,KAAK,IAAI;gBAClB,OAAO;oBACL,sCAAsC;oBACtC,QAAQ,GAAG,CAAC;oBACZ,OAAO;gBACT;YACF,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,yBAAyB;gBACvC,SAAS;gBACT,2CAA2C;gBAC3C,OAAO;YACT,SAAU;gBACR,WAAW;YACb;QACF;QAEA;IACF,GAAG,EAAE;IAEL,MAAM,cAAc,CAAC;QACnB,OAAO,IAAI,KAAK,YAAY,CAAC,SAAS,MAAM,CAAC;IAC/C;IAEA,MAAM,aAAa,CAAC;QAClB,MAAM,MAAM,IAAI;QAChB,MAAM,WAAW,KAAK,GAAG,CAAC,IAAI,OAAO,KAAK,KAAK,OAAO;QACtD,MAAM,WAAW,KAAK,IAAI,CAAC,WAAW,CAAC,OAAO,KAAK,KAAK,EAAE;QAE1D,IAAI,aAAa,GAAG,OAAO;QAC3B,IAAI,WAAW,GAAG,OAAO,CAAC,IAAI,EAAE,SAAS,KAAK,CAAC;QAC/C,IAAI,WAAW,IAAI,OAAO,CAAC,IAAI,EAAE,KAAK,IAAI,CAAC,WAAW,GAAG,OAAO,CAAC;QACjE,OAAO,CAAC,IAAI,EAAE,KAAK,IAAI,CAAC,WAAW,IAAI,IAAI,CAAC;IAC9C;IAEA,eAAe;IACf,IAAI,SAAS;QACX,qBACE,8OAAC;YAAQ,WAAU;sBACjB,cAAA,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;0CAAoD;;;;;;0CAGlE,8OAAC;gCAAE,WAAU;0CAA0C;;;;;;;;;;;;kCAMzD,8OAAC;wBAAI,WAAU;kCACZ;+BAAI,MAAM;yBAAG,CAAC,GAAG,CAAC,CAAC,GAAG,sBACrB,8OAAC;gCAEC,WAAU;;kDAEV,8OAAC;wCAAI,WAAU;;;;;;kDACf,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;;;;;;0DACf,8OAAC;gDAAI,WAAU;;;;;;0DACf,8OAAC;gDAAI,WAAU;;;;;;0DACf,8OAAC;gDAAI,WAAU;;;;;;;;;;;;;+BARZ;;;;;;;;;;;;;;;;;;;;;IAgBnB;IAEA,qBACE,8OAAC;QAAQ,WAAU;kBACjB,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAG,WAAU;sCAAoD;;;;;;sCAGlE,8OAAC;4BAAE,WAAU;sCAA0C;;;;;;;;;;;;gBAMxD,uBACC,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAE,WAAU;sCAAgB;;;;;;;;;;;;;;;;8BAKnC,8OAAC;oBAAI,WAAU;8BACZ,IAAI,GAAG,CAAC,CAAC,mBACR,8OAAC;4BAEC,WAAU;;8CAGV,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,4IAAA,CAAA,mBAAgB;4CACf,OAAO;4CACP,QAAQ;4CACR,MAAM,CAAC,KAAK,EAAE,GAAG,QAAQ,EAAE;4CAC3B,WAAU;;;;;;wCAIX,GAAG,UAAU,kBACZ,8OAAC;4CAAI,WAAU;sDAAgG;;;;;;sDAMjH,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC,0IAAA,CAAA,iBAAc;gDACb,MAAM,GAAG,EAAE;gDACX,MAAK;gDACL,WAAU;;;;;;;;;;;;;;;;;8CAMhB,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,4JAAA,CAAA,UAAI;4CAAC,MAAM,CAAC,KAAK,EAAE,GAAG,EAAE,EAAE;sDACzB,cAAA,8OAAC;gDAAG,WAAU;0DACX,GAAG,KAAK;;;;;;;;;;;sDAIb,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,mNAAA,CAAA,aAAU;oDAAC,WAAU;;;;;;gDACrB,GAAG,IAAI;gDAAC;gDAAI,GAAG,MAAM;;;;;;;sDAGxB,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,iNAAA,CAAA,YAAS;oDAAC,WAAU;;;;;;gDACpB,WAAW,GAAG,SAAS;;;;;;;sDAG1B,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;;wDACZ,YAAY,GAAG,KAAK;wDAAE;sEAAC,8OAAC;4DAAK,WAAU;sEAAU;;;;;;;;;;;;8DAEpD,8OAAC;oDAAI,WAAU;;wDAAyB,GAAG,KAAK;wDAAC;;;;;;;;;;;;;sDAGnD,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDACC,WAAW,CAAC,2CAA2C,EACrD,GAAG,SAAS,KAAK,SACb,gCACA,6BACJ;8DAED,GAAG,SAAS;;;;;;gDAGd,GAAG,QAAQ,kBACV,8OAAC;oDAAK,WAAU;8DAA2E;;;;;;;;;;;;;;;;;;;2BAlE5F,GAAG,EAAE;;;;;;;;;;8BA6EhB,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC,4JAAA,CAAA,UAAI;wBACH,MAAK;wBACL,WAAU;;4BACX;0CAEC,8OAAC;gCACC,WAAU;gCACV,MAAK;gCACL,QAAO;gCACP,SAAQ;0CAER,cAAA,8OAAC;oCACC,eAAc;oCACd,gBAAe;oCACf,aAAa;oCACb,GAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQlB", "debugId": null}}]}