{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 148, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/kaliha/khalihatan<PERSON>a/src/lib/prisma.ts"], "sourcesContent": ["import { PrismaClient } from '@prisma/client'\n\nconst globalForPrisma = globalThis as unknown as {\n  prisma: PrismaClient | undefined\n}\n\nexport const prisma =\n  globalForPrisma.prisma ??\n  new PrismaClient({\n    log: ['query'],\n  })\n\nif (process.env.NODE_ENV !== 'production') globalForPrisma.prisma = prisma\n"], "names": [], "mappings": ";;;AAAA;;AAEA,MAAM,kBAAkB;AAIjB,MAAM,SACX,gBAAgB,MAAM,IACtB,IAAI,6HAAA,CAAA,eAAY,CAAC;IACf,KAAK;QAAC;KAAQ;AAChB;AAEF,wCAA2C,gBAAgB,MAAM,GAAG", "debugId": null}}, {"offset": {"line": 166, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/kaliha/khalihatanbaa/src/lib/auth.ts"], "sourcesContent": ["import { NextAuthOptions } from \"next-auth\";\nimport Credentials<PERSON>rovider from \"next-auth/providers/credentials\";\nimport bcrypt from \"bcryptjs\";\nimport { prisma } from \"./prisma\";\n\nexport const authOptions: NextAuthOptions = {\n  providers: [\n    CredentialsProvider({\n      name: \"credentials\",\n      credentials: {\n        emailOrPhone: {\n          label: \"البريد الإلكتروني أو رقم الهاتف\",\n          type: \"text\",\n        },\n        password: { label: \"كلمة المرور\", type: \"password\" },\n      },\n      async authorize(credentials) {\n        if (!credentials?.emailOrPhone || !credentials?.password) {\n          return null;\n        }\n\n        // البحث عن المستخدم بالبريد الإلكتروني أو رقم الهاتف\n        const user = await prisma.user.findFirst({\n          where: {\n            OR: [\n              { email: credentials.emailOrPhone },\n              { phone: credentials.emailOrPhone },\n            ],\n            isActive: true,\n          },\n        });\n\n        if (!user) {\n          return null;\n        }\n\n        // التحقق من كلمة المرور\n        const isPasswordValid = await bcrypt.compare(\n          credentials.password,\n          user.passwordHash\n        );\n\n        if (!isPasswordValid) {\n          return null;\n        }\n\n        return {\n          id: user.id,\n          name: user.name,\n          email: user.email,\n          phone: user.phone,\n          role: user.role,\n          avatar: user.avatar || undefined,\n        };\n      },\n    }),\n  ],\n  session: {\n    strategy: \"jwt\",\n  },\n  callbacks: {\n    async jwt({ token, user }) {\n      if (user) {\n        token.role = user.role;\n        token.phone = user.phone;\n        token.avatar = user.avatar;\n      }\n      return token;\n    },\n    async session({ session, token }) {\n      if (token) {\n        session.user.id = token.sub!;\n        session.user.role = token.role as string;\n        session.user.phone = token.phone as string;\n        session.user.avatar = token.avatar as string;\n      }\n      return session;\n    },\n  },\n  pages: {\n    signIn: \"/auth/login\",\n    // signUp: \"/auth/register\", // غير مدعوم في NextAuth\n  },\n};\n"], "names": [], "mappings": ";;;AACA;AACA;AACA;;;;AAEO,MAAM,cAA+B;IAC1C,WAAW;QACT,CAAA,GAAA,0JAAA,CAAA,UAAmB,AAAD,EAAE;YAClB,MAAM;YACN,aAAa;gBACX,cAAc;oBACZ,OAAO;oBACP,MAAM;gBACR;gBACA,UAAU;oBAAE,OAAO;oBAAe,MAAM;gBAAW;YACrD;YACA,MAAM,WAAU,WAAW;gBACzB,IAAI,CAAC,aAAa,gBAAgB,CAAC,aAAa,UAAU;oBACxD,OAAO;gBACT;gBAEA,qDAAqD;gBACrD,MAAM,OAAO,MAAM,sHAAA,CAAA,SAAM,CAAC,IAAI,CAAC,SAAS,CAAC;oBACvC,OAAO;wBACL,IAAI;4BACF;gCAAE,OAAO,YAAY,YAAY;4BAAC;4BAClC;gCAAE,OAAO,YAAY,YAAY;4BAAC;yBACnC;wBACD,UAAU;oBACZ;gBACF;gBAEA,IAAI,CAAC,MAAM;oBACT,OAAO;gBACT;gBAEA,wBAAwB;gBACxB,MAAM,kBAAkB,MAAM,mIAAA,CAAA,UAAM,CAAC,OAAO,CAC1C,YAAY,QAAQ,EACpB,KAAK,YAAY;gBAGnB,IAAI,CAAC,iBAAiB;oBACpB,OAAO;gBACT;gBAEA,OAAO;oBACL,IAAI,KAAK,EAAE;oBACX,MAAM,KAAK,IAAI;oBACf,OAAO,KAAK,KAAK;oBACjB,OAAO,KAAK,KAAK;oBACjB,MAAM,KAAK,IAAI;oBACf,QAAQ,KAAK,MAAM,IAAI;gBACzB;YACF;QACF;KACD;IACD,SAAS;QACP,UAAU;IACZ;IACA,WAAW;QACT,MAAM,KAAI,EAAE,KAAK,EAAE,IAAI,EAAE;YACvB,IAAI,MAAM;gBACR,MAAM,IAAI,GAAG,KAAK,IAAI;gBACtB,MAAM,KAAK,GAAG,KAAK,KAAK;gBACxB,MAAM,MAAM,GAAG,KAAK,MAAM;YAC5B;YACA,OAAO;QACT;QACA,MAAM,SAAQ,EAAE,OAAO,EAAE,KAAK,EAAE;YAC9B,IAAI,OAAO;gBACT,QAAQ,IAAI,CAAC,EAAE,GAAG,MAAM,GAAG;gBAC3B,QAAQ,IAAI,CAAC,IAAI,GAAG,MAAM,IAAI;gBAC9B,QAAQ,IAAI,CAAC,KAAK,GAAG,MAAM,KAAK;gBAChC,QAAQ,IAAI,CAAC,MAAM,GAAG,MAAM,MAAM;YACpC;YACA,OAAO;QACT;IACF;IACA,OAAO;QACL,QAAQ;IAEV;AACF", "debugId": null}}, {"offset": {"line": 258, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/kaliha/khalihatanba<PERSON>/src/app/api/messages/temp/route.ts"], "sourcesContent": ["import { NextRequest, NextResponse } from \"next/server\";\nimport { getServerSession } from \"next-auth\";\nimport { authOptions } from \"@/lib/auth\";\n\n// API مؤقت للاختبار مع بيانات وهمية\nexport async function GET(request: NextRequest) {\n  try {\n    const session = await getServerSession(authOptions);\n    if (!session?.user?.id) {\n      return NextResponse.json(\n        { success: false, error: \"يجب تسجيل الدخول أولاً\" },\n        { status: 401 }\n      );\n    }\n\n    const { searchParams } = new URL(request.url);\n    const conversationWith = searchParams.get(\"with\");\n\n    if (conversationWith) {\n      // إرجاع رسائل وهمية للمحادثة\n      const mockMessages = [\n        {\n          id: \"1\",\n          content: \"مرحبا، كيف حالك؟\",\n          isRead: true,\n          createdAt: new Date(Date.now() - 60000).toISOString(),\n          fromId: conversationWith,\n          toId: session.user.id,\n          adId: null,\n          from: {\n            id: conversationWith,\n            name: \"أحمد محمد\",\n            avatar: null,\n          },\n          to: {\n            id: session.user.id,\n            name: session.user.name || \"أنت\",\n            avatar: session.user.avatar || null,\n          },\n          ad: null,\n        },\n        {\n          id: \"2\",\n          content: \"أهلاً وسهلاً، بخير والحمد لله\",\n          isRead: true,\n          createdAt: new Date(Date.now() - 30000).toISOString(),\n          fromId: session.user.id,\n          toId: conversationWith,\n          adId: null,\n          from: {\n            id: session.user.id,\n            name: session.user.name || \"أنت\",\n            avatar: session.user.avatar || null,\n          },\n          to: {\n            id: conversationWith,\n            name: \"أحمد محمد\",\n            avatar: null,\n          },\n          ad: null,\n        },\n      ];\n\n      return NextResponse.json({\n        success: true,\n        data: mockMessages,\n        hasMore: false,\n        lastMessageId: \"2\",\n      });\n    } else {\n      // إرجاع قائمة محادثات وهمية\n      const mockConversations = [\n        {\n          id: \"1\",\n          content: \"أهلاً وسهلاً، بخير والحمد لله\",\n          createdAt: new Date(Date.now() - 30000).toISOString(),\n          adId: null,\n          otherUser: {\n            id: \"user1\",\n            name: \"أحمد محمد\",\n            avatar: null,\n          },\n          ad: null,\n          unreadCount: 0,\n          isOnline: true,\n        },\n        {\n          id: \"2\",\n          content: \"شكراً لك على الاستفسار\",\n          createdAt: new Date(Date.now() - 120000).toISOString(),\n          adId: \"ad1\",\n          otherUser: {\n            id: \"user2\",\n            name: \"فاطمة أحمد\",\n            avatar: null,\n          },\n          ad: {\n            id: \"ad1\",\n            title: \"هاتف ذكي مستعمل\",\n            price: 500,\n            imageUrls: [],\n          },\n          unreadCount: 2,\n          isOnline: false,\n        },\n      ];\n\n      return NextResponse.json({\n        success: true,\n        data: mockConversations,\n      });\n    }\n  } catch (error) {\n    console.error(\"Error in temp messages API:\", error);\n    return NextResponse.json(\n      { success: false, error: \"حدث خطأ في الخادم\" },\n      { status: 500 }\n    );\n  }\n}\n\nexport async function POST(request: NextRequest) {\n  try {\n    const session = await getServerSession(authOptions);\n    if (!session?.user?.id) {\n      return NextResponse.json(\n        { success: false, error: \"يجب تسجيل الدخول أولاً\" },\n        { status: 401 }\n      );\n    }\n\n    const body = await request.json();\n    const { toId, content, adId } = body;\n\n    // إنشاء رسالة وهمية\n    const mockMessage = {\n      id: `temp-${Date.now()}`,\n      content,\n      isRead: false,\n      createdAt: new Date().toISOString(),\n      fromId: session.user.id,\n      toId,\n      adId: adId || null,\n      from: {\n        id: session.user.id,\n        name: session.user.name || \"أنت\",\n        avatar: session.user.avatar || null,\n      },\n      to: {\n        id: toId,\n        name: \"مستخدم آخر\",\n        avatar: null,\n      },\n      ad: adId ? {\n        id: adId,\n        title: \"إعلان تجريبي\",\n        price: 100,\n        imageUrls: [],\n      } : null,\n    };\n\n    return NextResponse.json({\n      success: true,\n      data: mockMessage,\n      message: \"تم إرسال الرسالة بنجاح\",\n    });\n  } catch (error) {\n    console.error(\"Error sending temp message:\", error);\n    return NextResponse.json(\n      { success: false, error: \"حدث خطأ في إرسال الرسالة\" },\n      { status: 500 }\n    );\n  }\n}\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;;;;AAGO,eAAe,IAAI,OAAoB;IAC5C,IAAI;QACF,MAAM,UAAU,MAAM,CAAA,GAAA,uIAAA,CAAA,mBAAgB,AAAD,EAAE,oHAAA,CAAA,cAAW;QAClD,IAAI,CAAC,SAAS,MAAM,IAAI;YACtB,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,SAAS;gBAAO,OAAO;YAAyB,GAClD;gBAAE,QAAQ;YAAI;QAElB;QAEA,MAAM,EAAE,YAAY,EAAE,GAAG,IAAI,IAAI,QAAQ,GAAG;QAC5C,MAAM,mBAAmB,aAAa,GAAG,CAAC;QAE1C,IAAI,kBAAkB;YACpB,6BAA6B;YAC7B,MAAM,eAAe;gBACnB;oBACE,IAAI;oBACJ,SAAS;oBACT,QAAQ;oBACR,WAAW,IAAI,KAAK,KAAK,GAAG,KAAK,OAAO,WAAW;oBACnD,QAAQ;oBACR,MAAM,QAAQ,IAAI,CAAC,EAAE;oBACrB,MAAM;oBACN,MAAM;wBACJ,IAAI;wBACJ,MAAM;wBACN,QAAQ;oBACV;oBACA,IAAI;wBACF,IAAI,QAAQ,IAAI,CAAC,EAAE;wBACnB,MAAM,QAAQ,IAAI,CAAC,IAAI,IAAI;wBAC3B,QAAQ,QAAQ,IAAI,CAAC,MAAM,IAAI;oBACjC;oBACA,IAAI;gBACN;gBACA;oBACE,IAAI;oBACJ,SAAS;oBACT,QAAQ;oBACR,WAAW,IAAI,KAAK,KAAK,GAAG,KAAK,OAAO,WAAW;oBACnD,QAAQ,QAAQ,IAAI,CAAC,EAAE;oBACvB,MAAM;oBACN,MAAM;oBACN,MAAM;wBACJ,IAAI,QAAQ,IAAI,CAAC,EAAE;wBACnB,MAAM,QAAQ,IAAI,CAAC,IAAI,IAAI;wBAC3B,QAAQ,QAAQ,IAAI,CAAC,MAAM,IAAI;oBACjC;oBACA,IAAI;wBACF,IAAI;wBACJ,MAAM;wBACN,QAAQ;oBACV;oBACA,IAAI;gBACN;aACD;YAED,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;gBACvB,SAAS;gBACT,MAAM;gBACN,SAAS;gBACT,eAAe;YACjB;QACF,OAAO;YACL,4BAA4B;YAC5B,MAAM,oBAAoB;gBACxB;oBACE,IAAI;oBACJ,SAAS;oBACT,WAAW,IAAI,KAAK,KAAK,GAAG,KAAK,OAAO,WAAW;oBACnD,MAAM;oBACN,WAAW;wBACT,IAAI;wBACJ,MAAM;wBACN,QAAQ;oBACV;oBACA,IAAI;oBACJ,aAAa;oBACb,UAAU;gBACZ;gBACA;oBACE,IAAI;oBACJ,SAAS;oBACT,WAAW,IAAI,KAAK,KAAK,GAAG,KAAK,QAAQ,WAAW;oBACpD,MAAM;oBACN,WAAW;wBACT,IAAI;wBACJ,MAAM;wBACN,QAAQ;oBACV;oBACA,IAAI;wBACF,IAAI;wBACJ,OAAO;wBACP,OAAO;wBACP,WAAW,EAAE;oBACf;oBACA,aAAa;oBACb,UAAU;gBACZ;aACD;YAED,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;gBACvB,SAAS;gBACT,MAAM;YACR;QACF;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,+BAA+B;QAC7C,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YAAE,SAAS;YAAO,OAAO;QAAoB,GAC7C;YAAE,QAAQ;QAAI;IAElB;AACF;AAEO,eAAe,KAAK,OAAoB;IAC7C,IAAI;QACF,MAAM,UAAU,MAAM,CAAA,GAAA,uIAAA,CAAA,mBAAgB,AAAD,EAAE,oHAAA,CAAA,cAAW;QAClD,IAAI,CAAC,SAAS,MAAM,IAAI;YACtB,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,SAAS;gBAAO,OAAO;YAAyB,GAClD;gBAAE,QAAQ;YAAI;QAElB;QAEA,MAAM,OAAO,MAAM,QAAQ,IAAI;QAC/B,MAAM,EAAE,IAAI,EAAE,OAAO,EAAE,IAAI,EAAE,GAAG;QAEhC,oBAAoB;QACpB,MAAM,cAAc;YAClB,IAAI,CAAC,KAAK,EAAE,KAAK,GAAG,IAAI;YACxB;YACA,QAAQ;YACR,WAAW,IAAI,OAAO,WAAW;YACjC,QAAQ,QAAQ,IAAI,CAAC,EAAE;YACvB;YACA,MAAM,QAAQ;YACd,MAAM;gBACJ,IAAI,QAAQ,IAAI,CAAC,EAAE;gBACnB,MAAM,QAAQ,IAAI,CAAC,IAAI,IAAI;gBAC3B,QAAQ,QAAQ,IAAI,CAAC,MAAM,IAAI;YACjC;YACA,IAAI;gBACF,IAAI;gBACJ,MAAM;gBACN,QAAQ;YACV;YACA,IAAI,OAAO;gBACT,IAAI;gBACJ,OAAO;gBACP,OAAO;gBACP,WAAW,EAAE;YACf,IAAI;QACN;QAEA,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YACvB,SAAS;YACT,MAAM;YACN,SAAS;QACX;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,+BAA+B;QAC7C,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YAAE,SAAS;YAAO,OAAO;QAA2B,GACpD;YAAE,QAAQ;QAAI;IAElB;AACF", "debugId": null}}]}