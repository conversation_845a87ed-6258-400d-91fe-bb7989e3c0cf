{"version": 3, "sources": [], "sections": [{"offset": {"line": 31, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/kaliha/khalihatanbaa/src/components/layout/Header.tsx"], "sourcesContent": ["\"use client\";\n\nimport { useState } from \"react\";\nimport Link from \"next/link\";\nimport { useSession, signOut } from \"next-auth/react\";\nimport {\n  MagnifyingGlassIcon,\n  PlusIcon,\n  ChatBubbleLeftRightIcon,\n  HeartIcon,\n  UserIcon,\n  Bars3Icon,\n  XMarkIcon,\n  ChartBarIcon,\n} from \"@heroicons/react/24/outline\";\n\nexport function Header() {\n  const { data: session } = useSession();\n  const [isMenuOpen, setIsMenuOpen] = useState(false);\n  const [searchQuery, setSearchQuery] = useState(\"\");\n\n  const handleSearch = (e: React.FormEvent) => {\n    e.preventDefault();\n    if (searchQuery.trim()) {\n      window.location.href = `/search?q=${encodeURIComponent(searchQuery)}`;\n    }\n  };\n\n  return (\n    <header className=\"bg-white shadow-sm border-b border-gray-200 sticky top-0 z-50\">\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n        <div className=\"flex items-center justify-between h-16\">\n          {/* الشعار */}\n          <Link\n            href=\"/\"\n            className=\"flex items-center space-x-2 space-x-reverse\"\n          >\n            <div className=\"w-10 h-10 bg-primary-500 rounded-lg flex items-center justify-center\">\n              <span className=\"text-white font-bold text-lg\">خ</span>\n            </div>\n            <div className=\"hidden sm:block\">\n              <h1 className=\"text-xl font-bold text-dark-800\">\n                خَلّيها تنْباع\n              </h1>\n              <p className=\"text-xs text-dark-500\">منصة البيع والشراء</p>\n            </div>\n          </Link>\n\n          {/* شريط البحث */}\n          <div className=\"flex-1 max-w-lg mx-4\">\n            <form onSubmit={handleSearch} className=\"relative\">\n              <input\n                type=\"text\"\n                placeholder=\"ابحث عن أي شيء...\"\n                value={searchQuery}\n                onChange={(e) => setSearchQuery(e.target.value)}\n                className=\"w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent\"\n              />\n              <MagnifyingGlassIcon className=\"absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400\" />\n            </form>\n          </div>\n\n          {/* أزرار التنقل */}\n          <div className=\"flex items-center space-x-4 space-x-reverse\">\n            {session ? (\n              <>\n                {/* نشر إعلان */}\n                <Link\n                  href=\"/ads/new\"\n                  className=\"bg-primary-500 text-white px-4 py-2 rounded-lg hover:bg-primary-600 transition-colors flex items-center space-x-2 space-x-reverse\"\n                >\n                  <PlusIcon className=\"h-5 w-5\" />\n                  <span className=\"hidden sm:inline\">نشر إعلان</span>\n                </Link>\n\n                {/* الرسائل */}\n                <Link\n                  href=\"/messages\"\n                  className=\"p-2 text-dark-600 hover:text-primary-500 transition-colors relative\"\n                >\n                  <ChatBubbleLeftRightIcon className=\"h-6 w-6\" />\n                  {/* نقطة الإشعارات */}\n                  <span className=\"absolute -top-1 -right-1 h-3 w-3 bg-primary-500 rounded-full\"></span>\n                </Link>\n\n                {/* المفضلة */}\n                <Link\n                  href=\"/favorites\"\n                  className=\"p-2 text-dark-600 hover:text-primary-500 transition-colors\"\n                >\n                  <HeartIcon className=\"h-6 w-6\" />\n                </Link>\n\n                {/* لوحة التحكم */}\n                <Link\n                  href=\"/dashboard\"\n                  className=\"p-2 text-dark-600 hover:text-primary-500 transition-colors\"\n                >\n                  <ChartBarIcon className=\"h-6 w-6\" />\n                </Link>\n\n                {/* قائمة المستخدم */}\n                <div className=\"relative\">\n                  <button\n                    onClick={() => setIsMenuOpen(!isMenuOpen)}\n                    className=\"flex items-center space-x-2 space-x-reverse p-2 text-dark-600 hover:text-primary-500 transition-colors\"\n                  >\n                    <UserIcon className=\"h-6 w-6\" />\n                    <span className=\"hidden sm:inline\">\n                      {session.user.name}\n                    </span>\n                  </button>\n\n                  {isMenuOpen && (\n                    <div className=\"absolute right-0 mt-2 w-48 bg-white rounded-lg shadow-lg border border-gray-200 py-2\">\n                      <Link\n                        href=\"/profile\"\n                        className=\"block px-4 py-2 text-dark-700 hover:bg-gray-50\"\n                      >\n                        الملف الشخصي\n                      </Link>\n                      <Link\n                        href=\"/profile/ads\"\n                        className=\"block px-4 py-2 text-dark-700 hover:bg-gray-50\"\n                      >\n                        إعلاناتي\n                      </Link>\n                      <Link\n                        href=\"/profile/settings\"\n                        className=\"block px-4 py-2 text-dark-700 hover:bg-gray-50\"\n                      >\n                        الإعدادات\n                      </Link>\n                      <hr className=\"my-2\" />\n                      <button\n                        onClick={() => signOut()}\n                        className=\"block w-full text-right px-4 py-2 text-red-600 hover:bg-gray-50\"\n                      >\n                        تسجيل الخروج\n                      </button>\n                    </div>\n                  )}\n                </div>\n              </>\n            ) : (\n              <>\n                <Link\n                  href=\"/auth/login\"\n                  className=\"text-dark-600 hover:text-primary-500 transition-colors\"\n                >\n                  تسجيل الدخول\n                </Link>\n                <Link\n                  href=\"/auth/register\"\n                  className=\"bg-primary-500 text-white px-4 py-2 rounded-lg hover:bg-primary-600 transition-colors\"\n                >\n                  إنشاء حساب\n                </Link>\n              </>\n            )}\n\n            {/* قائمة الموبايل */}\n            <button\n              onClick={() => setIsMenuOpen(!isMenuOpen)}\n              className=\"sm:hidden p-2 text-dark-600\"\n            >\n              {isMenuOpen ? (\n                <XMarkIcon className=\"h-6 w-6\" />\n              ) : (\n                <Bars3Icon className=\"h-6 w-6\" />\n              )}\n            </button>\n          </div>\n        </div>\n\n        {/* قائمة الموبايل */}\n        {isMenuOpen && (\n          <div className=\"sm:hidden border-t border-gray-200 py-4\">\n            <div className=\"space-y-2\">\n              <Link\n                href=\"/ads\"\n                className=\"block px-4 py-2 text-dark-700 hover:bg-gray-50 rounded\"\n              >\n                جميع الإعلانات\n              </Link>\n              <Link\n                href=\"/categories\"\n                className=\"block px-4 py-2 text-dark-700 hover:bg-gray-50 rounded\"\n              >\n                جميع الفئات\n              </Link>\n              {session && (\n                <>\n                  <Link\n                    href=\"/ads/new\"\n                    className=\"block px-4 py-2 text-primary-600 hover:bg-gray-50 rounded\"\n                  >\n                    نشر إعلان جديد\n                  </Link>\n                  <Link\n                    href=\"/messages\"\n                    className=\"block px-4 py-2 text-dark-700 hover:bg-gray-50 rounded\"\n                  >\n                    الرسائل\n                  </Link>\n                  <Link\n                    href=\"/favorites\"\n                    className=\"block px-4 py-2 text-dark-700 hover:bg-gray-50 rounded\"\n                  >\n                    المفضلة\n                  </Link>\n                  <Link\n                    href=\"/dashboard\"\n                    className=\"block px-4 py-2 text-dark-700 hover:bg-gray-50 rounded\"\n                  >\n                    لوحة التحكم\n                  </Link>\n                </>\n              )}\n            </div>\n          </div>\n        )}\n      </div>\n    </header>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AALA;;;;;;AAgBO,SAAS;IACd,MAAM,EAAE,MAAM,OAAO,EAAE,GAAG,CAAA,GAAA,8IAAA,CAAA,aAAU,AAAD;IACnC,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE/C,MAAM,eAAe,CAAC;QACpB,EAAE,cAAc;QAChB,IAAI,YAAY,IAAI,IAAI;YACtB,OAAO,QAAQ,CAAC,IAAI,GAAG,CAAC,UAAU,EAAE,mBAAmB,cAAc;QACvE;IACF;IAEA,qBACE,8OAAC;QAAO,WAAU;kBAChB,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC,4JAAA,CAAA,UAAI;4BACH,MAAK;4BACL,WAAU;;8CAEV,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCAAK,WAAU;kDAA+B;;;;;;;;;;;8CAEjD,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAG,WAAU;sDAAkC;;;;;;sDAGhD,8OAAC;4CAAE,WAAU;sDAAwB;;;;;;;;;;;;;;;;;;sCAKzC,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAK,UAAU;gCAAc,WAAU;;kDACtC,8OAAC;wCACC,MAAK;wCACL,aAAY;wCACZ,OAAO;wCACP,UAAU,CAAC,IAAM,eAAe,EAAE,MAAM,CAAC,KAAK;wCAC9C,WAAU;;;;;;kDAEZ,8OAAC,qOAAA,CAAA,sBAAmB;wCAAC,WAAU;;;;;;;;;;;;;;;;;sCAKnC,8OAAC;4BAAI,WAAU;;gCACZ,wBACC;;sDAEE,8OAAC,4JAAA,CAAA,UAAI;4CACH,MAAK;4CACL,WAAU;;8DAEV,8OAAC,+MAAA,CAAA,WAAQ;oDAAC,WAAU;;;;;;8DACpB,8OAAC;oDAAK,WAAU;8DAAmB;;;;;;;;;;;;sDAIrC,8OAAC,4JAAA,CAAA,UAAI;4CACH,MAAK;4CACL,WAAU;;8DAEV,8OAAC,6OAAA,CAAA,0BAAuB;oDAAC,WAAU;;;;;;8DAEnC,8OAAC;oDAAK,WAAU;;;;;;;;;;;;sDAIlB,8OAAC,4JAAA,CAAA,UAAI;4CACH,MAAK;4CACL,WAAU;sDAEV,cAAA,8OAAC,iNAAA,CAAA,YAAS;gDAAC,WAAU;;;;;;;;;;;sDAIvB,8OAAC,4JAAA,CAAA,UAAI;4CACH,MAAK;4CACL,WAAU;sDAEV,cAAA,8OAAC,uNAAA,CAAA,eAAY;gDAAC,WAAU;;;;;;;;;;;sDAI1B,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDACC,SAAS,IAAM,cAAc,CAAC;oDAC9B,WAAU;;sEAEV,8OAAC,+MAAA,CAAA,WAAQ;4DAAC,WAAU;;;;;;sEACpB,8OAAC;4DAAK,WAAU;sEACb,QAAQ,IAAI,CAAC,IAAI;;;;;;;;;;;;gDAIrB,4BACC,8OAAC;oDAAI,WAAU;;sEACb,8OAAC,4JAAA,CAAA,UAAI;4DACH,MAAK;4DACL,WAAU;sEACX;;;;;;sEAGD,8OAAC,4JAAA,CAAA,UAAI;4DACH,MAAK;4DACL,WAAU;sEACX;;;;;;sEAGD,8OAAC,4JAAA,CAAA,UAAI;4DACH,MAAK;4DACL,WAAU;sEACX;;;;;;sEAGD,8OAAC;4DAAG,WAAU;;;;;;sEACd,8OAAC;4DACC,SAAS,IAAM,CAAA,GAAA,8IAAA,CAAA,UAAO,AAAD;4DACrB,WAAU;sEACX;;;;;;;;;;;;;;;;;;;iEAQT;;sDACE,8OAAC,4JAAA,CAAA,UAAI;4CACH,MAAK;4CACL,WAAU;sDACX;;;;;;sDAGD,8OAAC,4JAAA,CAAA,UAAI;4CACH,MAAK;4CACL,WAAU;sDACX;;;;;;;;8CAOL,8OAAC;oCACC,SAAS,IAAM,cAAc,CAAC;oCAC9B,WAAU;8CAET,2BACC,8OAAC,iNAAA,CAAA,YAAS;wCAAC,WAAU;;;;;6DAErB,8OAAC,iNAAA,CAAA,YAAS;wCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;gBAO5B,4BACC,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,4JAAA,CAAA,UAAI;gCACH,MAAK;gCACL,WAAU;0CACX;;;;;;0CAGD,8OAAC,4JAAA,CAAA,UAAI;gCACH,MAAK;gCACL,WAAU;0CACX;;;;;;4BAGA,yBACC;;kDACE,8OAAC,4JAAA,CAAA,UAAI;wCACH,MAAK;wCACL,WAAU;kDACX;;;;;;kDAGD,8OAAC,4JAAA,CAAA,UAAI;wCACH,MAAK;wCACL,WAAU;kDACX;;;;;;kDAGD,8OAAC,4JAAA,CAAA,UAAI;wCACH,MAAK;wCACL,WAAU;kDACX;;;;;;kDAGD,8OAAC,4JAAA,CAAA,UAAI;wCACH,MAAK;wCACL,WAAU;kDACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAWnB", "debugId": null}}, {"offset": {"line": 473, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/kaliha/khalihatanbaa/src/components/layout/Footer.tsx"], "sourcesContent": ["import Link from 'next/link'\n\nexport function Footer() {\n  return (\n    <footer className=\"bg-dark-800 text-white\">\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12\">\n        <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8\">\n          {/* معلومات الموقع */}\n          <div className=\"lg:col-span-2\">\n            <div className=\"flex items-center space-x-2 space-x-reverse mb-4\">\n              <div className=\"w-10 h-10 bg-primary-500 rounded-lg flex items-center justify-center\">\n                <span className=\"text-white font-bold text-lg\">خ</span>\n              </div>\n              <div>\n                <h3 className=\"text-xl font-bold\">خَلّيها تنْباع</h3>\n                <p className=\"text-sm text-gray-400\">منصة البيع والشراء</p>\n              </div>\n            </div>\n            <p className=\"text-gray-300 mb-6 max-w-md\">\n              منصة إلكترونية سورية لبيع وشراء المنتجات المستعملة والجديدة. \n              نوفر بيئة آمنة وسهلة للتجارة الإلكترونية في سوريا.\n            </p>\n            <div className=\"flex space-x-4 space-x-reverse\">\n              <a href=\"#\" className=\"text-gray-400 hover:text-white transition-colors\">\n                <span className=\"sr-only\">فيسبوك</span>\n                <svg className=\"h-6 w-6\" fill=\"currentColor\" viewBox=\"0 0 24 24\">\n                  <path d=\"M24 12.073c0-6.627-5.373-12-12-12s-12 5.373-12 12c0 5.99 4.388 10.954 10.125 11.854v-8.385H7.078v-3.47h3.047V9.43c0-3.007 1.792-4.669 4.533-4.669 1.312 0 2.686.235 2.686.235v2.953H15.83c-1.491 0-1.956.925-1.956 1.874v2.25h3.328l-.532 3.47h-2.796v8.385C19.612 23.027 24 18.062 24 12.073z\"/>\n                </svg>\n              </a>\n              <a href=\"#\" className=\"text-gray-400 hover:text-white transition-colors\">\n                <span className=\"sr-only\">تلغرام</span>\n                <svg className=\"h-6 w-6\" fill=\"currentColor\" viewBox=\"0 0 24 24\">\n                  <path d=\"M11.944 0A12 12 0 0 0 0 12a12 12 0 0 0 12 12 12 12 0 0 0 12-12A12 12 0 0 0 12 0a12 12 0 0 0-.056 0zm4.962 7.224c.1-.002.321.023.465.14a.506.506 0 0 1 .171.325c.016.093.036.306.02.472-.18 1.898-.962 6.502-1.36 8.627-.168.9-.499 1.201-.82 1.23-.696.065-1.225-.46-1.9-.902-1.056-.693-1.653-1.124-2.678-1.8-1.185-.78-.417-1.21.258-1.91.177-.184 3.247-2.977 3.307-3.23.007-.032.014-.15-.056-.212s-.174-.041-.249-.024c-.106.024-1.793 1.14-5.061 3.345-.48.33-.913.49-1.302.48-.428-.008-1.252-.241-1.865-.44-.752-.245-1.349-.374-1.297-.789.027-.216.325-.437.893-.663 3.498-1.524 5.83-2.529 6.998-3.014 3.332-1.386 4.025-1.627 4.476-1.635z\"/>\n                </svg>\n              </a>\n              <a href=\"#\" className=\"text-gray-400 hover:text-white transition-colors\">\n                <span className=\"sr-only\">واتساب</span>\n                <svg className=\"h-6 w-6\" fill=\"currentColor\" viewBox=\"0 0 24 24\">\n                  <path d=\"M17.472 14.382c-.297-.149-1.758-.867-2.03-.967-.273-.099-.471-.148-.67.15-.197.297-.767.966-.94 1.164-.173.199-.347.223-.644.075-.297-.15-1.255-.463-2.39-1.475-.883-.788-1.48-1.761-1.653-2.059-.173-.297-.018-.458.13-.606.134-.133.298-.347.446-.52.149-.174.198-.298.298-.497.099-.198.05-.371-.025-.52-.075-.149-.669-1.612-.916-2.207-.242-.579-.487-.5-.669-.51-.173-.008-.371-.01-.57-.01-.198 0-.52.074-.792.372-.272.297-1.04 1.016-1.04 2.479 0 1.462 1.065 2.875 1.213 3.074.149.198 2.096 3.2 5.077 4.487.709.306 1.262.489 1.694.625.712.227 1.36.195 1.871.118.571-.085 1.758-.719 2.006-1.413.248-.694.248-1.289.173-1.413-.074-.124-.272-.198-.57-.347m-5.421 7.403h-.004a9.87 9.87 0 01-5.031-1.378l-.361-.214-3.741.982.998-3.648-.235-.374a9.86 9.86 0 01-1.51-5.26c.001-5.45 4.436-9.884 9.888-9.884 2.64 0 5.122 1.03 6.988 2.898a9.825 9.825 0 012.893 6.994c-.003 5.45-4.437 9.884-9.885 9.884m8.413-18.297A11.815 11.815 0 0012.05 0C5.495 0 .16 5.335.157 11.892c0 2.096.547 4.142 1.588 5.945L.057 24l6.305-1.654a11.882 11.882 0 005.683 1.448h.005c6.554 0 11.89-5.335 11.893-11.893A11.821 11.821 0 0020.885 3.097\"/>\n                </svg>\n              </a>\n            </div>\n          </div>\n\n          {/* روابط سريعة */}\n          <div>\n            <h4 className=\"text-lg font-semibold mb-4\">روابط سريعة</h4>\n            <ul className=\"space-y-2\">\n              <li>\n                <Link href=\"/about\" className=\"text-gray-300 hover:text-white transition-colors\">\n                  من نحن\n                </Link>\n              </li>\n              <li>\n                <Link href=\"/how-it-works\" className=\"text-gray-300 hover:text-white transition-colors\">\n                  كيف يعمل الموقع\n                </Link>\n              </li>\n              <li>\n                <Link href=\"/safety-tips\" className=\"text-gray-300 hover:text-white transition-colors\">\n                  نصائح الأمان\n                </Link>\n              </li>\n              <li>\n                <Link href=\"/pricing\" className=\"text-gray-300 hover:text-white transition-colors\">\n                  باقات الإعلانات\n                </Link>\n              </li>\n              <li>\n                <Link href=\"/contact\" className=\"text-gray-300 hover:text-white transition-colors\">\n                  اتصل بنا\n                </Link>\n              </li>\n            </ul>\n          </div>\n\n          {/* الدعم والمساعدة */}\n          <div>\n            <h4 className=\"text-lg font-semibold mb-4\">الدعم والمساعدة</h4>\n            <ul className=\"space-y-2\">\n              <li>\n                <Link href=\"/help\" className=\"text-gray-300 hover:text-white transition-colors\">\n                  مركز المساعدة\n                </Link>\n              </li>\n              <li>\n                <Link href=\"/terms\" className=\"text-gray-300 hover:text-white transition-colors\">\n                  شروط الاستخدام\n                </Link>\n              </li>\n              <li>\n                <Link href=\"/privacy\" className=\"text-gray-300 hover:text-white transition-colors\">\n                  سياسة الخصوصية\n                </Link>\n              </li>\n              <li>\n                <Link href=\"/report\" className=\"text-gray-300 hover:text-white transition-colors\">\n                  الإبلاغ عن مشكلة\n                </Link>\n              </li>\n              <li>\n                <Link href=\"/faq\" className=\"text-gray-300 hover:text-white transition-colors\">\n                  الأسئلة الشائعة\n                </Link>\n              </li>\n            </ul>\n          </div>\n        </div>\n\n        {/* معلومات الاتصال */}\n        <div className=\"border-t border-gray-700 mt-8 pt-8\">\n          <div className=\"grid grid-cols-1 md:grid-cols-3 gap-6\">\n            <div className=\"text-center md:text-right\">\n              <h5 className=\"font-semibold mb-2\">البريد الإلكتروني</h5>\n              <p className=\"text-gray-300\"><EMAIL></p>\n            </div>\n            <div className=\"text-center\">\n              <h5 className=\"font-semibold mb-2\">رقم الهاتف</h5>\n              <p className=\"text-gray-300\" dir=\"ltr\">+963 11 123 4567</p>\n            </div>\n            <div className=\"text-center md:text-left\">\n              <h5 className=\"font-semibold mb-2\">العنوان</h5>\n              <p className=\"text-gray-300\">دمشق، سوريا</p>\n            </div>\n          </div>\n        </div>\n\n        {/* حقوق النشر */}\n        <div className=\"border-t border-gray-700 mt-8 pt-8 text-center\">\n          <p className=\"text-gray-400\">\n            © 2024 خَلّيها تنْباع. جميع الحقوق محفوظة.\n          </p>\n          <p className=\"text-gray-500 text-sm mt-2\">\n            تم التطوير بـ ❤️ في سوريا\n          </p>\n        </div>\n      </div>\n    </footer>\n  )\n}\n"], "names": [], "mappings": ";;;;AAAA;;;AAEO,SAAS;IACd,qBACE,8OAAC;QAAO,WAAU;kBAChB,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDAAK,WAAU;0DAA+B;;;;;;;;;;;sDAEjD,8OAAC;;8DACC,8OAAC;oDAAG,WAAU;8DAAoB;;;;;;8DAClC,8OAAC;oDAAE,WAAU;8DAAwB;;;;;;;;;;;;;;;;;;8CAGzC,8OAAC;oCAAE,WAAU;8CAA8B;;;;;;8CAI3C,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAE,MAAK;4CAAI,WAAU;;8DACpB,8OAAC;oDAAK,WAAU;8DAAU;;;;;;8DAC1B,8OAAC;oDAAI,WAAU;oDAAU,MAAK;oDAAe,SAAQ;8DACnD,cAAA,8OAAC;wDAAK,GAAE;;;;;;;;;;;;;;;;;sDAGZ,8OAAC;4CAAE,MAAK;4CAAI,WAAU;;8DACpB,8OAAC;oDAAK,WAAU;8DAAU;;;;;;8DAC1B,8OAAC;oDAAI,WAAU;oDAAU,MAAK;oDAAe,SAAQ;8DACnD,cAAA,8OAAC;wDAAK,GAAE;;;;;;;;;;;;;;;;;sDAGZ,8OAAC;4CAAE,MAAK;4CAAI,WAAU;;8DACpB,8OAAC;oDAAK,WAAU;8DAAU;;;;;;8DAC1B,8OAAC;oDAAI,WAAU;oDAAU,MAAK;oDAAe,SAAQ;8DACnD,cAAA,8OAAC;wDAAK,GAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;sCAOhB,8OAAC;;8CACC,8OAAC;oCAAG,WAAU;8CAA6B;;;;;;8CAC3C,8OAAC;oCAAG,WAAU;;sDACZ,8OAAC;sDACC,cAAA,8OAAC,4JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAS,WAAU;0DAAmD;;;;;;;;;;;sDAInF,8OAAC;sDACC,cAAA,8OAAC,4JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAgB,WAAU;0DAAmD;;;;;;;;;;;sDAI1F,8OAAC;sDACC,cAAA,8OAAC,4JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAe,WAAU;0DAAmD;;;;;;;;;;;sDAIzF,8OAAC;sDACC,cAAA,8OAAC,4JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAW,WAAU;0DAAmD;;;;;;;;;;;sDAIrF,8OAAC;sDACC,cAAA,8OAAC,4JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAW,WAAU;0DAAmD;;;;;;;;;;;;;;;;;;;;;;;sCAQzF,8OAAC;;8CACC,8OAAC;oCAAG,WAAU;8CAA6B;;;;;;8CAC3C,8OAAC;oCAAG,WAAU;;sDACZ,8OAAC;sDACC,cAAA,8OAAC,4JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAQ,WAAU;0DAAmD;;;;;;;;;;;sDAIlF,8OAAC;sDACC,cAAA,8OAAC,4JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAS,WAAU;0DAAmD;;;;;;;;;;;sDAInF,8OAAC;sDACC,cAAA,8OAAC,4JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAW,WAAU;0DAAmD;;;;;;;;;;;sDAIrF,8OAAC;sDACC,cAAA,8OAAC,4JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAU,WAAU;0DAAmD;;;;;;;;;;;sDAIpF,8OAAC;sDACC,cAAA,8OAAC,4JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAO,WAAU;0DAAmD;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8BASvF,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAG,WAAU;kDAAqB;;;;;;kDACnC,8OAAC;wCAAE,WAAU;kDAAgB;;;;;;;;;;;;0CAE/B,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAG,WAAU;kDAAqB;;;;;;kDACnC,8OAAC;wCAAE,WAAU;wCAAgB,KAAI;kDAAM;;;;;;;;;;;;0CAEzC,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAG,WAAU;kDAAqB;;;;;;kDACnC,8OAAC;wCAAE,WAAU;kDAAgB;;;;;;;;;;;;;;;;;;;;;;;8BAMnC,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAE,WAAU;sCAAgB;;;;;;sCAG7B,8OAAC;4BAAE,WAAU;sCAA6B;;;;;;;;;;;;;;;;;;;;;;;AAOpD", "debugId": null}}, {"offset": {"line": 1006, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/kaliha/khalihatanbaa/src/components/ClientOnly.tsx"], "sourcesContent": ["'use client'\n\nimport { useEffect, useState } from 'react'\n\ninterface ClientOnlyProps {\n  children: React.ReactNode\n  fallback?: React.ReactNode\n}\n\nexport function ClientOnly({ children, fallback = null }: ClientOnlyProps) {\n  const [hasMounted, setHasMounted] = useState(false)\n\n  useEffect(() => {\n    setHasMounted(true)\n  }, [])\n\n  if (!hasMounted) {\n    return <>{fallback}</>\n  }\n\n  return <>{children}</>\n}\n"], "names": [], "mappings": ";;;;AAEA;AAFA;;;AASO,SAAS,WAAW,EAAE,QAAQ,EAAE,WAAW,IAAI,EAAmB;IACvE,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE7C,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,cAAc;IAChB,GAAG,EAAE;IAEL,IAAI,CAAC,YAAY;QACf,qBAAO;sBAAG;;IACZ;IAEA,qBAAO;kBAAG;;AACZ", "debugId": null}}, {"offset": {"line": 1034, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/kaliha/khalihatanbaa/src/app/messages/page.tsx"], "sourcesContent": ["\"use client\";\n\nimport { useState, useEffect, useRef } from \"react\";\nimport { useSession } from \"next-auth/react\";\nimport { useRouter, useSearchParams } from \"next/navigation\";\nimport { Header } from \"@/components/layout/Header\";\nimport { Footer } from \"@/components/layout/Footer\";\nimport { PlaceholderImage } from \"@/components/ui/PlaceholderImage\";\nimport {\n  PaperAirplaneIcon,\n  ChatBubbleLeftRightIcon,\n  UserIcon,\n  ClockIcon,\n} from \"@heroicons/react/24/outline\";\nimport { ClientOnly } from \"@/components/ClientOnly\";\n\ninterface Message {\n  id: string;\n  content: string;\n  isRead: boolean;\n  createdAt: string;\n  fromId: string;\n  toId: string;\n  adId?: string;\n  from: {\n    id: string;\n    name: string;\n    avatar?: string;\n  };\n  to: {\n    id: string;\n    name: string;\n    avatar?: string;\n  };\n  ad?: {\n    id: string;\n    title: string;\n    price: number;\n    imageUrls: string[];\n  };\n}\n\ninterface Conversation {\n  id: string;\n  content: string;\n  createdAt: string;\n  adId?: string;\n  otherUser: {\n    id: string;\n    name: string;\n    avatar?: string;\n  };\n  ad?: {\n    id: string;\n    title: string;\n    price: number;\n    imageUrls: string[];\n  };\n  unreadCount: number;\n}\n\nexport default function MessagesPage() {\n  const { data: session, status } = useSession();\n  const router = useRouter();\n  const searchParams = useSearchParams();\n\n  const [conversations, setConversations] = useState<Conversation[]>([]);\n  const [selectedConversation, setSelectedConversation] = useState<\n    string | null\n  >(null);\n  const [messages, setMessages] = useState<Message[]>([]);\n  const [newMessage, setNewMessage] = useState(\"\");\n  const [loading, setLoading] = useState(true);\n  const [sending, setSending] = useState(false);\n\n  const messagesEndRef = useRef<HTMLDivElement>(null);\n\n  // تعريف الدوال\n  const fetchConversations = async () => {\n    try {\n      const response = await fetch(\"/api/messages\");\n      const data = await response.json();\n\n      if (data.success) {\n        setConversations(data.data);\n        // إذا لم تكن هناك محادثة محددة وهناك محادثات متاحة، اختر الأولى\n        if (!selectedConversation && data.data.length > 0) {\n          setSelectedConversation(data.data[0].otherUser.id);\n          fetchMessages(data.data[0].otherUser.id, data.data[0].adId);\n        }\n      }\n    } catch (error) {\n      console.error(\"Error fetching conversations:\", error);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const fetchMessages = async (userId: string, adId?: string | null) => {\n    try {\n      const url = new URL(\"/api/messages\", window.location.origin);\n      url.searchParams.set(\"with\", userId);\n      if (adId) url.searchParams.set(\"adId\", adId);\n\n      const response = await fetch(url.toString());\n      const data = await response.json();\n\n      if (data.success) {\n        setMessages(data.data);\n      }\n    } catch (error) {\n      console.error(\"Error fetching messages:\", error);\n    }\n  };\n\n  // جميع useEffect يجب أن تكون في الأعلى قبل أي return مشروط\n  useEffect(() => {\n    if (status === \"authenticated\") {\n      fetchConversations();\n\n      // التحقق من وجود محادثة محددة في URL\n      const withUser = searchParams.get(\"with\") || searchParams.get(\"user\");\n      const adId = searchParams.get(\"ad\");\n\n      if (withUser) {\n        setSelectedConversation(withUser);\n        fetchMessages(withUser, adId);\n      }\n    }\n  }, [searchParams, status]);\n\n  useEffect(() => {}, [messages]);\n\n  useEffect(() => {\n    if (status === \"unauthenticated\") {\n      router.push(\"/auth/login\");\n    }\n  }, [status, router]);\n\n  // إعادة توجيه إذا لم يكن المستخدم مسجل دخول\n  if (status === \"loading\") {\n    return (\n      <div className=\"min-h-screen flex items-center justify-center\">\n        جاري التحميل...\n      </div>\n    );\n  }\n\n  if (status === \"unauthenticated\") {\n    return null;\n  }\n\n  const sendMessage = async () => {\n    if (!newMessage.trim() || !selectedConversation || sending) return;\n\n    setSending(true);\n    try {\n      const adId = searchParams.get(\"ad\");\n\n      const response = await fetch(\"/api/messages\", {\n        method: \"POST\",\n        headers: {\n          \"Content-Type\": \"application/json\",\n        },\n        body: JSON.stringify({\n          toId: selectedConversation,\n          content: newMessage.trim(),\n          ...(adId && { adId }),\n        }),\n      });\n\n      const data = await response.json();\n\n      if (data.success) {\n        setMessages((prev) => [...prev, data.data]);\n        setNewMessage(\"\");\n        fetchConversations(); // تحديث قائمة المحادثات\n      } else {\n        alert(data.error || \"حدث خطأ في إرسال الرسالة\");\n      }\n    } catch (error) {\n      console.error(\"Error sending message:\", error);\n      alert(\"حدث خطأ في إرسال الرسالة\");\n    } finally {\n      setSending(false);\n    }\n  };\n\n  const selectConversation = (conversation: Conversation) => {\n    setSelectedConversation(conversation.otherUser.id);\n    fetchMessages(conversation.otherUser.id, conversation.adId);\n\n    // تحديث URL\n    const url = new URL(window.location.href);\n    url.searchParams.set(\"with\", conversation.otherUser.id);\n    if (conversation.adId) {\n      url.searchParams.set(\"ad\", conversation.adId);\n    } else {\n      url.searchParams.delete(\"ad\");\n    }\n    window.history.pushState({}, \"\", url.toString());\n  };\n\n  const formatTime = (dateString: string) => {\n    const date = new Date(dateString);\n    return date.toLocaleTimeString(\"ar-SY\", {\n      hour: \"2-digit\",\n      minute: \"2-digit\",\n      hour12: false,\n    });\n  };\n\n  const formatPrice = (price: number) => {\n    return new Intl.NumberFormat(\"ar-SY\").format(price);\n  };\n\n  if (loading) {\n    return (\n      <div className=\"min-h-screen bg-background\">\n        <Header />\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8\">\n          <div className=\"animate-pulse\">\n            <div className=\"h-8 bg-gray-200 rounded w-1/4 mb-6\"></div>\n            <div className=\"grid grid-cols-1 lg:grid-cols-3 gap-6\">\n              <div className=\"h-96 bg-gray-200 rounded-lg\"></div>\n              <div className=\"lg:col-span-2 h-96 bg-gray-200 rounded-lg\"></div>\n            </div>\n          </div>\n        </div>\n        <Footer />\n      </div>\n    );\n  }\n\n  return (\n    <ClientOnly\n      fallback={\n        <div className=\"min-h-screen bg-background\">\n          <Header />\n          <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8\">\n            <div className=\"animate-pulse\">\n              <div className=\"h-8 bg-gray-200 rounded w-1/4 mb-6\"></div>\n              <div className=\"grid grid-cols-1 lg:grid-cols-3 gap-6\">\n                <div className=\"h-96 bg-gray-200 rounded-lg\"></div>\n                <div className=\"lg:col-span-2 h-96 bg-gray-200 rounded-lg\"></div>\n              </div>\n            </div>\n          </div>\n          <Footer />\n        </div>\n      }\n    >\n      <div className=\"min-h-screen bg-background\">\n        <Header />\n\n        <main className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8\">\n          <h1 className=\"text-2xl font-bold text-dark-800 mb-6\">الرسائل</h1>\n\n          <div className=\"grid grid-cols-1 lg:grid-cols-3 gap-6 h-[600px]\">\n            {/* قائمة المحادثات */}\n            <div className=\"bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden\">\n              <div className=\"p-4 border-b border-gray-200\">\n                <h2 className=\"font-semibold text-dark-800\">المحادثات</h2>\n              </div>\n\n              <div className=\"overflow-y-auto h-full\">\n                {conversations.length === 0 ? (\n                  <div className=\"p-6 text-center text-gray-500\">\n                    <ChatBubbleLeftRightIcon className=\"h-12 w-12 mx-auto mb-3 text-gray-300\" />\n                    <p>لا توجد محادثات حتى الآن</p>\n                    <p className=\"text-sm mt-1\">\n                      ابدأ محادثة من خلال التواصل مع بائع\n                    </p>\n                  </div>\n                ) : (\n                  conversations.map((conversation) => (\n                    <button\n                      key={`${conversation.otherUser.id}-${\n                        conversation.adId || \"general\"\n                      }`}\n                      onClick={() => selectConversation(conversation)}\n                      className={`w-full p-4 border-b border-gray-100 hover:bg-gray-50 text-right transition-colors ${\n                        selectedConversation === conversation.otherUser.id\n                          ? \"bg-primary-50\"\n                          : \"\"\n                      }`}\n                    >\n                      <div className=\"flex items-start space-x-3 space-x-reverse\">\n                        <div className=\"w-10 h-10 bg-primary-100 rounded-full flex items-center justify-center flex-shrink-0\">\n                          {conversation.otherUser.avatar ? (\n                            <img\n                              src={conversation.otherUser.avatar}\n                              alt={conversation.otherUser.name}\n                              className=\"w-full h-full rounded-full object-cover\"\n                            />\n                          ) : (\n                            <UserIcon className=\"h-5 w-5 text-primary-600\" />\n                          )}\n                        </div>\n\n                        <div className=\"flex-1 min-w-0\">\n                          <div className=\"flex items-center justify-between mb-1\">\n                            <p className=\"font-medium text-dark-800 truncate\">\n                              {conversation.otherUser.name}\n                            </p>\n                            {conversation.unreadCount > 0 && (\n                              <span className=\"bg-primary-500 text-white text-xs rounded-full px-2 py-1 min-w-[20px] text-center\">\n                                {conversation.unreadCount}\n                              </span>\n                            )}\n                          </div>\n\n                          {conversation.ad && (\n                            <p className=\"text-sm text-gray-600 truncate mb-1\">\n                              {conversation.ad.title}\n                            </p>\n                          )}\n\n                          <p className=\"text-sm text-gray-500 truncate\">\n                            {conversation.content}\n                          </p>\n\n                          <div className=\"flex items-center mt-1\">\n                            <ClockIcon className=\"h-3 w-3 text-gray-400 ml-1\" />\n                            <span className=\"text-xs text-gray-400\">\n                              {formatTime(conversation.createdAt)}\n                            </span>\n                          </div>\n                        </div>\n                      </div>\n                    </button>\n                  ))\n                )}\n              </div>\n            </div>\n\n            {/* منطقة المحادثة */}\n            <div className=\"lg:col-span-2 bg-white rounded-lg shadow-sm border border-gray-200 flex flex-col\">\n              {selectedConversation ? (\n                <>\n                  {/* رأس المحادثة */}\n                  <div className=\"p-4 border-b border-gray-200\">\n                    <div className=\"flex items-center space-x-3 space-x-reverse\">\n                      <div className=\"w-10 h-10 bg-primary-100 rounded-full flex items-center justify-center\">\n                        <UserIcon className=\"h-5 w-5 text-primary-600\" />\n                      </div>\n                      <div>\n                        <p className=\"font-medium text-dark-800\">\n                          {\n                            conversations.find(\n                              (c) => c.otherUser.id === selectedConversation\n                            )?.otherUser.name\n                          }\n                        </p>\n                        {searchParams.get(\"ad\") && (\n                          <p className=\"text-sm text-gray-500\">\n                            حول:{\" \"}\n                            {\n                              conversations.find(\n                                (c) => c.otherUser.id === selectedConversation\n                              )?.ad?.title\n                            }\n                          </p>\n                        )}\n                      </div>\n                    </div>\n                  </div>\n\n                  {/* الرسائل */}\n                  <div className=\"flex-1 overflow-y-auto p-4 space-y-4\">\n                    {messages.map((message) => (\n                      <div\n                        key={message.id}\n                        className={`flex ${\n                          message.fromId === session?.user?.id\n                            ? \"justify-end\"\n                            : \"justify-start\"\n                        }`}\n                      >\n                        <div\n                          className={`max-w-xs lg:max-w-md px-4 py-2 rounded-lg ${\n                            message.fromId === session?.user?.id\n                              ? \"bg-primary-500 text-white\"\n                              : \"bg-gray-100 text-dark-800\"\n                          }`}\n                        >\n                          <p className=\"text-sm\">{message.content}</p>\n                          <p\n                            className={`text-xs mt-1 ${\n                              message.fromId === session?.user?.id\n                                ? \"text-primary-100\"\n                                : \"text-gray-500\"\n                            }`}\n                          >\n                            {formatTime(message.createdAt)}\n                          </p>\n                        </div>\n                      </div>\n                    ))}\n                    <div ref={messagesEndRef} />\n                  </div>\n\n                  {/* إرسال رسالة */}\n                  <div className=\"p-4 border-t border-gray-200\">\n                    <div className=\"flex space-x-2 space-x-reverse\">\n                      <input\n                        type=\"text\"\n                        value={newMessage}\n                        onChange={(e) => setNewMessage(e.target.value)}\n                        onKeyPress={(e) => e.key === \"Enter\" && sendMessage()}\n                        placeholder=\"اكتب رسالتك...\"\n                        className=\"flex-1 px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent\"\n                        disabled={sending}\n                      />\n                      <button\n                        onClick={sendMessage}\n                        disabled={!newMessage.trim() || sending}\n                        className=\"px-4 py-2 bg-primary-500 text-white rounded-lg hover:bg-primary-600 disabled:opacity-50 disabled:cursor-not-allowed\"\n                      >\n                        <PaperAirplaneIcon className=\"h-5 w-5\" />\n                      </button>\n                    </div>\n                  </div>\n                </>\n              ) : (\n                <div className=\"flex-1 flex items-center justify-center text-gray-500\">\n                  <div className=\"text-center\">\n                    <ChatBubbleLeftRightIcon className=\"h-16 w-16 mx-auto mb-4 text-gray-300\" />\n                    <p className=\"text-lg font-medium\">\n                      اختر محادثة لبدء المراسلة\n                    </p>\n                    <p className=\"text-sm mt-1\">\n                      اختر محادثة من القائمة الجانبية\n                    </p>\n                  </div>\n                </div>\n              )}\n            </div>\n          </div>\n        </main>\n\n        <Footer />\n      </div>\n    </ClientOnly>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AAEA;AAAA;AAAA;AAAA;AAMA;AAdA;;;;;;;;;AA6De,SAAS;IACtB,MAAM,EAAE,MAAM,OAAO,EAAE,MAAM,EAAE,GAAG,CAAA,GAAA,8IAAA,CAAA,aAAU,AAAD;IAC3C,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,eAAe,CAAA,GAAA,kIAAA,CAAA,kBAAe,AAAD;IAEnC,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAkB,EAAE;IACrE,MAAM,CAAC,sBAAsB,wBAAwB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAE7D;IACF,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAa,EAAE;IACtD,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEvC,MAAM,iBAAiB,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAkB;IAE9C,eAAe;IACf,MAAM,qBAAqB;QACzB,IAAI;YACF,MAAM,WAAW,MAAM,MAAM;YAC7B,MAAM,OAAO,MAAM,SAAS,IAAI;YAEhC,IAAI,KAAK,OAAO,EAAE;gBAChB,iBAAiB,KAAK,IAAI;gBAC1B,gEAAgE;gBAChE,IAAI,CAAC,wBAAwB,KAAK,IAAI,CAAC,MAAM,GAAG,GAAG;oBACjD,wBAAwB,KAAK,IAAI,CAAC,EAAE,CAAC,SAAS,CAAC,EAAE;oBACjD,cAAc,KAAK,IAAI,CAAC,EAAE,CAAC,SAAS,CAAC,EAAE,EAAE,KAAK,IAAI,CAAC,EAAE,CAAC,IAAI;gBAC5D;YACF;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,iCAAiC;QACjD,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,gBAAgB,OAAO,QAAgB;QAC3C,IAAI;YACF,MAAM,MAAM,IAAI,IAAI,iBAAiB,OAAO,QAAQ,CAAC,MAAM;YAC3D,IAAI,YAAY,CAAC,GAAG,CAAC,QAAQ;YAC7B,IAAI,MAAM,IAAI,YAAY,CAAC,GAAG,CAAC,QAAQ;YAEvC,MAAM,WAAW,MAAM,MAAM,IAAI,QAAQ;YACzC,MAAM,OAAO,MAAM,SAAS,IAAI;YAEhC,IAAI,KAAK,OAAO,EAAE;gBAChB,YAAY,KAAK,IAAI;YACvB;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,4BAA4B;QAC5C;IACF;IAEA,2DAA2D;IAC3D,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,WAAW,iBAAiB;YAC9B;YAEA,qCAAqC;YACrC,MAAM,WAAW,aAAa,GAAG,CAAC,WAAW,aAAa,GAAG,CAAC;YAC9D,MAAM,OAAO,aAAa,GAAG,CAAC;YAE9B,IAAI,UAAU;gBACZ,wBAAwB;gBACxB,cAAc,UAAU;YAC1B;QACF;IACF,GAAG;QAAC;QAAc;KAAO;IAEzB,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE,KAAO,GAAG;QAAC;KAAS;IAE9B,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,WAAW,mBAAmB;YAChC,OAAO,IAAI,CAAC;QACd;IACF,GAAG;QAAC;QAAQ;KAAO;IAEnB,4CAA4C;IAC5C,IAAI,WAAW,WAAW;QACxB,qBACE,8OAAC;YAAI,WAAU;sBAAgD;;;;;;IAInE;IAEA,IAAI,WAAW,mBAAmB;QAChC,OAAO;IACT;IAEA,MAAM,cAAc;QAClB,IAAI,CAAC,WAAW,IAAI,MAAM,CAAC,wBAAwB,SAAS;QAE5D,WAAW;QACX,IAAI;YACF,MAAM,OAAO,aAAa,GAAG,CAAC;YAE9B,MAAM,WAAW,MAAM,MAAM,iBAAiB;gBAC5C,QAAQ;gBACR,SAAS;oBACP,gBAAgB;gBAClB;gBACA,MAAM,KAAK,SAAS,CAAC;oBACnB,MAAM;oBACN,SAAS,WAAW,IAAI;oBACxB,GAAI,QAAQ;wBAAE;oBAAK,CAAC;gBACtB;YACF;YAEA,MAAM,OAAO,MAAM,SAAS,IAAI;YAEhC,IAAI,KAAK,OAAO,EAAE;gBAChB,YAAY,CAAC,OAAS;2BAAI;wBAAM,KAAK,IAAI;qBAAC;gBAC1C,cAAc;gBACd,sBAAsB,wBAAwB;YAChD,OAAO;gBACL,MAAM,KAAK,KAAK,IAAI;YACtB;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,0BAA0B;YACxC,MAAM;QACR,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,qBAAqB,CAAC;QAC1B,wBAAwB,aAAa,SAAS,CAAC,EAAE;QACjD,cAAc,aAAa,SAAS,CAAC,EAAE,EAAE,aAAa,IAAI;QAE1D,YAAY;QACZ,MAAM,MAAM,IAAI,IAAI,OAAO,QAAQ,CAAC,IAAI;QACxC,IAAI,YAAY,CAAC,GAAG,CAAC,QAAQ,aAAa,SAAS,CAAC,EAAE;QACtD,IAAI,aAAa,IAAI,EAAE;YACrB,IAAI,YAAY,CAAC,GAAG,CAAC,MAAM,aAAa,IAAI;QAC9C,OAAO;YACL,IAAI,YAAY,CAAC,MAAM,CAAC;QAC1B;QACA,OAAO,OAAO,CAAC,SAAS,CAAC,CAAC,GAAG,IAAI,IAAI,QAAQ;IAC/C;IAEA,MAAM,aAAa,CAAC;QAClB,MAAM,OAAO,IAAI,KAAK;QACtB,OAAO,KAAK,kBAAkB,CAAC,SAAS;YACtC,MAAM;YACN,QAAQ;YACR,QAAQ;QACV;IACF;IAEA,MAAM,cAAc,CAAC;QACnB,OAAO,IAAI,KAAK,YAAY,CAAC,SAAS,MAAM,CAAC;IAC/C;IAEA,IAAI,SAAS;QACX,qBACE,8OAAC;YAAI,WAAU;;8BACb,8OAAC,sIAAA,CAAA,SAAM;;;;;8BACP,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;;;;;0CACf,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;;;;;kDACf,8OAAC;wCAAI,WAAU;;;;;;;;;;;;;;;;;;;;;;;8BAIrB,8OAAC,sIAAA,CAAA,SAAM;;;;;;;;;;;IAGb;IAEA,qBACE,8OAAC,gIAAA,CAAA,aAAU;QACT,wBACE,8OAAC;YAAI,WAAU;;8BACb,8OAAC,sIAAA,CAAA,SAAM;;;;;8BACP,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;;;;;0CACf,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;;;;;kDACf,8OAAC;wCAAI,WAAU;;;;;;;;;;;;;;;;;;;;;;;8BAIrB,8OAAC,sIAAA,CAAA,SAAM;;;;;;;;;;;kBAIX,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC,sIAAA,CAAA,SAAM;;;;;8BAEP,8OAAC;oBAAK,WAAU;;sCACd,8OAAC;4BAAG,WAAU;sCAAwC;;;;;;sCAEtD,8OAAC;4BAAI,WAAU;;8CAEb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDAAG,WAAU;0DAA8B;;;;;;;;;;;sDAG9C,8OAAC;4CAAI,WAAU;sDACZ,cAAc,MAAM,KAAK,kBACxB,8OAAC;gDAAI,WAAU;;kEACb,8OAAC,6OAAA,CAAA,0BAAuB;wDAAC,WAAU;;;;;;kEACnC,8OAAC;kEAAE;;;;;;kEACH,8OAAC;wDAAE,WAAU;kEAAe;;;;;;;;;;;uDAK9B,cAAc,GAAG,CAAC,CAAC,6BACjB,8OAAC;oDAIC,SAAS,IAAM,mBAAmB;oDAClC,WAAW,CAAC,kFAAkF,EAC5F,yBAAyB,aAAa,SAAS,CAAC,EAAE,GAC9C,kBACA,IACJ;8DAEF,cAAA,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAI,WAAU;0EACZ,aAAa,SAAS,CAAC,MAAM,iBAC5B,8OAAC;oEACC,KAAK,aAAa,SAAS,CAAC,MAAM;oEAClC,KAAK,aAAa,SAAS,CAAC,IAAI;oEAChC,WAAU;;;;;yFAGZ,8OAAC,+MAAA,CAAA,WAAQ;oEAAC,WAAU;;;;;;;;;;;0EAIxB,8OAAC;gEAAI,WAAU;;kFACb,8OAAC;wEAAI,WAAU;;0FACb,8OAAC;gFAAE,WAAU;0FACV,aAAa,SAAS,CAAC,IAAI;;;;;;4EAE7B,aAAa,WAAW,GAAG,mBAC1B,8OAAC;gFAAK,WAAU;0FACb,aAAa,WAAW;;;;;;;;;;;;oEAK9B,aAAa,EAAE,kBACd,8OAAC;wEAAE,WAAU;kFACV,aAAa,EAAE,CAAC,KAAK;;;;;;kFAI1B,8OAAC;wEAAE,WAAU;kFACV,aAAa,OAAO;;;;;;kFAGvB,8OAAC;wEAAI,WAAU;;0FACb,8OAAC,iNAAA,CAAA,YAAS;gFAAC,WAAU;;;;;;0FACrB,8OAAC;gFAAK,WAAU;0FACb,WAAW,aAAa,SAAS;;;;;;;;;;;;;;;;;;;;;;;;mDAhDrC,GAAG,aAAa,SAAS,CAAC,EAAE,CAAC,CAAC,EACjC,aAAa,IAAI,IAAI,WACrB;;;;;;;;;;;;;;;;8CA0DZ,8OAAC;oCAAI,WAAU;8CACZ,qCACC;;0DAEE,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAI,WAAU;sEACb,cAAA,8OAAC,+MAAA,CAAA,WAAQ;gEAAC,WAAU;;;;;;;;;;;sEAEtB,8OAAC;;8EACC,8OAAC;oEAAE,WAAU;8EAET,cAAc,IAAI,CAChB,CAAC,IAAM,EAAE,SAAS,CAAC,EAAE,KAAK,uBACzB,UAAU;;;;;;gEAGhB,aAAa,GAAG,CAAC,uBAChB,8OAAC;oEAAE,WAAU;;wEAAwB;wEAC9B;wEAEH,cAAc,IAAI,CAChB,CAAC,IAAM,EAAE,SAAS,CAAC,EAAE,KAAK,uBACzB,IAAI;;;;;;;;;;;;;;;;;;;;;;;;0DASnB,8OAAC;gDAAI,WAAU;;oDACZ,SAAS,GAAG,CAAC,CAAC,wBACb,8OAAC;4DAEC,WAAW,CAAC,KAAK,EACf,QAAQ,MAAM,KAAK,SAAS,MAAM,KAC9B,gBACA,iBACJ;sEAEF,cAAA,8OAAC;gEACC,WAAW,CAAC,0CAA0C,EACpD,QAAQ,MAAM,KAAK,SAAS,MAAM,KAC9B,8BACA,6BACJ;;kFAEF,8OAAC;wEAAE,WAAU;kFAAW,QAAQ,OAAO;;;;;;kFACvC,8OAAC;wEACC,WAAW,CAAC,aAAa,EACvB,QAAQ,MAAM,KAAK,SAAS,MAAM,KAC9B,qBACA,iBACJ;kFAED,WAAW,QAAQ,SAAS;;;;;;;;;;;;2DAtB5B,QAAQ,EAAE;;;;;kEA2BnB,8OAAC;wDAAI,KAAK;;;;;;;;;;;;0DAIZ,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DACC,MAAK;4DACL,OAAO;4DACP,UAAU,CAAC,IAAM,cAAc,EAAE,MAAM,CAAC,KAAK;4DAC7C,YAAY,CAAC,IAAM,EAAE,GAAG,KAAK,WAAW;4DACxC,aAAY;4DACZ,WAAU;4DACV,UAAU;;;;;;sEAEZ,8OAAC;4DACC,SAAS;4DACT,UAAU,CAAC,WAAW,IAAI,MAAM;4DAChC,WAAU;sEAEV,cAAA,8OAAC,iOAAA,CAAA,oBAAiB;gEAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;qEAMrC,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,6OAAA,CAAA,0BAAuB;oDAAC,WAAU;;;;;;8DACnC,8OAAC;oDAAE,WAAU;8DAAsB;;;;;;8DAGnC,8OAAC;oDAAE,WAAU;8DAAe;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8BAUxC,8OAAC,sIAAA,CAAA,SAAM;;;;;;;;;;;;;;;;AAIf", "debugId": null}}]}