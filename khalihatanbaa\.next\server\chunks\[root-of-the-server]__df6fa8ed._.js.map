{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 68, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/kaliha/khalihatan<PERSON>a/src/lib/prisma.ts"], "sourcesContent": ["import { PrismaClient } from '@prisma/client'\n\nconst globalForPrisma = globalThis as unknown as {\n  prisma: PrismaClient | undefined\n}\n\nexport const prisma =\n  globalForPrisma.prisma ??\n  new PrismaClient({\n    log: ['query'],\n  })\n\nif (process.env.NODE_ENV !== 'production') globalForPrisma.prisma = prisma\n"], "names": [], "mappings": ";;;AAAA;;AAEA,MAAM,kBAAkB;AAIjB,MAAM,SACX,gBAAgB,MAAM,IACtB,IAAI,6HAAA,CAAA,eAAY,CAAC;IACf,KAAK;QAAC;KAAQ;AAChB;AAEF,wCAA2C,gBAAgB,MAAM,GAAG", "debugId": null}}, {"offset": {"line": 86, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/kaliha/khalihatanbaa/src/app/api/ads/route.ts"], "sourcesContent": ["import { NextRequest, NextResponse } from \"next/server\";\nimport { prisma } from \"@/lib/prisma\";\n\nexport async function GET(request: NextRequest) {\n  try {\n    const { searchParams } = new URL(request.url);\n    const page = parseInt(searchParams.get(\"page\") || \"1\");\n    const limit = parseInt(searchParams.get(\"limit\") || \"12\");\n    const category = searchParams.get(\"category\");\n    const city = searchParams.get(\"city\");\n    const condition = searchParams.get(\"condition\");\n    const minPrice = searchParams.get(\"minPrice\");\n    const maxPrice = searchParams.get(\"maxPrice\");\n    const search = searchParams.get(\"search\");\n    const sortBy = searchParams.get(\"sortBy\") || \"newest\";\n    const featured = searchParams.get(\"featured\") === \"true\";\n    const skip = (page - 1) * limit;\n\n    // بناء شروط البحث\n    const where: any = {\n      isActive: true,\n      OR: [{ expiresAt: null }, { expiresAt: { gte: new Date() } }],\n    };\n\n    // فلترة حسب الفئة\n    if (category) {\n      where.category = category;\n    }\n\n    // فلترة حسب المدينة\n    if (city) {\n      where.city = city;\n    }\n\n    // فلترة حسب الحالة\n    if (condition) {\n      where.condition = condition;\n    }\n\n    // فلترة الإعلانات المميزة\n    if (featured) {\n      where.isPromoted = true;\n    }\n\n    // فلترة حسب السعر\n    if (minPrice || maxPrice) {\n      where.price = {};\n      if (minPrice) where.price.gte = parseFloat(minPrice);\n      if (maxPrice) where.price.lte = parseFloat(maxPrice);\n    }\n\n    // البحث في العنوان والوصف\n    if (search) {\n      where.AND = [\n        where.AND || {},\n        {\n          OR: [\n            { title: { contains: search, mode: \"insensitive\" } },\n            { description: { contains: search, mode: \"insensitive\" } },\n          ],\n        },\n      ];\n    }\n\n    // ترتيب النتائج\n    let orderBy: any = [];\n\n    switch (sortBy) {\n      case \"newest\":\n        orderBy = [{ isPromoted: \"desc\" }, { createdAt: \"desc\" }];\n        break;\n      case \"oldest\":\n        orderBy = [{ isPromoted: \"desc\" }, { createdAt: \"asc\" }];\n        break;\n      case \"price_low\":\n        orderBy = [{ isPromoted: \"desc\" }, { price: \"asc\" }];\n        break;\n      case \"price_high\":\n        orderBy = [{ isPromoted: \"desc\" }, { price: \"desc\" }];\n        break;\n      case \"most_viewed\":\n        orderBy = [{ isPromoted: \"desc\" }, { views: \"desc\" }];\n        break;\n      default:\n        orderBy = [{ isPromoted: \"desc\" }, { createdAt: \"desc\" }];\n    }\n\n    // جلب الإعلانات\n    const [ads, total] = await Promise.all([\n      prisma.ad.findMany({\n        where,\n        include: {\n          user: {\n            select: {\n              id: true,\n              name: true,\n              avatar: true,\n              ratingAverage: true,\n              ratingCount: true,\n            },\n          },\n        },\n        orderBy,\n        skip,\n        take: limit,\n      }),\n      prisma.ad.count({ where }),\n    ]);\n\n    const totalPages = Math.ceil(total / limit);\n\n    return NextResponse.json({\n      success: true,\n      data: ads,\n      pagination: {\n        page,\n        limit,\n        total,\n        totalPages,\n        hasNext: page < totalPages,\n        hasPrev: page > 1,\n      },\n    });\n  } catch (error) {\n    console.error(\"Error fetching ads:\", error);\n    return NextResponse.json(\n      { success: false, error: \"حدث خطأ في جلب الإعلانات\" },\n      { status: 500 }\n    );\n  }\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAEO,eAAe,IAAI,OAAoB;IAC5C,IAAI;QACF,MAAM,EAAE,YAAY,EAAE,GAAG,IAAI,IAAI,QAAQ,GAAG;QAC5C,MAAM,OAAO,SAAS,aAAa,GAAG,CAAC,WAAW;QAClD,MAAM,QAAQ,SAAS,aAAa,GAAG,CAAC,YAAY;QACpD,MAAM,WAAW,aAAa,GAAG,CAAC;QAClC,MAAM,OAAO,aAAa,GAAG,CAAC;QAC9B,MAAM,YAAY,aAAa,GAAG,CAAC;QACnC,MAAM,WAAW,aAAa,GAAG,CAAC;QAClC,MAAM,WAAW,aAAa,GAAG,CAAC;QAClC,MAAM,SAAS,aAAa,GAAG,CAAC;QAChC,MAAM,SAAS,aAAa,GAAG,CAAC,aAAa;QAC7C,MAAM,WAAW,aAAa,GAAG,CAAC,gBAAgB;QAClD,MAAM,OAAO,CAAC,OAAO,CAAC,IAAI;QAE1B,kBAAkB;QAClB,MAAM,QAAa;YACjB,UAAU;YACV,IAAI;gBAAC;oBAAE,WAAW;gBAAK;gBAAG;oBAAE,WAAW;wBAAE,KAAK,IAAI;oBAAO;gBAAE;aAAE;QAC/D;QAEA,kBAAkB;QAClB,IAAI,UAAU;YACZ,MAAM,QAAQ,GAAG;QACnB;QAEA,oBAAoB;QACpB,IAAI,MAAM;YACR,MAAM,IAAI,GAAG;QACf;QAEA,mBAAmB;QACnB,IAAI,WAAW;YACb,MAAM,SAAS,GAAG;QACpB;QAEA,0BAA0B;QAC1B,IAAI,UAAU;YACZ,MAAM,UAAU,GAAG;QACrB;QAEA,kBAAkB;QAClB,IAAI,YAAY,UAAU;YACxB,MAAM,KAAK,GAAG,CAAC;YACf,IAAI,UAAU,MAAM,KAAK,CAAC,GAAG,GAAG,WAAW;YAC3C,IAAI,UAAU,MAAM,KAAK,CAAC,GAAG,GAAG,WAAW;QAC7C;QAEA,0BAA0B;QAC1B,IAAI,QAAQ;YACV,MAAM,GAAG,GAAG;gBACV,MAAM,GAAG,IAAI,CAAC;gBACd;oBACE,IAAI;wBACF;4BAAE,OAAO;gCAAE,UAAU;gCAAQ,MAAM;4BAAc;wBAAE;wBACnD;4BAAE,aAAa;gCAAE,UAAU;gCAAQ,MAAM;4BAAc;wBAAE;qBAC1D;gBACH;aACD;QACH;QAEA,gBAAgB;QAChB,IAAI,UAAe,EAAE;QAErB,OAAQ;YACN,KAAK;gBACH,UAAU;oBAAC;wBAAE,YAAY;oBAAO;oBAAG;wBAAE,WAAW;oBAAO;iBAAE;gBACzD;YACF,KAAK;gBACH,UAAU;oBAAC;wBAAE,YAAY;oBAAO;oBAAG;wBAAE,WAAW;oBAAM;iBAAE;gBACxD;YACF,KAAK;gBACH,UAAU;oBAAC;wBAAE,YAAY;oBAAO;oBAAG;wBAAE,OAAO;oBAAM;iBAAE;gBACpD;YACF,KAAK;gBACH,UAAU;oBAAC;wBAAE,YAAY;oBAAO;oBAAG;wBAAE,OAAO;oBAAO;iBAAE;gBACrD;YACF,KAAK;gBACH,UAAU;oBAAC;wBAAE,YAAY;oBAAO;oBAAG;wBAAE,OAAO;oBAAO;iBAAE;gBACrD;YACF;gBACE,UAAU;oBAAC;wBAAE,YAAY;oBAAO;oBAAG;wBAAE,WAAW;oBAAO;iBAAE;QAC7D;QAEA,gBAAgB;QAChB,MAAM,CAAC,KAAK,MAAM,GAAG,MAAM,QAAQ,GAAG,CAAC;YACrC,sHAAA,CAAA,SAAM,CAAC,EAAE,CAAC,QAAQ,CAAC;gBACjB;gBACA,SAAS;oBACP,MAAM;wBACJ,QAAQ;4BACN,IAAI;4BACJ,MAAM;4BACN,QAAQ;4BACR,eAAe;4BACf,aAAa;wBACf;oBACF;gBACF;gBACA;gBACA;gBACA,MAAM;YACR;YACA,sHAAA,CAAA,SAAM,CAAC,EAAE,CAAC,KAAK,CAAC;gBAAE;YAAM;SACzB;QAED,MAAM,aAAa,KAAK,IAAI,CAAC,QAAQ;QAErC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YACvB,SAAS;YACT,MAAM;YACN,YAAY;gBACV;gBACA;gBACA;gBACA;gBACA,SAAS,OAAO;gBAChB,SAAS,OAAO;YAClB;QACF;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,uBAAuB;QACrC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YAAE,SAAS;YAAO,OAAO;QAA2B,GACpD;YAAE,QAAQ;QAAI;IAElB;AACF", "debugId": null}}]}