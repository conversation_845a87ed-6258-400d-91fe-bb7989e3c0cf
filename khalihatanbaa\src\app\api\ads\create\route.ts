import { NextRequest, NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import { authOptions } from "@/lib/auth";
import { prisma } from "@/lib/prisma";
import { z } from "zod";

// مخطط التحقق من بيانات الإعلان
const adSchema = z.object({
  title: z
    .string()
    .min(5, "العنوان يجب أن يكون 5 أحرف على الأقل")
    .max(100, "العنوان طويل جداً"),
  description: z
    .string()
    .min(20, "الوصف يجب أن يكون 20 حرف على الأقل")
    .max(1000, "الوصف طويل جداً"),
  price: z.number().min(0, "السعر يجب أن يكون أكبر من أو يساوي صفر"),
  category: z.string().min(1, "الفئة مطلوبة"),
  subCategory: z.string().optional(),
  condition: z.enum(["جديد", "مستعمل"], {
    errorMap: () => ({ message: "الحالة يجب أن تكون جديد أو مستعمل" }),
  }),
  city: z.string().min(1, "المدينة مطلوبة"),
  region: z.string().optional(),
  addressDetail: z.string().optional(),
  imageUrls: z.array(z.string()).optional(),
  specifications: z.record(z.any()).optional(),
  adType: z
    .enum(["free", "paid", "promoted"], {
      errorMap: () => ({ message: "نوع الإعلان غير صحيح" }),
    })
    .default("free"),
  duration: z.number().min(1).max(365).optional(), // مدة الإعلان بالأيام
});

export async function POST(request: NextRequest) {
  try {
    console.log("=== API Create Ad Start ===");

    const session = await getServerSession(authOptions);
    console.log(
      "Session:",
      session?.user?.id ? "Authenticated" : "Not authenticated"
    );

    if (!session?.user?.id) {
      return NextResponse.json(
        { success: false, error: "يجب تسجيل الدخول أولاً" },
        { status: 401 }
      );
    }

    const body = await request.json();
    console.log("Request body:", body);

    const validatedData = adSchema.parse(body);
    console.log("Validated data:", validatedData);

    // جلب بيانات المستخدم للتحقق من الحدود
    const user = await prisma.user.findUnique({
      where: { id: session.user.id },
      select: {
        freeAdsCount: true,
        freeAdsExpiresAt: true,
        paidAdsCount: true,
        paidAdsExpiresAt: true,
        _count: {
          select: {
            ads: {
              where: { isActive: true },
            },
          },
        },
      },
    });

    if (!user) {
      console.log("User not found:", session.user.id);
      return NextResponse.json(
        { success: false, error: "المستخدم غير موجود" },
        { status: 404 }
      );
    }

    console.log("User data:", user);

    const now = new Date();
    let canCreate = false;
    let adData: any = {
      ...validatedData,
      userId: session.user.id,
    };

    console.log("Initial ad data:", adData);

    // التحقق من نوع الإعلان والحدود المتاحة
    console.log("Checking ad type:", validatedData.adType);

    switch (validatedData.adType) {
      case "free":
        const freeAdsAvailable =
          user.freeAdsExpiresAt && user.freeAdsExpiresAt < now
            ? 0
            : Math.max(0, user.freeAdsCount - user._count.ads);

        console.log("Free ads check:", {
          freeAdsCount: user.freeAdsCount,
          currentActiveAds: user._count.ads,
          freeAdsAvailable,
          expiresAt: user.freeAdsExpiresAt,
          now,
        });

        if (freeAdsAvailable > 0) {
          canCreate = true;
          adData.isFreeAd = true;
          adData.expiresAt = new Date(now.getTime() + 30 * 24 * 60 * 60 * 1000); // 30 يوم
          console.log("Free ad approved");
        } else {
          console.log("Free ad rejected - no available slots");
        }
        break;

      case "paid":
        const paidAdsAvailable =
          user.paidAdsExpiresAt && user.paidAdsExpiresAt < now
            ? 0
            : Math.max(0, user.paidAdsCount - user._count.ads);

        if (paidAdsAvailable > 0) {
          canCreate = true;
          adData.isFreeAd = false;
          const duration = validatedData.duration || 60; // 60 يوم افتراضي
          adData.expiresAt = new Date(
            now.getTime() + duration * 24 * 60 * 60 * 1000
          );
        }
        break;

      case "promoted":
        // للإعلانات المميزة، نسمح بها دائماً (يمكن تعديل هذا لاحقاً)
        canCreate = true;
        adData.isFreeAd = false;
        adData.isPromoted = true;
        const duration = validatedData.duration || 30; // 30 يوم افتراضي
        adData.expiresAt = new Date(
          now.getTime() + duration * 24 * 60 * 60 * 1000
        );
        console.log("Promoted ad approved");
        break;
    }

    console.log("Can create ad:", canCreate);
    console.log("Final ad data:", adData);

    if (!canCreate) {
      const errorMsg = `لا يمكنك إنشاء إعلان ${
        validatedData.adType === "free"
          ? "مجاني"
          : validatedData.adType === "paid"
          ? "مدفوع"
          : "مميز"
      } في الوقت الحالي. تحقق من حدود الإعلانات المتاحة.`;

      console.log("Ad creation rejected:", errorMsg);

      return NextResponse.json(
        {
          success: false,
          error: errorMsg,
        },
        { status: 400 }
      );
    }

    // إنشاء الإعلان
    console.log("Creating ad in database...");

    // إزالة الحقول غير الموجودة في schema
    const { duration, ...cleanAdData } = adData;
    console.log("Clean ad data (after removing duration):", cleanAdData);

    const result = await prisma.ad.create({
      data: cleanAdData,
      include: {
        user: {
          select: { id: true, name: true, avatar: true },
        },
      },
    });

    console.log("Ad created successfully:", result.id);

    return NextResponse.json({
      success: true,
      data: result,
      message: "تم إنشاء الإعلان بنجاح",
    });
  } catch (error) {
    console.error("Error creating ad:", error);

    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { success: false, error: error.errors[0].message },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { success: false, error: "حدث خطأ في إنشاء الإعلان" },
      { status: 500 }
    );
  }
}

// جلب الإعلانات مع فلترة حسب النوع
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const page = parseInt(searchParams.get("page") || "1");
    const limit = parseInt(searchParams.get("limit") || "12");
    const adType = searchParams.get("adType"); // 'free', 'paid', 'promoted'
    const category = searchParams.get("category");
    const city = searchParams.get("city");
    const minPrice = searchParams.get("minPrice");
    const maxPrice = searchParams.get("maxPrice");
    const condition = searchParams.get("condition");
    const search = searchParams.get("search");
    const skip = (page - 1) * limit;

    const where: any = {
      isActive: true,
      OR: [
        { expiresAt: null }, // الإعلانات بدون تاريخ انتهاء
        { expiresAt: { gte: new Date() } }, // الإعلانات غير منتهية الصلاحية
      ],
    };

    // فلترة حسب نوع الإعلان
    if (adType) {
      where.adType = adType;
    }

    // فلترة حسب الفئة
    if (category) {
      where.category = category;
    }

    // فلترة حسب المدينة
    if (city) {
      where.city = city;
    }

    // فلترة حسب السعر
    if (minPrice || maxPrice) {
      where.price = {};
      if (minPrice) where.price.gte = parseFloat(minPrice);
      if (maxPrice) where.price.lte = parseFloat(maxPrice);
    }

    // فلترة حسب الحالة
    if (condition) {
      where.condition = condition;
    }

    // البحث في العنوان والوصف
    if (search) {
      where.OR = [
        { title: { contains: search, mode: "insensitive" } },
        { description: { contains: search, mode: "insensitive" } },
      ];
    }

    // ترتيب الإعلانات (المميزة أولاً)
    const orderBy = [
      { isPromoted: "desc" as const },
      { createdAt: "desc" as const },
    ];

    const [ads, total] = await Promise.all([
      prisma.ad.findMany({
        where,
        include: {
          user: {
            select: {
              id: true,
              name: true,
              avatar: true,
              ratingAverage: true,
              ratingCount: true,
            },
          },
        },
        orderBy,
        skip,
        take: limit,
      }),
      prisma.ad.count({ where }),
    ]);

    const totalPages = Math.ceil(total / limit);

    return NextResponse.json({
      success: true,
      data: ads,
      pagination: {
        page,
        limit,
        total,
        totalPages,
      },
    });
  } catch (error) {
    console.error("Error fetching ads:", error);
    return NextResponse.json(
      { success: false, error: "حدث خطأ في جلب الإعلانات" },
      { status: 500 }
    );
  }
}
