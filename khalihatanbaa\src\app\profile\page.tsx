"use client";

import { useState, useEffect } from "react";
import { useSession } from "next-auth/react";
import { useRouter } from "next/navigation";
import Link from "next/link";
import Image from "next/image";
import {
  UserIcon,
  PencilIcon,
  PlusIcon,
  ChatBubbleLeftRightIcon,
  HeartIcon,
  EyeIcon,
  CalendarIcon,
  MapPinIcon,
  EnvelopeIcon,
  PhoneIcon,
} from "@heroicons/react/24/outline";
import { Header } from "@/components/layout/Header";
import { Footer } from "@/components/layout/Footer";
import { FavoriteButton } from "@/components/ui/FavoriteButton";

interface UserProfile {
  id: string;
  name: string;
  email: string;
  avatar?: string;
  phone?: string;
  city?: string;
  createdAt: string;
  freeAdsCount: number;
  paidAdsCount: number;
  ratingAverage?: number;
  ratingCount?: number;
  _count: {
    ads: number;
    sentMessages: number;
    receivedMessages: number;
    favorites: number;
  };
}

interface UserAd {
  id: string;
  title: string;
  price: number;
  imageUrls: string[];
  category: string;
  city: string;
  views: number;
  contactsCount: number;
  isActive: boolean;
  createdAt: string;
  adType: string;
}

export default function ProfilePage() {
  const { data: session, status } = useSession();
  const router = useRouter();

  const [profile, setProfile] = useState<UserProfile | null>(null);
  const [userAds, setUserAds] = useState<UserAd[]>([]);
  const [favorites, setFavorites] = useState<any[]>([]);
  const [loading, setLoading] = useState(true);
  const [activeTab, setActiveTab] = useState<"ads" | "favorites" | "messages">(
    "ads"
  );
  const [error, setError] = useState("");

  useEffect(() => {
    if (status === "unauthenticated") {
      router.push("/auth/login");
      return;
    }

    if (status === "authenticated") {
      fetchProfile();
      fetchUserAds();
      fetchFavorites();
    }
  }, [status, router]);

  const fetchProfile = async () => {
    try {
      const response = await fetch("/api/user/profile");
      const data = await response.json();

      if (data.success) {
        setProfile(data.data);
      } else {
        setError(data.error || "حدث خطأ في جلب البيانات");
      }
    } catch (error) {
      console.error("Error fetching profile:", error);
      setError("حدث خطأ في الاتصال");
    }
  };

  const fetchUserAds = async () => {
    try {
      const response = await fetch("/api/user/ads");
      const data = await response.json();

      if (data.success) {
        setUserAds(data.data);
      }
    } catch (error) {
      console.error("Error fetching user ads:", error);
    } finally {
      setLoading(false);
    }
  };

  const fetchFavorites = async () => {
    try {
      const response = await fetch("/api/favorites");
      const data = await response.json();

      if (data.success) {
        setFavorites(data.data);
      }
    } catch (error) {
      console.error("Error fetching favorites:", error);
    }
  };

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString("ar-SY", {
      year: "numeric",
      month: "long",
      day: "numeric",
    });
  };

  const getAdStatusColor = (ad: UserAd) => {
    if (!ad.isActive) return "bg-gray-100 text-gray-600";
    if (ad.adType === "promoted") return "bg-yellow-100 text-yellow-800";
    if (ad.adType === "paid") return "bg-blue-100 text-blue-800";
    return "bg-green-100 text-green-800";
  };

  const getAdStatusText = (ad: UserAd) => {
    if (!ad.isActive) return "غير نشط";
    if (ad.adType === "promoted") return "مميز";
    if (ad.adType === "paid") return "مدفوع";
    return "مجاني";
  };

  if (status === "loading" || loading) {
    return (
      <div className="min-h-screen bg-gray-50">
        <Header />
        <div className="max-w-6xl mx-auto px-4 py-8">
          <div className="animate-pulse">
            <div className="bg-white rounded-lg shadow-sm p-6 mb-6">
              <div className="flex items-center gap-6">
                <div className="w-24 h-24 bg-gray-200 rounded-full"></div>
                <div className="flex-1">
                  <div className="h-6 bg-gray-200 rounded w-1/3 mb-2"></div>
                  <div className="h-4 bg-gray-200 rounded w-1/2 mb-2"></div>
                  <div className="h-4 bg-gray-200 rounded w-1/4"></div>
                </div>
              </div>
            </div>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              {[1, 2, 3].map((i) => (
                <div key={i} className="bg-white rounded-lg shadow-sm p-6">
                  <div className="h-4 bg-gray-200 rounded w-1/2 mb-4"></div>
                  <div className="h-8 bg-gray-200 rounded w-1/3"></div>
                </div>
              ))}
            </div>
          </div>
        </div>
        <Footer />
      </div>
    );
  }

  if (!profile) {
    return (
      <div className="min-h-screen bg-gray-50">
        <Header />
        <div className="max-w-6xl mx-auto px-4 py-8">
          <div className="text-center">
            <p className="text-red-600">
              {error || "حدث خطأ في تحميل البيانات"}
            </p>
          </div>
        </div>
        <Footer />
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <Header />

      <main className="max-w-6xl mx-auto px-4 py-8">
        {/* Profile Header */}
        <div className="bg-white rounded-lg shadow-sm p-6 mb-6">
          <div className="flex flex-col md:flex-row items-start md:items-center gap-6">
            {/* Avatar */}
            <div className="flex-shrink-0">
              {profile.avatar ? (
                <Image
                  src={profile.avatar}
                  alt={profile.name}
                  width={96}
                  height={96}
                  className="rounded-full object-cover"
                />
              ) : (
                <div className="w-24 h-24 bg-gray-200 rounded-full flex items-center justify-center">
                  <UserIcon className="h-12 w-12 text-gray-500" />
                </div>
              )}
            </div>

            {/* Profile Info */}
            <div className="flex-1">
              <div className="flex flex-col md:flex-row md:items-center md:justify-between mb-4">
                <div>
                  <h1 className="text-2xl font-bold text-gray-900 mb-2">
                    {profile.name}
                  </h1>
                  <div className="flex flex-wrap items-center gap-4 text-sm text-gray-600">
                    <div className="flex items-center gap-1">
                      <EnvelopeIcon className="h-4 w-4" />
                      {profile.email}
                    </div>
                    {profile.phone && (
                      <div className="flex items-center gap-1">
                        <PhoneIcon className="h-4 w-4" />
                        {profile.phone}
                      </div>
                    )}
                    {profile.city && (
                      <div className="flex items-center gap-1">
                        <MapPinIcon className="h-4 w-4" />
                        {profile.city}
                      </div>
                    )}
                    <div className="flex items-center gap-1">
                      <CalendarIcon className="h-4 w-4" />
                      عضو منذ {formatDate(profile.createdAt)}
                    </div>
                  </div>
                </div>

                <Link
                  href="/profile/edit"
                  className="mt-4 md:mt-0 inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
                >
                  <PencilIcon className="h-4 w-4 mr-2" />
                  تعديل الملف الشخصي
                </Link>
              </div>

              {/* Rating */}
              {profile.ratingCount && profile.ratingCount > 0 && (
                <div className="flex items-center gap-2 mb-4">
                  <div className="flex items-center">
                    {[1, 2, 3, 4, 5].map((star) => (
                      <svg
                        key={star}
                        className={`h-5 w-5 ${
                          star <= (profile.ratingAverage || 0)
                            ? "text-yellow-400"
                            : "text-gray-300"
                        }`}
                        fill="currentColor"
                        viewBox="0 0 20 20"
                      >
                        <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                      </svg>
                    ))}
                  </div>
                  <span className="text-sm text-gray-600">
                    ({profile.ratingAverage?.toFixed(1)} من{" "}
                    {profile.ratingCount} تقييم)
                  </span>
                </div>
              )}
            </div>
          </div>
        </div>

        {/* Stats Cards */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-6">
          <div className="bg-white rounded-lg shadow-sm p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600">إجمالي الإعلانات</p>
                <p className="text-2xl font-bold text-gray-900">
                  {profile._count.ads}
                </p>
              </div>
              <div className="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center">
                <PlusIcon className="h-6 w-6 text-blue-600" />
              </div>
            </div>
          </div>

          <div className="bg-white rounded-lg shadow-sm p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600">الرسائل</p>
                <p className="text-2xl font-bold text-gray-900">
                  {profile._count.sentMessages +
                    profile._count.receivedMessages}
                </p>
              </div>
              <div className="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center">
                <ChatBubbleLeftRightIcon className="h-6 w-6 text-green-600" />
              </div>
            </div>
          </div>

          <div className="bg-white rounded-lg shadow-sm p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600">المفضلة</p>
                <p className="text-2xl font-bold text-gray-900">
                  {profile._count.favorites}
                </p>
              </div>
              <div className="w-12 h-12 bg-red-100 rounded-lg flex items-center justify-center">
                <HeartIcon className="h-6 w-6 text-red-600" />
              </div>
            </div>
          </div>

          <div className="bg-white rounded-lg shadow-sm p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600">الإعلانات المجانية</p>
                <p className="text-2xl font-bold text-gray-900">
                  {profile.freeAdsCount}
                </p>
              </div>
              <div className="w-12 h-12 bg-yellow-100 rounded-lg flex items-center justify-center">
                <EyeIcon className="h-6 w-6 text-yellow-600" />
              </div>
            </div>
          </div>
        </div>

        {/* Tabs */}
        <div className="bg-white rounded-lg shadow-sm">
          <div className="border-b border-gray-200">
            <nav className="flex">
              <button
                onClick={() => setActiveTab("ads")}
                className={`px-6 py-4 text-sm font-medium border-b-2 ${
                  activeTab === "ads"
                    ? "border-blue-600 text-blue-600"
                    : "border-transparent text-gray-500 hover:text-gray-700"
                }`}
              >
                إعلاناتي ({userAds.length})
              </button>
              <button
                onClick={() => setActiveTab("favorites")}
                className={`px-6 py-4 text-sm font-medium border-b-2 ${
                  activeTab === "favorites"
                    ? "border-blue-600 text-blue-600"
                    : "border-transparent text-gray-500 hover:text-gray-700"
                }`}
              >
                المفضلة ({profile._count.favorites})
              </button>
              <button
                onClick={() => setActiveTab("messages")}
                className={`px-6 py-4 text-sm font-medium border-b-2 ${
                  activeTab === "messages"
                    ? "border-blue-600 text-blue-600"
                    : "border-transparent text-gray-500 hover:text-gray-700"
                }`}
              >
                الرسائل
              </button>
            </nav>
          </div>

          <div className="p-6">
            {activeTab === "ads" && (
              <div>
                {userAds.length === 0 ? (
                  <div className="text-center py-12">
                    <PlusIcon className="h-16 w-16 text-gray-300 mx-auto mb-4" />
                    <h3 className="text-lg font-medium text-gray-900 mb-2">
                      لا توجد إعلانات
                    </h3>
                    <p className="text-gray-600 mb-6">ابدأ بنشر إعلانك الأول</p>
                    <Link
                      href="/ads/create"
                      className="inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
                    >
                      <PlusIcon className="h-4 w-4 mr-2" />
                      إنشاء إعلان جديد
                    </Link>
                  </div>
                ) : (
                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                    {userAds.map((ad) => (
                      <div
                        key={ad.id}
                        className="border border-gray-200 rounded-lg overflow-hidden hover:shadow-md transition-shadow"
                      >
                        <Link href={`/ads/${ad.id}`}>
                          <div className="aspect-w-16 aspect-h-9">
                            {ad.imageUrls?.[0] ? (
                              <Image
                                src={ad.imageUrls[0]}
                                alt={ad.title}
                                width={300}
                                height={200}
                                className="w-full h-48 object-cover"
                              />
                            ) : (
                              <div className="w-full h-48 bg-gray-200 flex items-center justify-center">
                                <span className="text-gray-500">
                                  لا توجد صورة
                                </span>
                              </div>
                            )}
                          </div>
                        </Link>

                        <div className="p-4">
                          <div className="flex items-center justify-between mb-2">
                            <span
                              className={`px-2 py-1 text-xs rounded-full ${getAdStatusColor(
                                ad
                              )}`}
                            >
                              {getAdStatusText(ad)}
                            </span>
                            <span className="text-sm text-gray-500">
                              {formatDate(ad.createdAt)}
                            </span>
                          </div>

                          <Link href={`/ads/${ad.id}`}>
                            <h3 className="font-medium text-gray-900 mb-2 hover:text-blue-600 line-clamp-2">
                              {ad.title}
                            </h3>
                          </Link>

                          <p className="text-lg font-bold text-blue-600 mb-2">
                            {ad.price.toLocaleString()} ل.س
                          </p>

                          <div className="flex items-center justify-between text-sm text-gray-500">
                            <span>{ad.city}</span>
                            <div className="flex items-center gap-4">
                              <div className="flex items-center gap-1">
                                <EyeIcon className="h-4 w-4" />
                                {ad.views}
                              </div>
                              <div className="flex items-center gap-1">
                                <ChatBubbleLeftRightIcon className="h-4 w-4" />
                                {ad.contactsCount}
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                )}
              </div>
            )}

            {activeTab === "favorites" && (
              <div>
                {favorites.length === 0 ? (
                  <div className="text-center py-12">
                    <HeartIcon className="h-16 w-16 text-gray-300 mx-auto mb-4" />
                    <h3 className="text-lg font-medium text-gray-900 mb-2">
                      لا توجد إعلانات مفضلة
                    </h3>
                    <p className="text-gray-600 mb-6">
                      ابدأ بإضافة إعلانات إلى المفضلة
                    </p>
                    <Link
                      href="/"
                      className="inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
                    >
                      تصفح الإعلانات
                    </Link>
                  </div>
                ) : (
                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                    {favorites.map((favorite) => (
                      <div
                        key={favorite.id}
                        className="border border-gray-200 rounded-lg overflow-hidden hover:shadow-md transition-shadow"
                      >
                        <Link href={`/ads/${favorite.ad.id}`}>
                          <div className="aspect-w-16 aspect-h-9">
                            {favorite.ad.imageUrls?.[0] ? (
                              <Image
                                src={favorite.ad.imageUrls[0]}
                                alt={favorite.ad.title}
                                width={300}
                                height={200}
                                className="w-full h-48 object-cover"
                              />
                            ) : (
                              <div className="w-full h-48 bg-gray-200 flex items-center justify-center">
                                <span className="text-gray-500">
                                  لا توجد صورة
                                </span>
                              </div>
                            )}
                          </div>
                        </Link>

                        <div className="p-4">
                          <div className="flex items-center justify-between mb-2">
                            <span className="text-sm text-gray-500">
                              {formatDate(favorite.createdAt)}
                            </span>
                            <FavoriteButton
                              adId={favorite.ad.id}
                              initialIsFavorite={true}
                              size="sm"
                            />
                          </div>

                          <Link href={`/ads/${favorite.ad.id}`}>
                            <h3 className="font-medium text-gray-900 mb-2 hover:text-blue-600 line-clamp-2">
                              {favorite.ad.title}
                            </h3>
                          </Link>

                          <p className="text-lg font-bold text-blue-600 mb-2">
                            {favorite.ad.price.toLocaleString()} ل.س
                          </p>

                          <div className="flex items-center justify-between text-sm text-gray-500">
                            <span>{favorite.ad.city}</span>
                            <span>بواسطة: {favorite.ad.user?.name}</span>
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                )}
              </div>
            )}

            {activeTab === "messages" && (
              <div className="text-center py-12">
                <ChatBubbleLeftRightIcon className="h-16 w-16 text-gray-300 mx-auto mb-4" />
                <h3 className="text-lg font-medium text-gray-900 mb-2">
                  الرسائل
                </h3>
                <p className="text-gray-600 mb-6">
                  تواصل مع البائعين والمشترين
                </p>
                <Link
                  href="/messages"
                  className="inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
                >
                  <ChatBubbleLeftRightIcon className="h-4 w-4 mr-2" />
                  عرض الرسائل
                </Link>
              </div>
            )}
          </div>
        </div>
      </main>

      <Footer />
    </div>
  );
}
