import { NextRequest, NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import { authOptions } from "@/lib/auth";
import { prisma } from "@/lib/prisma";
import { z } from "zod";

// مخطط التحقق من إرسال رسالة
const sendMessageSchema = z.object({
  toId: z.string().min(1, "معرف المستقبل مطلوب"),
  adId: z.string().min(1, "معرف الإعلان مطلوب"), // جعل adId مطلوب
  content: z
    .string()
    .min(1, "محتوى الرسالة مطلوب")
    .max(1000, "الرسالة طويلة جداً"),
});

// Cache للاتصالات النشطة
const activeConnections = new Map<string, Set<string>>();

// إضافة مستخدم للاتصالات النشطة
export function addActiveConnection(userId: string, connectionId: string) {
  if (!activeConnections.has(userId)) {
    activeConnections.set(userId, new Set());
  }
  activeConnections.get(userId)!.add(connectionId);
}

// إزالة مستخدم من الاتصالات النشطة
export function removeActiveConnection(userId: string, connectionId: string) {
  const connections = activeConnections.get(userId);
  if (connections) {
    connections.delete(connectionId);
    if (connections.size === 0) {
      activeConnections.delete(userId);
    }
  }
}

// التحقق من وجود مستخدم متصل
export function isUserOnline(userId: string): boolean {
  return (
    activeConnections.has(userId) && activeConnections.get(userId)!.size > 0
  );
}

// جلب المحادثات
export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user?.id) {
      return NextResponse.json(
        { success: false, error: "يجب تسجيل الدخول أولاً" },
        { status: 401 }
      );
    }

    const { searchParams } = new URL(request.url);
    const conversationWith = searchParams.get("with");
    const adId = searchParams.get("adId") || searchParams.get("ad"); // دعم كلا الشكلين
    const lastMessageId = searchParams.get("lastMessageId");
    const limit = Math.min(parseInt(searchParams.get("limit") || "10"), 10); // حد أقصى 10 رسائل للتحميل السريع

    if (conversationWith) {
      // التحقق من عدم كون المستخدم يحاول مراسلة نفسه
      if (conversationWith === session.user.id) {
        return NextResponse.json(
          { success: false, error: "لا يمكن مراسلة نفسك" },
          { status: 400 }
        );
      }

      // جلب محادثة محددة مع pagination
      const whereClause: any = {
        OR: [
          { fromId: session.user.id, toId: conversationWith },
          { fromId: conversationWith, toId: session.user.id },
        ],
        ...(adId && { adId }),
      };

      // إضافة cursor pagination للرسائل الأقدم
      if (lastMessageId) {
        whereClause.id = {
          lt: lastMessageId, // للحصول على الرسائل الأقدم من النقطة المرجعية
        };
      }

      // إضافة فلترة زمنية فقط إذا لم يكن هناك lastMessageId (التحميل الأول فقط)
      if (!lastMessageId) {
        const thirtyDaysAgo = new Date();
        thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);
        whereClause.createdAt = {
          gte: thirtyDaysAgo,
        };
      }

      // جلب الرسائل بدون include لتقليل حجم البيانات
      const messages = await prisma.message.findMany({
        where: whereClause,
        select: {
          id: true,
          content: true,
          isRead: true,
          createdAt: true,
          fromId: true,
          toId: true,
          adId: true,
        },
        orderBy: { createdAt: "desc" }, // ترتيب تنازلي دائماً للحصول على الأحدث أولاً
        take: limit,
      });

      // جلب بيانات المستخدمين والإعلانات بشكل منفصل
      const userIds = [
        ...new Set([
          ...messages.map((m) => m.fromId),
          ...messages.map((m) => m.toId),
        ]),
      ];
      const adIds = [...new Set(messages.map((m) => m.adId).filter(Boolean))];

      const [users, ads] = await Promise.all([
        prisma.user.findMany({
          where: { id: { in: userIds } },
          select: { id: true, name: true, avatar: true },
        }),
        adIds.length > 0
          ? prisma.ad.findMany({
              where: { id: { in: adIds } },
              select: { id: true, title: true, price: true, imageUrls: true },
            })
          : [],
      ]);

      // دمج البيانات مع الحفاظ على الترتيب التنازلي
      const messagesWithData = messages.map((message) => ({
        ...message,
        from: users.find((u) => u.id === message.fromId),
        to: users.find((u) => u.id === message.toId),
        ad: message.adId ? ads.find((a) => a.id === message.adId) : null,
      }));

      // تحديد الرسائل كمقروءة فقط إذا لم يكن هناك lastMessageId (أول تحميل)
      if (!lastMessageId) {
        await prisma.message.updateMany({
          where: {
            fromId: conversationWith,
            toId: session.user.id,
            isRead: false,
          },
          data: { isRead: true },
        });
      }

      // التحقق من وجود رسائل أقدم (محسن ودقيق)
      let hasMore = false;
      if (messages.length > 0) {
        const oldestMessageId = messages[messages.length - 1].id;

        // بناء شروط البحث بنفس منطق الاستعلام الأصلي
        const whereClause: any = {
          OR: [
            { fromId: session.user.id, toId: conversationWith },
            { fromId: conversationWith, toId: session.user.id },
          ],
          id: { lt: oldestMessageId },
        };

        // إضافة adId إذا كان موجود
        if (adId) {
          whereClause.adId = adId;
        }

        const olderMessagesCount = await prisma.message.count({
          where: whereClause,
        });

        hasMore = olderMessagesCount > 0;
      }

      return NextResponse.json({
        success: true,
        data: messagesWithData,
        hasMore,
        // آخر رسالة هي الأقدم في المجموعة (للـ pagination)
        lastMessageId:
          messages.length > 0 ? messages[messages.length - 1].id : null,
      });
    } else {
      // جلب قائمة المحادثات - بطريقة محسنة وسريعة
      const threeDaysAgo = new Date();
      threeDaysAgo.setDate(threeDaysAgo.getDate() - 3);

      const recentMessages = await prisma.message.findMany({
        where: {
          OR: [{ fromId: session.user.id }, { toId: session.user.id }],
          createdAt: {
            gte: threeDaysAgo, // آخر 3 أيام للمحادثات
          },
          // منع الرسائل للنفس
          NOT: {
            AND: {
              fromId: session.user.id,
              toId: session.user.id,
            },
          },
          // فقط المحادثات التي لها adId
          adId: {
            not: null,
          },
        },
        include: {
          from: {
            select: { id: true, name: true, avatar: true },
          },
          to: {
            select: { id: true, name: true, avatar: true },
          },
          ad: {
            select: { id: true, title: true, price: true },
          },
        },
        orderBy: { createdAt: "desc" },
        take: 30, // حد أقصى 30 رسالة حديثة للسرعة
      });

      // تجميع المحادثات حسب الشخص + الإعلان (نظام محادثات متعددة محترف)
      const groupedConversations = recentMessages.reduce((acc, message) => {
        const otherUserId =
          message.fromId === session.user.id ? message.toId : message.fromId;
        const key = `${otherUserId}-${message.adId}`; // الشخص + الإعلان = محادثة منفصلة

        if (!acc[key] || acc[key].createdAt < message.createdAt) {
          acc[key] = {
            ...message,
            otherUser:
              message.fromId === session.user.id ? message.to : message.from,
            unreadCount: 0,
            isOnline: isUserOnline(otherUserId),
          };
        }

        return acc;
      }, {} as any);

      // حساب الرسائل غير المقروءة لكل محادثة (محددة بالإعلان)
      const conversationsList = Object.values(groupedConversations) as any[];

      for (const conversation of conversationsList) {
        const unreadCount = await prisma.message.count({
          where: {
            fromId: conversation.otherUser.id,
            toId: session.user.id,
            adId: conversation.adId, // تحديد الإعلان للدقة
            isRead: false,
          },
        });
        conversation.unreadCount = unreadCount;
      }

      return NextResponse.json({
        success: true,
        data: conversationsList,
      });
    }
  } catch (error) {
    console.error("Error fetching messages:", error);
    return NextResponse.json(
      { success: false, error: "حدث خطأ في جلب الرسائل" },
      { status: 500 }
    );
  }
}

// إرسال رسالة جديدة
export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user?.id) {
      return NextResponse.json(
        { success: false, error: "يجب تسجيل الدخول أولاً" },
        { status: 401 }
      );
    }

    const body = await request.json();
    const validatedData = sendMessageSchema.parse(body);

    // التحقق من عدم إرسال رسالة للنفس
    if (validatedData.toId === session.user.id) {
      return NextResponse.json(
        { success: false, error: "لا يمكن إرسال رسالة لنفسك" },
        { status: 400 }
      );
    }

    // التحقق من وجود المستقبل
    const recipient = await prisma.user.findUnique({
      where: { id: validatedData.toId },
    });

    if (!recipient) {
      return NextResponse.json(
        { success: false, error: "المستخدم المستقبل غير موجود" },
        { status: 404 }
      );
    }

    // التحقق من وجود الإعلان
    const ad = await prisma.ad.findUnique({
      where: { id: validatedData.adId },
    });

    if (!ad) {
      return NextResponse.json(
        { success: false, error: "الإعلان غير موجود" },
        { status: 404 }
      );
    }

    // إنشاء الرسالة
    const message = await prisma.message.create({
      data: {
        fromId: session.user.id,
        toId: validatedData.toId,
        adId: validatedData.adId,
        content: validatedData.content,
      },
      include: {
        from: {
          select: { id: true, name: true, avatar: true },
        },
        to: {
          select: { id: true, name: true, avatar: true },
        },
        ad: {
          select: { id: true, title: true, price: true, imageUrls: true },
        },
      },
    });

    // إرسال تحديث لحظي للمستقبل
    try {
      const { sendMessageToUser } = await import("./sse/route");
      sendMessageToUser(validatedData.toId, {
        type: "new_message",
        data: message,
        timestamp: new Date().toISOString(),
      });
    } catch (error) {
      console.error("Error sending real-time update:", error);
      // لا نريد أن يفشل إرسال الرسالة بسبب مشكلة في التحديث اللحظي
    }

    return NextResponse.json({
      success: true,
      data: message,
      message: "تم إرسال الرسالة بنجاح",
    });
  } catch (error) {
    console.error("Error sending message:", error);

    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { success: false, error: error.errors[0].message },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { success: false, error: "حدث خطأ في إرسال الرسالة" },
      { status: 500 }
    );
  }
}
