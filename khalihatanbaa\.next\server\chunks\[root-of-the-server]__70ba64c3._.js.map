{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 148, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/kaliha/khalihatan<PERSON>a/src/lib/prisma.ts"], "sourcesContent": ["import { PrismaClient } from '@prisma/client'\n\nconst globalForPrisma = globalThis as unknown as {\n  prisma: PrismaClient | undefined\n}\n\nexport const prisma =\n  globalForPrisma.prisma ??\n  new PrismaClient({\n    log: ['query'],\n  })\n\nif (process.env.NODE_ENV !== 'production') globalForPrisma.prisma = prisma\n"], "names": [], "mappings": ";;;AAAA;;AAEA,MAAM,kBAAkB;AAIjB,MAAM,SACX,gBAAgB,MAAM,IACtB,IAAI,6HAAA,CAAA,eAAY,CAAC;IACf,KAAK;QAAC;KAAQ;AAChB;AAEF,wCAA2C,gBAAgB,MAAM,GAAG", "debugId": null}}, {"offset": {"line": 166, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/kaliha/khalihatanbaa/src/lib/auth.ts"], "sourcesContent": ["import { NextAuthOptions } from \"next-auth\";\nimport Credentials<PERSON>rovider from \"next-auth/providers/credentials\";\nimport bcrypt from \"bcryptjs\";\nimport { prisma } from \"./prisma\";\n\nexport const authOptions: NextAuthOptions = {\n  providers: [\n    CredentialsProvider({\n      name: \"credentials\",\n      credentials: {\n        emailOrPhone: {\n          label: \"البريد الإلكتروني أو رقم الهاتف\",\n          type: \"text\",\n        },\n        password: { label: \"كلمة المرور\", type: \"password\" },\n      },\n      async authorize(credentials) {\n        if (!credentials?.emailOrPhone || !credentials?.password) {\n          return null;\n        }\n\n        // البحث عن المستخدم بالبريد الإلكتروني أو رقم الهاتف\n        const user = await prisma.user.findFirst({\n          where: {\n            OR: [\n              { email: credentials.emailOrPhone },\n              { phone: credentials.emailOrPhone },\n            ],\n            isActive: true,\n          },\n        });\n\n        if (!user) {\n          return null;\n        }\n\n        // التحقق من كلمة المرور\n        const isPasswordValid = await bcrypt.compare(\n          credentials.password,\n          user.passwordHash\n        );\n\n        if (!isPasswordValid) {\n          return null;\n        }\n\n        return {\n          id: user.id,\n          name: user.name,\n          email: user.email,\n          phone: user.phone,\n          role: user.role,\n          avatar: user.avatar || undefined,\n        };\n      },\n    }),\n  ],\n  session: {\n    strategy: \"jwt\",\n  },\n  callbacks: {\n    async jwt({ token, user }) {\n      if (user) {\n        token.role = user.role;\n        token.phone = user.phone;\n        token.avatar = user.avatar;\n      }\n      return token;\n    },\n    async session({ session, token }) {\n      if (token) {\n        session.user.id = token.sub!;\n        session.user.role = token.role as string;\n        session.user.phone = token.phone as string;\n        session.user.avatar = token.avatar as string;\n      }\n      return session;\n    },\n  },\n  pages: {\n    signIn: \"/auth/login\",\n    // signUp: \"/auth/register\", // غير مدعوم في NextAuth\n  },\n};\n"], "names": [], "mappings": ";;;AACA;AACA;AACA;;;;AAEO,MAAM,cAA+B;IAC1C,WAAW;QACT,CAAA,GAAA,0JAAA,CAAA,UAAmB,AAAD,EAAE;YAClB,MAAM;YACN,aAAa;gBACX,cAAc;oBACZ,OAAO;oBACP,MAAM;gBACR;gBACA,UAAU;oBAAE,OAAO;oBAAe,MAAM;gBAAW;YACrD;YACA,MAAM,WAAU,WAAW;gBACzB,IAAI,CAAC,aAAa,gBAAgB,CAAC,aAAa,UAAU;oBACxD,OAAO;gBACT;gBAEA,qDAAqD;gBACrD,MAAM,OAAO,MAAM,sHAAA,CAAA,SAAM,CAAC,IAAI,CAAC,SAAS,CAAC;oBACvC,OAAO;wBACL,IAAI;4BACF;gCAAE,OAAO,YAAY,YAAY;4BAAC;4BAClC;gCAAE,OAAO,YAAY,YAAY;4BAAC;yBACnC;wBACD,UAAU;oBACZ;gBACF;gBAEA,IAAI,CAAC,MAAM;oBACT,OAAO;gBACT;gBAEA,wBAAwB;gBACxB,MAAM,kBAAkB,MAAM,mIAAA,CAAA,UAAM,CAAC,OAAO,CAC1C,YAAY,QAAQ,EACpB,KAAK,YAAY;gBAGnB,IAAI,CAAC,iBAAiB;oBACpB,OAAO;gBACT;gBAEA,OAAO;oBACL,IAAI,KAAK,EAAE;oBACX,MAAM,KAAK,IAAI;oBACf,OAAO,KAAK,KAAK;oBACjB,OAAO,KAAK,KAAK;oBACjB,MAAM,KAAK,IAAI;oBACf,QAAQ,KAAK,MAAM,IAAI;gBACzB;YACF;QACF;KACD;IACD,SAAS;QACP,UAAU;IACZ;IACA,WAAW;QACT,MAAM,KAAI,EAAE,KAAK,EAAE,IAAI,EAAE;YACvB,IAAI,MAAM;gBACR,MAAM,IAAI,GAAG,KAAK,IAAI;gBACtB,MAAM,KAAK,GAAG,KAAK,KAAK;gBACxB,MAAM,MAAM,GAAG,KAAK,MAAM;YAC5B;YACA,OAAO;QACT;QACA,MAAM,SAAQ,EAAE,OAAO,EAAE,KAAK,EAAE;YAC9B,IAAI,OAAO;gBACT,QAAQ,IAAI,CAAC,EAAE,GAAG,MAAM,GAAG;gBAC3B,QAAQ,IAAI,CAAC,IAAI,GAAG,MAAM,IAAI;gBAC9B,QAAQ,IAAI,CAAC,KAAK,GAAG,MAAM,KAAK;gBAChC,QAAQ,IAAI,CAAC,MAAM,GAAG,MAAM,MAAM;YACpC;YACA,OAAO;QACT;IACF;IACA,OAAO;QACL,QAAQ;IAEV;AACF", "debugId": null}}, {"offset": {"line": 258, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/kaliha/khalihatanbaa/src/app/api/messages/route.ts"], "sourcesContent": ["import { NextRequest, NextResponse } from \"next/server\";\nimport { getServerSession } from \"next-auth\";\nimport { authOptions } from \"@/lib/auth\";\nimport { prisma } from \"@/lib/prisma\";\nimport { z } from \"zod\";\n\n// مخطط التحقق من إرسال رسالة\nconst sendMessageSchema = z.object({\n  toId: z.string().min(1, \"معرف المستقبل مطلوب\"),\n  adId: z.string().min(1, \"معرف الإعلان مطلوب\"), // جعل adId مطلوب\n  content: z\n    .string()\n    .min(1, \"محتوى الرسالة مطلوب\")\n    .max(1000, \"الرسالة طويلة جداً\"),\n});\n\n// Cache للاتصالات النشطة\nconst activeConnections = new Map<string, Set<string>>();\n\n// إضافة مستخدم للاتصالات النشطة\nexport function addActiveConnection(userId: string, connectionId: string) {\n  if (!activeConnections.has(userId)) {\n    activeConnections.set(userId, new Set());\n  }\n  activeConnections.get(userId)!.add(connectionId);\n}\n\n// إزالة مستخدم من الاتصالات النشطة\nexport function removeActiveConnection(userId: string, connectionId: string) {\n  const connections = activeConnections.get(userId);\n  if (connections) {\n    connections.delete(connectionId);\n    if (connections.size === 0) {\n      activeConnections.delete(userId);\n    }\n  }\n}\n\n// التحقق من وجود مستخدم متصل\nexport function isUserOnline(userId: string): boolean {\n  return (\n    activeConnections.has(userId) && activeConnections.get(userId)!.size > 0\n  );\n}\n\n// جلب المحادثات\nexport async function GET(request: NextRequest) {\n  try {\n    const session = await getServerSession(authOptions);\n    if (!session?.user?.id) {\n      return NextResponse.json(\n        { success: false, error: \"يجب تسجيل الدخول أولاً\" },\n        { status: 401 }\n      );\n    }\n\n    const { searchParams } = new URL(request.url);\n    const conversationWith = searchParams.get(\"with\");\n    const adId = searchParams.get(\"adId\") || searchParams.get(\"ad\"); // دعم كلا الشكلين\n    const lastMessageId = searchParams.get(\"lastMessageId\");\n    const limit = Math.min(parseInt(searchParams.get(\"limit\") || \"10\"), 10); // حد أقصى 10 رسائل للتحميل السريع\n\n    if (conversationWith) {\n      // التحقق من عدم كون المستخدم يحاول مراسلة نفسه\n      if (conversationWith === session.user.id) {\n        return NextResponse.json(\n          { success: false, error: \"لا يمكن مراسلة نفسك\" },\n          { status: 400 }\n        );\n      }\n\n      // جلب محادثة محددة مع pagination\n      const whereClause: any = {\n        OR: [\n          { fromId: session.user.id, toId: conversationWith },\n          { fromId: conversationWith, toId: session.user.id },\n        ],\n        ...(adId && { adId }),\n      };\n\n      // إضافة cursor pagination للرسائل الأقدم\n      if (lastMessageId) {\n        whereClause.id = {\n          lt: lastMessageId, // للحصول على الرسائل الأقدم من النقطة المرجعية\n        };\n      }\n\n      // إضافة فلترة زمنية فقط إذا لم يكن هناك lastMessageId (التحميل الأول فقط)\n      if (!lastMessageId) {\n        const thirtyDaysAgo = new Date();\n        thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);\n        whereClause.createdAt = {\n          gte: thirtyDaysAgo,\n        };\n      }\n\n      // جلب الرسائل بدون include لتقليل حجم البيانات\n      const messages = await prisma.message.findMany({\n        where: whereClause,\n        select: {\n          id: true,\n          content: true,\n          isRead: true,\n          createdAt: true,\n          fromId: true,\n          toId: true,\n          adId: true,\n        },\n        orderBy: { createdAt: \"desc\" }, // ترتيب تنازلي دائماً للحصول على الأحدث أولاً\n        take: limit,\n      });\n\n      // جلب بيانات المستخدمين والإعلانات بشكل منفصل\n      const userIds = [\n        ...new Set([\n          ...messages.map((m) => m.fromId),\n          ...messages.map((m) => m.toId),\n        ]),\n      ];\n      const adIds = [...new Set(messages.map((m) => m.adId).filter(Boolean))];\n\n      const [users, ads] = await Promise.all([\n        prisma.user.findMany({\n          where: { id: { in: userIds } },\n          select: { id: true, name: true, avatar: true },\n        }),\n        adIds.length > 0\n          ? prisma.ad.findMany({\n              where: { id: { in: adIds } },\n              select: { id: true, title: true, price: true, imageUrls: true },\n            })\n          : [],\n      ]);\n\n      // دمج البيانات مع الحفاظ على الترتيب التنازلي\n      const messagesWithData = messages.map((message) => ({\n        ...message,\n        from: users.find((u) => u.id === message.fromId),\n        to: users.find((u) => u.id === message.toId),\n        ad: message.adId ? ads.find((a) => a.id === message.adId) : null,\n      }));\n\n      // تحديد الرسائل كمقروءة فقط إذا لم يكن هناك lastMessageId (أول تحميل)\n      if (!lastMessageId) {\n        await prisma.message.updateMany({\n          where: {\n            fromId: conversationWith,\n            toId: session.user.id,\n            isRead: false,\n          },\n          data: { isRead: true },\n        });\n      }\n\n      // التحقق من وجود رسائل أقدم\n      let hasMore = false;\n      if (messages.length === limit) {\n        const oldestMessageId = messages[messages.length - 1].id;\n        const olderMessagesCount = await prisma.message.count({\n          where: {\n            OR: [\n              { fromId: session.user.id, toId: conversationWith },\n              { fromId: conversationWith, toId: session.user.id },\n            ],\n            ...(adId && { adId }),\n            id: { lt: oldestMessageId },\n          },\n        });\n        hasMore = olderMessagesCount > 0;\n      }\n\n      return NextResponse.json({\n        success: true,\n        data: messagesWithData,\n        hasMore,\n        // آخر رسالة هي الأقدم في المجموعة (للـ pagination)\n        lastMessageId:\n          messages.length > 0 ? messages[messages.length - 1].id : null,\n      });\n    } else {\n      // جلب قائمة المحادثات - بطريقة محسنة وسريعة\n      const threeDaysAgo = new Date();\n      threeDaysAgo.setDate(threeDaysAgo.getDate() - 3);\n\n      const recentMessages = await prisma.message.findMany({\n        where: {\n          OR: [{ fromId: session.user.id }, { toId: session.user.id }],\n          createdAt: {\n            gte: threeDaysAgo, // آخر 3 أيام للمحادثات\n          },\n          // منع الرسائل للنفس\n          NOT: {\n            AND: {\n              fromId: session.user.id,\n              toId: session.user.id,\n            },\n          },\n          // فقط المحادثات التي لها adId\n          adId: {\n            not: null,\n          },\n        },\n        include: {\n          from: {\n            select: { id: true, name: true, avatar: true },\n          },\n          to: {\n            select: { id: true, name: true, avatar: true },\n          },\n          ad: {\n            select: { id: true, title: true, price: true },\n          },\n        },\n        orderBy: { createdAt: \"desc\" },\n        take: 30, // حد أقصى 30 رسالة حديثة للسرعة\n      });\n\n      // تجميع المحادثات حسب الشخص فقط (بدون الإعلان)\n      const groupedConversations = recentMessages.reduce((acc, message) => {\n        const otherUserId =\n          message.fromId === session.user.id ? message.toId : message.fromId;\n        const key = otherUserId; // فقط معرف الشخص، بدون الإعلان\n\n        if (!acc[key] || acc[key].createdAt < message.createdAt) {\n          acc[key] = {\n            ...message,\n            otherUser:\n              message.fromId === session.user.id ? message.to : message.from,\n            unreadCount: 0,\n            isOnline: isUserOnline(otherUserId),\n          };\n        }\n\n        return acc;\n      }, {} as any);\n\n      // حساب الرسائل غير المقروءة لكل محادثة\n      const conversationsList = Object.values(groupedConversations) as any[];\n\n      for (const conversation of conversationsList) {\n        const unreadCount = await prisma.message.count({\n          where: {\n            fromId: conversation.otherUser.id,\n            toId: session.user.id,\n            isRead: false,\n          },\n        });\n        conversation.unreadCount = unreadCount;\n      }\n\n      return NextResponse.json({\n        success: true,\n        data: conversationsList,\n      });\n    }\n  } catch (error) {\n    console.error(\"Error fetching messages:\", error);\n    return NextResponse.json(\n      { success: false, error: \"حدث خطأ في جلب الرسائل\" },\n      { status: 500 }\n    );\n  }\n}\n\n// إرسال رسالة جديدة\nexport async function POST(request: NextRequest) {\n  try {\n    const session = await getServerSession(authOptions);\n    if (!session?.user?.id) {\n      return NextResponse.json(\n        { success: false, error: \"يجب تسجيل الدخول أولاً\" },\n        { status: 401 }\n      );\n    }\n\n    const body = await request.json();\n    const validatedData = sendMessageSchema.parse(body);\n\n    // التحقق من عدم إرسال رسالة للنفس\n    if (validatedData.toId === session.user.id) {\n      return NextResponse.json(\n        { success: false, error: \"لا يمكن إرسال رسالة لنفسك\" },\n        { status: 400 }\n      );\n    }\n\n    // التحقق من وجود المستقبل\n    const recipient = await prisma.user.findUnique({\n      where: { id: validatedData.toId },\n    });\n\n    if (!recipient) {\n      return NextResponse.json(\n        { success: false, error: \"المستخدم المستقبل غير موجود\" },\n        { status: 404 }\n      );\n    }\n\n    // التحقق من وجود الإعلان\n    const ad = await prisma.ad.findUnique({\n      where: { id: validatedData.adId },\n    });\n\n    if (!ad) {\n      return NextResponse.json(\n        { success: false, error: \"الإعلان غير موجود\" },\n        { status: 404 }\n      );\n    }\n\n    // إنشاء الرسالة\n    const message = await prisma.message.create({\n      data: {\n        fromId: session.user.id,\n        toId: validatedData.toId,\n        adId: validatedData.adId,\n        content: validatedData.content,\n      },\n      include: {\n        from: {\n          select: { id: true, name: true, avatar: true },\n        },\n        to: {\n          select: { id: true, name: true, avatar: true },\n        },\n        ad: {\n          select: { id: true, title: true, price: true, imageUrls: true },\n        },\n      },\n    });\n\n    // إرسال تحديث لحظي للمستقبل\n    try {\n      const { sendMessageToUser } = await import(\"./sse/route\");\n      sendMessageToUser(validatedData.toId, {\n        type: \"new_message\",\n        data: message,\n        timestamp: new Date().toISOString(),\n      });\n    } catch (error) {\n      console.error(\"Error sending real-time update:\", error);\n      // لا نريد أن يفشل إرسال الرسالة بسبب مشكلة في التحديث اللحظي\n    }\n\n    return NextResponse.json({\n      success: true,\n      data: message,\n      message: \"تم إرسال الرسالة بنجاح\",\n    });\n  } catch (error) {\n    console.error(\"Error sending message:\", error);\n\n    if (error instanceof z.ZodError) {\n      return NextResponse.json(\n        { success: false, error: error.errors[0].message },\n        { status: 400 }\n      );\n    }\n\n    return NextResponse.json(\n      { success: false, error: \"حدث خطأ في إرسال الرسالة\" },\n      { status: 500 }\n    );\n  }\n}\n"], "names": [], "mappings": ";;;;;;;AAAA;AACA;AACA;AACA;AACA;AAAA;;;;;;AAEA,6BAA6B;AAC7B,MAAM,oBAAoB,mLAAA,CAAA,IAAC,CAAC,MAAM,CAAC;IACjC,MAAM,mLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG;IACxB,MAAM,mLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG;IACxB,SAAS,mLAAA,CAAA,IAAC,CACP,MAAM,GACN,GAAG,CAAC,GAAG,uBACP,GAAG,CAAC,MAAM;AACf;AAEA,yBAAyB;AACzB,MAAM,oBAAoB,IAAI;AAGvB,SAAS,oBAAoB,MAAc,EAAE,YAAoB;IACtE,IAAI,CAAC,kBAAkB,GAAG,CAAC,SAAS;QAClC,kBAAkB,GAAG,CAAC,QAAQ,IAAI;IACpC;IACA,kBAAkB,GAAG,CAAC,QAAS,GAAG,CAAC;AACrC;AAGO,SAAS,uBAAuB,MAAc,EAAE,YAAoB;IACzE,MAAM,cAAc,kBAAkB,GAAG,CAAC;IAC1C,IAAI,aAAa;QACf,YAAY,MAAM,CAAC;QACnB,IAAI,YAAY,IAAI,KAAK,GAAG;YAC1B,kBAAkB,MAAM,CAAC;QAC3B;IACF;AACF;AAGO,SAAS,aAAa,MAAc;IACzC,OACE,kBAAkB,GAAG,CAAC,WAAW,kBAAkB,GAAG,CAAC,QAAS,IAAI,GAAG;AAE3E;AAGO,eAAe,IAAI,OAAoB;IAC5C,IAAI;QACF,MAAM,UAAU,MAAM,CAAA,GAAA,uIAAA,CAAA,mBAAgB,AAAD,EAAE,oHAAA,CAAA,cAAW;QAClD,IAAI,CAAC,SAAS,MAAM,IAAI;YACtB,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,SAAS;gBAAO,OAAO;YAAyB,GAClD;gBAAE,QAAQ;YAAI;QAElB;QAEA,MAAM,EAAE,YAAY,EAAE,GAAG,IAAI,IAAI,QAAQ,GAAG;QAC5C,MAAM,mBAAmB,aAAa,GAAG,CAAC;QAC1C,MAAM,OAAO,aAAa,GAAG,CAAC,WAAW,aAAa,GAAG,CAAC,OAAO,kBAAkB;QACnF,MAAM,gBAAgB,aAAa,GAAG,CAAC;QACvC,MAAM,QAAQ,KAAK,GAAG,CAAC,SAAS,aAAa,GAAG,CAAC,YAAY,OAAO,KAAK,kCAAkC;QAE3G,IAAI,kBAAkB;YACpB,+CAA+C;YAC/C,IAAI,qBAAqB,QAAQ,IAAI,CAAC,EAAE,EAAE;gBACxC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;oBAAE,SAAS;oBAAO,OAAO;gBAAsB,GAC/C;oBAAE,QAAQ;gBAAI;YAElB;YAEA,iCAAiC;YACjC,MAAM,cAAmB;gBACvB,IAAI;oBACF;wBAAE,QAAQ,QAAQ,IAAI,CAAC,EAAE;wBAAE,MAAM;oBAAiB;oBAClD;wBAAE,QAAQ;wBAAkB,MAAM,QAAQ,IAAI,CAAC,EAAE;oBAAC;iBACnD;gBACD,GAAI,QAAQ;oBAAE;gBAAK,CAAC;YACtB;YAEA,yCAAyC;YACzC,IAAI,eAAe;gBACjB,YAAY,EAAE,GAAG;oBACf,IAAI;gBACN;YACF;YAEA,0EAA0E;YAC1E,IAAI,CAAC,eAAe;gBAClB,MAAM,gBAAgB,IAAI;gBAC1B,cAAc,OAAO,CAAC,cAAc,OAAO,KAAK;gBAChD,YAAY,SAAS,GAAG;oBACtB,KAAK;gBACP;YACF;YAEA,+CAA+C;YAC/C,MAAM,WAAW,MAAM,sHAAA,CAAA,SAAM,CAAC,OAAO,CAAC,QAAQ,CAAC;gBAC7C,OAAO;gBACP,QAAQ;oBACN,IAAI;oBACJ,SAAS;oBACT,QAAQ;oBACR,WAAW;oBACX,QAAQ;oBACR,MAAM;oBACN,MAAM;gBACR;gBACA,SAAS;oBAAE,WAAW;gBAAO;gBAC7B,MAAM;YACR;YAEA,8CAA8C;YAC9C,MAAM,UAAU;mBACX,IAAI,IAAI;uBACN,SAAS,GAAG,CAAC,CAAC,IAAM,EAAE,MAAM;uBAC5B,SAAS,GAAG,CAAC,CAAC,IAAM,EAAE,IAAI;iBAC9B;aACF;YACD,MAAM,QAAQ;mBAAI,IAAI,IAAI,SAAS,GAAG,CAAC,CAAC,IAAM,EAAE,IAAI,EAAE,MAAM,CAAC;aAAU;YAEvE,MAAM,CAAC,OAAO,IAAI,GAAG,MAAM,QAAQ,GAAG,CAAC;gBACrC,sHAAA,CAAA,SAAM,CAAC,IAAI,CAAC,QAAQ,CAAC;oBACnB,OAAO;wBAAE,IAAI;4BAAE,IAAI;wBAAQ;oBAAE;oBAC7B,QAAQ;wBAAE,IAAI;wBAAM,MAAM;wBAAM,QAAQ;oBAAK;gBAC/C;gBACA,MAAM,MAAM,GAAG,IACX,sHAAA,CAAA,SAAM,CAAC,EAAE,CAAC,QAAQ,CAAC;oBACjB,OAAO;wBAAE,IAAI;4BAAE,IAAI;wBAAM;oBAAE;oBAC3B,QAAQ;wBAAE,IAAI;wBAAM,OAAO;wBAAM,OAAO;wBAAM,WAAW;oBAAK;gBAChE,KACA,EAAE;aACP;YAED,8CAA8C;YAC9C,MAAM,mBAAmB,SAAS,GAAG,CAAC,CAAC,UAAY,CAAC;oBAClD,GAAG,OAAO;oBACV,MAAM,MAAM,IAAI,CAAC,CAAC,IAAM,EAAE,EAAE,KAAK,QAAQ,MAAM;oBAC/C,IAAI,MAAM,IAAI,CAAC,CAAC,IAAM,EAAE,EAAE,KAAK,QAAQ,IAAI;oBAC3C,IAAI,QAAQ,IAAI,GAAG,IAAI,IAAI,CAAC,CAAC,IAAM,EAAE,EAAE,KAAK,QAAQ,IAAI,IAAI;gBAC9D,CAAC;YAED,sEAAsE;YACtE,IAAI,CAAC,eAAe;gBAClB,MAAM,sHAAA,CAAA,SAAM,CAAC,OAAO,CAAC,UAAU,CAAC;oBAC9B,OAAO;wBACL,QAAQ;wBACR,MAAM,QAAQ,IAAI,CAAC,EAAE;wBACrB,QAAQ;oBACV;oBACA,MAAM;wBAAE,QAAQ;oBAAK;gBACvB;YACF;YAEA,4BAA4B;YAC5B,IAAI,UAAU;YACd,IAAI,SAAS,MAAM,KAAK,OAAO;gBAC7B,MAAM,kBAAkB,QAAQ,CAAC,SAAS,MAAM,GAAG,EAAE,CAAC,EAAE;gBACxD,MAAM,qBAAqB,MAAM,sHAAA,CAAA,SAAM,CAAC,OAAO,CAAC,KAAK,CAAC;oBACpD,OAAO;wBACL,IAAI;4BACF;gCAAE,QAAQ,QAAQ,IAAI,CAAC,EAAE;gCAAE,MAAM;4BAAiB;4BAClD;gCAAE,QAAQ;gCAAkB,MAAM,QAAQ,IAAI,CAAC,EAAE;4BAAC;yBACnD;wBACD,GAAI,QAAQ;4BAAE;wBAAK,CAAC;wBACpB,IAAI;4BAAE,IAAI;wBAAgB;oBAC5B;gBACF;gBACA,UAAU,qBAAqB;YACjC;YAEA,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;gBACvB,SAAS;gBACT,MAAM;gBACN;gBACA,mDAAmD;gBACnD,eACE,SAAS,MAAM,GAAG,IAAI,QAAQ,CAAC,SAAS,MAAM,GAAG,EAAE,CAAC,EAAE,GAAG;YAC7D;QACF,OAAO;YACL,4CAA4C;YAC5C,MAAM,eAAe,IAAI;YACzB,aAAa,OAAO,CAAC,aAAa,OAAO,KAAK;YAE9C,MAAM,iBAAiB,MAAM,sHAAA,CAAA,SAAM,CAAC,OAAO,CAAC,QAAQ,CAAC;gBACnD,OAAO;oBACL,IAAI;wBAAC;4BAAE,QAAQ,QAAQ,IAAI,CAAC,EAAE;wBAAC;wBAAG;4BAAE,MAAM,QAAQ,IAAI,CAAC,EAAE;wBAAC;qBAAE;oBAC5D,WAAW;wBACT,KAAK;oBACP;oBACA,oBAAoB;oBACpB,KAAK;wBACH,KAAK;4BACH,QAAQ,QAAQ,IAAI,CAAC,EAAE;4BACvB,MAAM,QAAQ,IAAI,CAAC,EAAE;wBACvB;oBACF;oBACA,8BAA8B;oBAC9B,MAAM;wBACJ,KAAK;oBACP;gBACF;gBACA,SAAS;oBACP,MAAM;wBACJ,QAAQ;4BAAE,IAAI;4BAAM,MAAM;4BAAM,QAAQ;wBAAK;oBAC/C;oBACA,IAAI;wBACF,QAAQ;4BAAE,IAAI;4BAAM,MAAM;4BAAM,QAAQ;wBAAK;oBAC/C;oBACA,IAAI;wBACF,QAAQ;4BAAE,IAAI;4BAAM,OAAO;4BAAM,OAAO;wBAAK;oBAC/C;gBACF;gBACA,SAAS;oBAAE,WAAW;gBAAO;gBAC7B,MAAM;YACR;YAEA,+CAA+C;YAC/C,MAAM,uBAAuB,eAAe,MAAM,CAAC,CAAC,KAAK;gBACvD,MAAM,cACJ,QAAQ,MAAM,KAAK,QAAQ,IAAI,CAAC,EAAE,GAAG,QAAQ,IAAI,GAAG,QAAQ,MAAM;gBACpE,MAAM,MAAM,aAAa,+BAA+B;gBAExD,IAAI,CAAC,GAAG,CAAC,IAAI,IAAI,GAAG,CAAC,IAAI,CAAC,SAAS,GAAG,QAAQ,SAAS,EAAE;oBACvD,GAAG,CAAC,IAAI,GAAG;wBACT,GAAG,OAAO;wBACV,WACE,QAAQ,MAAM,KAAK,QAAQ,IAAI,CAAC,EAAE,GAAG,QAAQ,EAAE,GAAG,QAAQ,IAAI;wBAChE,aAAa;wBACb,UAAU,aAAa;oBACzB;gBACF;gBAEA,OAAO;YACT,GAAG,CAAC;YAEJ,uCAAuC;YACvC,MAAM,oBAAoB,OAAO,MAAM,CAAC;YAExC,KAAK,MAAM,gBAAgB,kBAAmB;gBAC5C,MAAM,cAAc,MAAM,sHAAA,CAAA,SAAM,CAAC,OAAO,CAAC,KAAK,CAAC;oBAC7C,OAAO;wBACL,QAAQ,aAAa,SAAS,CAAC,EAAE;wBACjC,MAAM,QAAQ,IAAI,CAAC,EAAE;wBACrB,QAAQ;oBACV;gBACF;gBACA,aAAa,WAAW,GAAG;YAC7B;YAEA,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;gBACvB,SAAS;gBACT,MAAM;YACR;QACF;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,4BAA4B;QAC1C,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YAAE,SAAS;YAAO,OAAO;QAAyB,GAClD;YAAE,QAAQ;QAAI;IAElB;AACF;AAGO,eAAe,KAAK,OAAoB;IAC7C,IAAI;QACF,MAAM,UAAU,MAAM,CAAA,GAAA,uIAAA,CAAA,mBAAgB,AAAD,EAAE,oHAAA,CAAA,cAAW;QAClD,IAAI,CAAC,SAAS,MAAM,IAAI;YACtB,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,SAAS;gBAAO,OAAO;YAAyB,GAClD;gBAAE,QAAQ;YAAI;QAElB;QAEA,MAAM,OAAO,MAAM,QAAQ,IAAI;QAC/B,MAAM,gBAAgB,kBAAkB,KAAK,CAAC;QAE9C,kCAAkC;QAClC,IAAI,cAAc,IAAI,KAAK,QAAQ,IAAI,CAAC,EAAE,EAAE;YAC1C,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,SAAS;gBAAO,OAAO;YAA4B,GACrD;gBAAE,QAAQ;YAAI;QAElB;QAEA,0BAA0B;QAC1B,MAAM,YAAY,MAAM,sHAAA,CAAA,SAAM,CAAC,IAAI,CAAC,UAAU,CAAC;YAC7C,OAAO;gBAAE,IAAI,cAAc,IAAI;YAAC;QAClC;QAEA,IAAI,CAAC,WAAW;YACd,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,SAAS;gBAAO,OAAO;YAA8B,GACvD;gBAAE,QAAQ;YAAI;QAElB;QAEA,yBAAyB;QACzB,MAAM,KAAK,MAAM,sHAAA,CAAA,SAAM,CAAC,EAAE,CAAC,UAAU,CAAC;YACpC,OAAO;gBAAE,IAAI,cAAc,IAAI;YAAC;QAClC;QAEA,IAAI,CAAC,IAAI;YACP,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,SAAS;gBAAO,OAAO;YAAoB,GAC7C;gBAAE,QAAQ;YAAI;QAElB;QAEA,gBAAgB;QAChB,MAAM,UAAU,MAAM,sHAAA,CAAA,SAAM,CAAC,OAAO,CAAC,MAAM,CAAC;YAC1C,MAAM;gBACJ,QAAQ,QAAQ,IAAI,CAAC,EAAE;gBACvB,MAAM,cAAc,IAAI;gBACxB,MAAM,cAAc,IAAI;gBACxB,SAAS,cAAc,OAAO;YAChC;YACA,SAAS;gBACP,MAAM;oBACJ,QAAQ;wBAAE,IAAI;wBAAM,MAAM;wBAAM,QAAQ;oBAAK;gBAC/C;gBACA,IAAI;oBACF,QAAQ;wBAAE,IAAI;wBAAM,MAAM;wBAAM,QAAQ;oBAAK;gBAC/C;gBACA,IAAI;oBACF,QAAQ;wBAAE,IAAI;wBAAM,OAAO;wBAAM,OAAO;wBAAM,WAAW;oBAAK;gBAChE;YACF;QACF;QAEA,4BAA4B;QAC5B,IAAI;YACF,MAAM,EAAE,iBAAiB,EAAE,GAAG;YAC9B,kBAAkB,cAAc,IAAI,EAAE;gBACpC,MAAM;gBACN,MAAM;gBACN,WAAW,IAAI,OAAO,WAAW;YACnC;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,mCAAmC;QACjD,6DAA6D;QAC/D;QAEA,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YACvB,SAAS;YACT,MAAM;YACN,SAAS;QACX;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,0BAA0B;QAExC,IAAI,iBAAiB,mLAAA,CAAA,IAAC,CAAC,QAAQ,EAAE;YAC/B,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,SAAS;gBAAO,OAAO,MAAM,MAAM,CAAC,EAAE,CAAC,OAAO;YAAC,GACjD;gBAAE,QAAQ;YAAI;QAElB;QAEA,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YAAE,SAAS;YAAO,OAAO;QAA2B,GACpD;YAAE,QAAQ;QAAI;IAElB;AACF", "debugId": null}}, {"offset": {"line": 687, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/kaliha/khalihatanba<PERSON>/src/app/api/messages/sse/route.ts"], "sourcesContent": ["import { NextRequest } from \"next/server\";\nimport { getServerSession } from \"next-auth\";\nimport { authOptions } from \"@/lib/auth\";\nimport { addActiveConnection, removeActiveConnection } from \"../route\";\n\n// Map لتخزين الاتصالات النشطة\nconst connections = new Map<string, ReadableStreamDefaultController>();\n\n// إرسال رسالة لمستخدم محدد\nexport function sendMessageToUser(userId: string, data: any) {\n  const controller = connections.get(userId);\n  if (controller) {\n    try {\n      const message = `data: ${JSON.stringify(data)}\\n\\n`;\n      controller.enqueue(new TextEncoder().encode(message));\n    } catch (error) {\n      console.error(\"Error sending SSE message:\", error);\n      // إزالة الاتصال المعطل\n      connections.delete(userId);\n      removeActiveConnection(userId, userId);\n    }\n  }\n}\n\n// إرسال رسالة لجميع المستخدمين المتصلين\nexport function broadcastMessage(data: any) {\n  connections.forEach((controller, userId) => {\n    sendMessageToUser(userId, data);\n  });\n}\n\nexport async function GET(request: NextRequest) {\n  const session = await getServerSession(authOptions);\n  \n  if (!session?.user?.id) {\n    return new Response(\"Unauthorized\", { status: 401 });\n  }\n\n  const userId = session.user.id;\n\n  // إنشاء ReadableStream للـ SSE\n  const stream = new ReadableStream({\n    start(controller) {\n      // إضافة الاتصال للخريطة\n      connections.set(userId, controller);\n      addActiveConnection(userId, userId);\n\n      // إرسال رسالة ترحيب\n      const welcomeMessage = `data: ${JSON.stringify({\n        type: \"connected\",\n        message: \"تم الاتصال بنجاح\",\n        timestamp: new Date().toISOString(),\n      })}\\n\\n`;\n      \n      controller.enqueue(new TextEncoder().encode(welcomeMessage));\n\n      // إرسال ping كل 30 ثانية للحفاظ على الاتصال\n      const pingInterval = setInterval(() => {\n        try {\n          const pingMessage = `data: ${JSON.stringify({\n            type: \"ping\",\n            timestamp: new Date().toISOString(),\n          })}\\n\\n`;\n          controller.enqueue(new TextEncoder().encode(pingMessage));\n        } catch (error) {\n          console.error(\"Error sending ping:\", error);\n          clearInterval(pingInterval);\n          connections.delete(userId);\n          removeActiveConnection(userId, userId);\n        }\n      }, 30000);\n\n      // تنظيف عند إغلاق الاتصال\n      request.signal.addEventListener(\"abort\", () => {\n        clearInterval(pingInterval);\n        connections.delete(userId);\n        removeActiveConnection(userId, userId);\n        controller.close();\n      });\n    },\n    cancel() {\n      connections.delete(userId);\n      removeActiveConnection(userId, userId);\n    },\n  });\n\n  return new Response(stream, {\n    headers: {\n      \"Content-Type\": \"text/event-stream\",\n      \"Cache-Control\": \"no-cache\",\n      \"Connection\": \"keep-alive\",\n      \"Access-Control-Allow-Origin\": \"*\",\n      \"Access-Control-Allow-Headers\": \"Cache-Control\",\n    },\n  });\n}\n"], "names": [], "mappings": ";;;;;AACA;AACA;AACA;;;;AAEA,8BAA8B;AAC9B,MAAM,cAAc,IAAI;AAGjB,SAAS,kBAAkB,MAAc,EAAE,IAAS;IACzD,MAAM,aAAa,YAAY,GAAG,CAAC;IACnC,IAAI,YAAY;QACd,IAAI;YACF,MAAM,UAAU,CAAC,MAAM,EAAE,KAAK,SAAS,CAAC,MAAM,IAAI,CAAC;YACnD,WAAW,OAAO,CAAC,IAAI,cAAc,MAAM,CAAC;QAC9C,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,8BAA8B;YAC5C,uBAAuB;YACvB,YAAY,MAAM,CAAC;YACnB,CAAA,GAAA,wIAAA,CAAA,yBAAsB,AAAD,EAAE,QAAQ;QACjC;IACF;AACF;AAGO,SAAS,iBAAiB,IAAS;IACxC,YAAY,OAAO,CAAC,CAAC,YAAY;QAC/B,kBAAkB,QAAQ;IAC5B;AACF;AAEO,eAAe,IAAI,OAAoB;IAC5C,MAAM,UAAU,MAAM,CAAA,GAAA,uIAAA,CAAA,mBAAgB,AAAD,EAAE,oHAAA,CAAA,cAAW;IAElD,IAAI,CAAC,SAAS,MAAM,IAAI;QACtB,OAAO,IAAI,SAAS,gBAAgB;YAAE,QAAQ;QAAI;IACpD;IAEA,MAAM,SAAS,QAAQ,IAAI,CAAC,EAAE;IAE9B,+BAA+B;IAC/B,MAAM,SAAS,IAAI,eAAe;QAChC,OAAM,UAAU;YACd,wBAAwB;YACxB,YAAY,GAAG,CAAC,QAAQ;YACxB,CAAA,GAAA,wIAAA,CAAA,sBAAmB,AAAD,EAAE,QAAQ;YAE5B,oBAAoB;YACpB,MAAM,iBAAiB,CAAC,MAAM,EAAE,KAAK,SAAS,CAAC;gBAC7C,MAAM;gBACN,SAAS;gBACT,WAAW,IAAI,OAAO,WAAW;YACnC,GAAG,IAAI,CAAC;YAER,WAAW,OAAO,CAAC,IAAI,cAAc,MAAM,CAAC;YAE5C,4CAA4C;YAC5C,MAAM,eAAe,YAAY;gBAC/B,IAAI;oBACF,MAAM,cAAc,CAAC,MAAM,EAAE,KAAK,SAAS,CAAC;wBAC1C,MAAM;wBACN,WAAW,IAAI,OAAO,WAAW;oBACnC,GAAG,IAAI,CAAC;oBACR,WAAW,OAAO,CAAC,IAAI,cAAc,MAAM,CAAC;gBAC9C,EAAE,OAAO,OAAO;oBACd,QAAQ,KAAK,CAAC,uBAAuB;oBACrC,cAAc;oBACd,YAAY,MAAM,CAAC;oBACnB,CAAA,GAAA,wIAAA,CAAA,yBAAsB,AAAD,EAAE,QAAQ;gBACjC;YACF,GAAG;YAEH,0BAA0B;YAC1B,QAAQ,MAAM,CAAC,gBAAgB,CAAC,SAAS;gBACvC,cAAc;gBACd,YAAY,MAAM,CAAC;gBACnB,CAAA,GAAA,wIAAA,CAAA,yBAAsB,AAAD,EAAE,QAAQ;gBAC/B,WAAW,KAAK;YAClB;QACF;QACA;YACE,YAAY,MAAM,CAAC;YACnB,CAAA,GAAA,wIAAA,CAAA,yBAAsB,AAAD,EAAE,QAAQ;QACjC;IACF;IAEA,OAAO,IAAI,SAAS,QAAQ;QAC1B,SAAS;YACP,gBAAgB;YAChB,iBAAiB;YACjB,cAAc;YACd,+BAA+B;YAC/B,gCAAgC;QAClC;IACF;AACF", "debugId": null}}]}