{"version": 3, "sources": [], "sections": [{"offset": {"line": 31, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/kaliha/khalihatanbaa/src/components/layout/Header.tsx"], "sourcesContent": ["\"use client\";\n\nimport { useState } from \"react\";\nimport Link from \"next/link\";\nimport { useSession, signOut } from \"next-auth/react\";\nimport {\n  MagnifyingGlassIcon,\n  PlusIcon,\n  ChatBubbleLeftRightIcon,\n  HeartIcon,\n  UserIcon,\n  Bars3Icon,\n  XMarkIcon,\n  ChartBarIcon,\n} from \"@heroicons/react/24/outline\";\n\nexport function Header() {\n  const { data: session } = useSession();\n  const [isMenuOpen, setIsMenuOpen] = useState(false);\n  const [searchQuery, setSearchQuery] = useState(\"\");\n\n  const handleSearch = (e: React.FormEvent) => {\n    e.preventDefault();\n    if (searchQuery.trim()) {\n      window.location.href = `/search?q=${encodeURIComponent(searchQuery)}`;\n    }\n  };\n\n  return (\n    <header className=\"bg-white shadow-sm border-b border-gray-200 sticky top-0 z-50\">\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n        <div className=\"flex items-center justify-between h-16\">\n          {/* الشعار */}\n          <Link\n            href=\"/\"\n            className=\"flex items-center space-x-2 space-x-reverse\"\n          >\n            <div className=\"w-10 h-10 bg-primary-500 rounded-lg flex items-center justify-center\">\n              <span className=\"text-white font-bold text-lg\">خ</span>\n            </div>\n            <div className=\"hidden sm:block\">\n              <h1 className=\"text-xl font-bold text-dark-800\">\n                خَلّيها تنْباع\n              </h1>\n              <p className=\"text-xs text-dark-500\">منصة البيع والشراء</p>\n            </div>\n          </Link>\n\n          {/* شريط البحث */}\n          <div className=\"flex-1 max-w-lg mx-4\">\n            <form onSubmit={handleSearch} className=\"relative\">\n              <input\n                type=\"text\"\n                placeholder=\"ابحث عن أي شيء...\"\n                value={searchQuery}\n                onChange={(e) => setSearchQuery(e.target.value)}\n                className=\"w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent\"\n              />\n              <MagnifyingGlassIcon className=\"absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400\" />\n            </form>\n          </div>\n\n          {/* أزرار التنقل */}\n          <div className=\"flex items-center space-x-4 space-x-reverse\">\n            {session ? (\n              <>\n                {/* نشر إعلان */}\n                <Link\n                  href=\"/ads/new\"\n                  className=\"bg-primary-500 text-white px-4 py-2 rounded-lg hover:bg-primary-600 transition-colors flex items-center space-x-2 space-x-reverse\"\n                >\n                  <PlusIcon className=\"h-5 w-5\" />\n                  <span className=\"hidden sm:inline\">نشر إعلان</span>\n                </Link>\n\n                {/* الرسائل */}\n                <Link\n                  href=\"/messages\"\n                  className=\"p-2 text-dark-600 hover:text-primary-500 transition-colors relative\"\n                >\n                  <ChatBubbleLeftRightIcon className=\"h-6 w-6\" />\n                  {/* نقطة الإشعارات */}\n                  <span className=\"absolute -top-1 -right-1 h-3 w-3 bg-primary-500 rounded-full\"></span>\n                </Link>\n\n                {/* المفضلة */}\n                <Link\n                  href=\"/favorites\"\n                  className=\"p-2 text-dark-600 hover:text-primary-500 transition-colors\"\n                >\n                  <HeartIcon className=\"h-6 w-6\" />\n                </Link>\n\n                {/* لوحة التحكم */}\n                <Link\n                  href=\"/dashboard\"\n                  className=\"p-2 text-dark-600 hover:text-primary-500 transition-colors\"\n                >\n                  <ChartBarIcon className=\"h-6 w-6\" />\n                </Link>\n\n                {/* قائمة المستخدم */}\n                <div className=\"relative\">\n                  <button\n                    onClick={() => setIsMenuOpen(!isMenuOpen)}\n                    className=\"flex items-center space-x-2 space-x-reverse p-2 text-dark-600 hover:text-primary-500 transition-colors\"\n                  >\n                    <UserIcon className=\"h-6 w-6\" />\n                    <span className=\"hidden sm:inline\">\n                      {session.user.name}\n                    </span>\n                  </button>\n\n                  {isMenuOpen && (\n                    <div className=\"absolute right-0 mt-2 w-48 bg-white rounded-lg shadow-lg border border-gray-200 py-2\">\n                      <Link\n                        href=\"/profile\"\n                        className=\"block px-4 py-2 text-dark-700 hover:bg-gray-50\"\n                      >\n                        الملف الشخصي\n                      </Link>\n                      <Link\n                        href=\"/profile/ads\"\n                        className=\"block px-4 py-2 text-dark-700 hover:bg-gray-50\"\n                      >\n                        إعلاناتي\n                      </Link>\n                      <Link\n                        href=\"/profile/settings\"\n                        className=\"block px-4 py-2 text-dark-700 hover:bg-gray-50\"\n                      >\n                        الإعدادات\n                      </Link>\n                      <hr className=\"my-2\" />\n                      <button\n                        onClick={() => signOut()}\n                        className=\"block w-full text-right px-4 py-2 text-red-600 hover:bg-gray-50\"\n                      >\n                        تسجيل الخروج\n                      </button>\n                    </div>\n                  )}\n                </div>\n              </>\n            ) : (\n              <>\n                <Link\n                  href=\"/auth/login\"\n                  className=\"text-dark-600 hover:text-primary-500 transition-colors\"\n                >\n                  تسجيل الدخول\n                </Link>\n                <Link\n                  href=\"/auth/register\"\n                  className=\"bg-primary-500 text-white px-4 py-2 rounded-lg hover:bg-primary-600 transition-colors\"\n                >\n                  إنشاء حساب\n                </Link>\n              </>\n            )}\n\n            {/* قائمة الموبايل */}\n            <button\n              onClick={() => setIsMenuOpen(!isMenuOpen)}\n              className=\"sm:hidden p-2 text-dark-600\"\n            >\n              {isMenuOpen ? (\n                <XMarkIcon className=\"h-6 w-6\" />\n              ) : (\n                <Bars3Icon className=\"h-6 w-6\" />\n              )}\n            </button>\n          </div>\n        </div>\n\n        {/* قائمة الموبايل */}\n        {isMenuOpen && (\n          <div className=\"sm:hidden border-t border-gray-200 py-4\">\n            <div className=\"space-y-2\">\n              <Link\n                href=\"/ads\"\n                className=\"block px-4 py-2 text-dark-700 hover:bg-gray-50 rounded\"\n              >\n                جميع الإعلانات\n              </Link>\n              <Link\n                href=\"/categories\"\n                className=\"block px-4 py-2 text-dark-700 hover:bg-gray-50 rounded\"\n              >\n                جميع الفئات\n              </Link>\n              {session && (\n                <>\n                  <Link\n                    href=\"/ads/new\"\n                    className=\"block px-4 py-2 text-primary-600 hover:bg-gray-50 rounded\"\n                  >\n                    نشر إعلان جديد\n                  </Link>\n                  <Link\n                    href=\"/messages\"\n                    className=\"block px-4 py-2 text-dark-700 hover:bg-gray-50 rounded\"\n                  >\n                    الرسائل\n                  </Link>\n                  <Link\n                    href=\"/favorites\"\n                    className=\"block px-4 py-2 text-dark-700 hover:bg-gray-50 rounded\"\n                  >\n                    المفضلة\n                  </Link>\n                  <Link\n                    href=\"/dashboard\"\n                    className=\"block px-4 py-2 text-dark-700 hover:bg-gray-50 rounded\"\n                  >\n                    لوحة التحكم\n                  </Link>\n                </>\n              )}\n            </div>\n          </div>\n        )}\n      </div>\n    </header>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AALA;;;;;;AAgBO,SAAS;IACd,MAAM,EAAE,MAAM,OAAO,EAAE,GAAG,CAAA,GAAA,8IAAA,CAAA,aAAU,AAAD;IACnC,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE/C,MAAM,eAAe,CAAC;QACpB,EAAE,cAAc;QAChB,IAAI,YAAY,IAAI,IAAI;YACtB,OAAO,QAAQ,CAAC,IAAI,GAAG,CAAC,UAAU,EAAE,mBAAmB,cAAc;QACvE;IACF;IAEA,qBACE,8OAAC;QAAO,WAAU;kBAChB,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC,4JAAA,CAAA,UAAI;4BACH,MAAK;4BACL,WAAU;;8CAEV,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCAAK,WAAU;kDAA+B;;;;;;;;;;;8CAEjD,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAG,WAAU;sDAAkC;;;;;;sDAGhD,8OAAC;4CAAE,WAAU;sDAAwB;;;;;;;;;;;;;;;;;;sCAKzC,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAK,UAAU;gCAAc,WAAU;;kDACtC,8OAAC;wCACC,MAAK;wCACL,aAAY;wCACZ,OAAO;wCACP,UAAU,CAAC,IAAM,eAAe,EAAE,MAAM,CAAC,KAAK;wCAC9C,WAAU;;;;;;kDAEZ,8OAAC,qOAAA,CAAA,sBAAmB;wCAAC,WAAU;;;;;;;;;;;;;;;;;sCAKnC,8OAAC;4BAAI,WAAU;;gCACZ,wBACC;;sDAEE,8OAAC,4JAAA,CAAA,UAAI;4CACH,MAAK;4CACL,WAAU;;8DAEV,8OAAC,+MAAA,CAAA,WAAQ;oDAAC,WAAU;;;;;;8DACpB,8OAAC;oDAAK,WAAU;8DAAmB;;;;;;;;;;;;sDAIrC,8OAAC,4JAAA,CAAA,UAAI;4CACH,MAAK;4CACL,WAAU;;8DAEV,8OAAC,6OAAA,CAAA,0BAAuB;oDAAC,WAAU;;;;;;8DAEnC,8OAAC;oDAAK,WAAU;;;;;;;;;;;;sDAIlB,8OAAC,4JAAA,CAAA,UAAI;4CACH,MAAK;4CACL,WAAU;sDAEV,cAAA,8OAAC,iNAAA,CAAA,YAAS;gDAAC,WAAU;;;;;;;;;;;sDAIvB,8OAAC,4JAAA,CAAA,UAAI;4CACH,MAAK;4CACL,WAAU;sDAEV,cAAA,8OAAC,uNAAA,CAAA,eAAY;gDAAC,WAAU;;;;;;;;;;;sDAI1B,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDACC,SAAS,IAAM,cAAc,CAAC;oDAC9B,WAAU;;sEAEV,8OAAC,+MAAA,CAAA,WAAQ;4DAAC,WAAU;;;;;;sEACpB,8OAAC;4DAAK,WAAU;sEACb,QAAQ,IAAI,CAAC,IAAI;;;;;;;;;;;;gDAIrB,4BACC,8OAAC;oDAAI,WAAU;;sEACb,8OAAC,4JAAA,CAAA,UAAI;4DACH,MAAK;4DACL,WAAU;sEACX;;;;;;sEAGD,8OAAC,4JAAA,CAAA,UAAI;4DACH,MAAK;4DACL,WAAU;sEACX;;;;;;sEAGD,8OAAC,4JAAA,CAAA,UAAI;4DACH,MAAK;4DACL,WAAU;sEACX;;;;;;sEAGD,8OAAC;4DAAG,WAAU;;;;;;sEACd,8OAAC;4DACC,SAAS,IAAM,CAAA,GAAA,8IAAA,CAAA,UAAO,AAAD;4DACrB,WAAU;sEACX;;;;;;;;;;;;;;;;;;;iEAQT;;sDACE,8OAAC,4JAAA,CAAA,UAAI;4CACH,MAAK;4CACL,WAAU;sDACX;;;;;;sDAGD,8OAAC,4JAAA,CAAA,UAAI;4CACH,MAAK;4CACL,WAAU;sDACX;;;;;;;;8CAOL,8OAAC;oCACC,SAAS,IAAM,cAAc,CAAC;oCAC9B,WAAU;8CAET,2BACC,8OAAC,iNAAA,CAAA,YAAS;wCAAC,WAAU;;;;;6DAErB,8OAAC,iNAAA,CAAA,YAAS;wCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;gBAO5B,4BACC,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,4JAAA,CAAA,UAAI;gCACH,MAAK;gCACL,WAAU;0CACX;;;;;;0CAGD,8OAAC,4JAAA,CAAA,UAAI;gCACH,MAAK;gCACL,WAAU;0CACX;;;;;;4BAGA,yBACC;;kDACE,8OAAC,4JAAA,CAAA,UAAI;wCACH,MAAK;wCACL,WAAU;kDACX;;;;;;kDAGD,8OAAC,4JAAA,CAAA,UAAI;wCACH,MAAK;wCACL,WAAU;kDACX;;;;;;kDAGD,8OAAC,4JAAA,CAAA,UAAI;wCACH,MAAK;wCACL,WAAU;kDACX;;;;;;kDAGD,8OAAC,4JAAA,CAAA,UAAI;wCACH,MAAK;wCACL,WAAU;kDACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAWnB", "debugId": null}}, {"offset": {"line": 473, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/kaliha/khalihatanbaa/src/components/layout/Footer.tsx"], "sourcesContent": ["import Link from 'next/link'\n\nexport function Footer() {\n  return (\n    <footer className=\"bg-dark-800 text-white\">\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12\">\n        <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8\">\n          {/* معلومات الموقع */}\n          <div className=\"lg:col-span-2\">\n            <div className=\"flex items-center space-x-2 space-x-reverse mb-4\">\n              <div className=\"w-10 h-10 bg-primary-500 rounded-lg flex items-center justify-center\">\n                <span className=\"text-white font-bold text-lg\">خ</span>\n              </div>\n              <div>\n                <h3 className=\"text-xl font-bold\">خَلّيها تنْباع</h3>\n                <p className=\"text-sm text-gray-400\">منصة البيع والشراء</p>\n              </div>\n            </div>\n            <p className=\"text-gray-300 mb-6 max-w-md\">\n              منصة إلكترونية سورية لبيع وشراء المنتجات المستعملة والجديدة. \n              نوفر بيئة آمنة وسهلة للتجارة الإلكترونية في سوريا.\n            </p>\n            <div className=\"flex space-x-4 space-x-reverse\">\n              <a href=\"#\" className=\"text-gray-400 hover:text-white transition-colors\">\n                <span className=\"sr-only\">فيسبوك</span>\n                <svg className=\"h-6 w-6\" fill=\"currentColor\" viewBox=\"0 0 24 24\">\n                  <path d=\"M24 12.073c0-6.627-5.373-12-12-12s-12 5.373-12 12c0 5.99 4.388 10.954 10.125 11.854v-8.385H7.078v-3.47h3.047V9.43c0-3.007 1.792-4.669 4.533-4.669 1.312 0 2.686.235 2.686.235v2.953H15.83c-1.491 0-1.956.925-1.956 1.874v2.25h3.328l-.532 3.47h-2.796v8.385C19.612 23.027 24 18.062 24 12.073z\"/>\n                </svg>\n              </a>\n              <a href=\"#\" className=\"text-gray-400 hover:text-white transition-colors\">\n                <span className=\"sr-only\">تلغرام</span>\n                <svg className=\"h-6 w-6\" fill=\"currentColor\" viewBox=\"0 0 24 24\">\n                  <path d=\"M11.944 0A12 12 0 0 0 0 12a12 12 0 0 0 12 12 12 12 0 0 0 12-12A12 12 0 0 0 12 0a12 12 0 0 0-.056 0zm4.962 7.224c.1-.002.321.023.465.14a.506.506 0 0 1 .171.325c.016.093.036.306.02.472-.18 1.898-.962 6.502-1.36 8.627-.168.9-.499 1.201-.82 1.23-.696.065-1.225-.46-1.9-.902-1.056-.693-1.653-1.124-2.678-1.8-1.185-.78-.417-1.21.258-1.91.177-.184 3.247-2.977 3.307-3.23.007-.032.014-.15-.056-.212s-.174-.041-.249-.024c-.106.024-1.793 1.14-5.061 3.345-.48.33-.913.49-1.302.48-.428-.008-1.252-.241-1.865-.44-.752-.245-1.349-.374-1.297-.789.027-.216.325-.437.893-.663 3.498-1.524 5.83-2.529 6.998-3.014 3.332-1.386 4.025-1.627 4.476-1.635z\"/>\n                </svg>\n              </a>\n              <a href=\"#\" className=\"text-gray-400 hover:text-white transition-colors\">\n                <span className=\"sr-only\">واتساب</span>\n                <svg className=\"h-6 w-6\" fill=\"currentColor\" viewBox=\"0 0 24 24\">\n                  <path d=\"M17.472 14.382c-.297-.149-1.758-.867-2.03-.967-.273-.099-.471-.148-.67.15-.197.297-.767.966-.94 1.164-.173.199-.347.223-.644.075-.297-.15-1.255-.463-2.39-1.475-.883-.788-1.48-1.761-1.653-2.059-.173-.297-.018-.458.13-.606.134-.133.298-.347.446-.52.149-.174.198-.298.298-.497.099-.198.05-.371-.025-.52-.075-.149-.669-1.612-.916-2.207-.242-.579-.487-.5-.669-.51-.173-.008-.371-.01-.57-.01-.198 0-.52.074-.792.372-.272.297-1.04 1.016-1.04 2.479 0 1.462 1.065 2.875 1.213 3.074.149.198 2.096 3.2 5.077 4.487.709.306 1.262.489 1.694.625.712.227 1.36.195 1.871.118.571-.085 1.758-.719 2.006-1.413.248-.694.248-1.289.173-1.413-.074-.124-.272-.198-.57-.347m-5.421 7.403h-.004a9.87 9.87 0 01-5.031-1.378l-.361-.214-3.741.982.998-3.648-.235-.374a9.86 9.86 0 01-1.51-5.26c.001-5.45 4.436-9.884 9.888-9.884 2.64 0 5.122 1.03 6.988 2.898a9.825 9.825 0 012.893 6.994c-.003 5.45-4.437 9.884-9.885 9.884m8.413-18.297A11.815 11.815 0 0012.05 0C5.495 0 .16 5.335.157 11.892c0 2.096.547 4.142 1.588 5.945L.057 24l6.305-1.654a11.882 11.882 0 005.683 1.448h.005c6.554 0 11.89-5.335 11.893-11.893A11.821 11.821 0 0020.885 3.097\"/>\n                </svg>\n              </a>\n            </div>\n          </div>\n\n          {/* روابط سريعة */}\n          <div>\n            <h4 className=\"text-lg font-semibold mb-4\">روابط سريعة</h4>\n            <ul className=\"space-y-2\">\n              <li>\n                <Link href=\"/about\" className=\"text-gray-300 hover:text-white transition-colors\">\n                  من نحن\n                </Link>\n              </li>\n              <li>\n                <Link href=\"/how-it-works\" className=\"text-gray-300 hover:text-white transition-colors\">\n                  كيف يعمل الموقع\n                </Link>\n              </li>\n              <li>\n                <Link href=\"/safety-tips\" className=\"text-gray-300 hover:text-white transition-colors\">\n                  نصائح الأمان\n                </Link>\n              </li>\n              <li>\n                <Link href=\"/pricing\" className=\"text-gray-300 hover:text-white transition-colors\">\n                  باقات الإعلانات\n                </Link>\n              </li>\n              <li>\n                <Link href=\"/contact\" className=\"text-gray-300 hover:text-white transition-colors\">\n                  اتصل بنا\n                </Link>\n              </li>\n            </ul>\n          </div>\n\n          {/* الدعم والمساعدة */}\n          <div>\n            <h4 className=\"text-lg font-semibold mb-4\">الدعم والمساعدة</h4>\n            <ul className=\"space-y-2\">\n              <li>\n                <Link href=\"/help\" className=\"text-gray-300 hover:text-white transition-colors\">\n                  مركز المساعدة\n                </Link>\n              </li>\n              <li>\n                <Link href=\"/terms\" className=\"text-gray-300 hover:text-white transition-colors\">\n                  شروط الاستخدام\n                </Link>\n              </li>\n              <li>\n                <Link href=\"/privacy\" className=\"text-gray-300 hover:text-white transition-colors\">\n                  سياسة الخصوصية\n                </Link>\n              </li>\n              <li>\n                <Link href=\"/report\" className=\"text-gray-300 hover:text-white transition-colors\">\n                  الإبلاغ عن مشكلة\n                </Link>\n              </li>\n              <li>\n                <Link href=\"/faq\" className=\"text-gray-300 hover:text-white transition-colors\">\n                  الأسئلة الشائعة\n                </Link>\n              </li>\n            </ul>\n          </div>\n        </div>\n\n        {/* معلومات الاتصال */}\n        <div className=\"border-t border-gray-700 mt-8 pt-8\">\n          <div className=\"grid grid-cols-1 md:grid-cols-3 gap-6\">\n            <div className=\"text-center md:text-right\">\n              <h5 className=\"font-semibold mb-2\">البريد الإلكتروني</h5>\n              <p className=\"text-gray-300\"><EMAIL></p>\n            </div>\n            <div className=\"text-center\">\n              <h5 className=\"font-semibold mb-2\">رقم الهاتف</h5>\n              <p className=\"text-gray-300\" dir=\"ltr\">+963 11 123 4567</p>\n            </div>\n            <div className=\"text-center md:text-left\">\n              <h5 className=\"font-semibold mb-2\">العنوان</h5>\n              <p className=\"text-gray-300\">دمشق، سوريا</p>\n            </div>\n          </div>\n        </div>\n\n        {/* حقوق النشر */}\n        <div className=\"border-t border-gray-700 mt-8 pt-8 text-center\">\n          <p className=\"text-gray-400\">\n            © 2024 خَلّيها تنْباع. جميع الحقوق محفوظة.\n          </p>\n          <p className=\"text-gray-500 text-sm mt-2\">\n            تم التطوير بـ ❤️ في سوريا\n          </p>\n        </div>\n      </div>\n    </footer>\n  )\n}\n"], "names": [], "mappings": ";;;;AAAA;;;AAEO,SAAS;IACd,qBACE,8OAAC;QAAO,WAAU;kBAChB,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDAAK,WAAU;0DAA+B;;;;;;;;;;;sDAEjD,8OAAC;;8DACC,8OAAC;oDAAG,WAAU;8DAAoB;;;;;;8DAClC,8OAAC;oDAAE,WAAU;8DAAwB;;;;;;;;;;;;;;;;;;8CAGzC,8OAAC;oCAAE,WAAU;8CAA8B;;;;;;8CAI3C,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAE,MAAK;4CAAI,WAAU;;8DACpB,8OAAC;oDAAK,WAAU;8DAAU;;;;;;8DAC1B,8OAAC;oDAAI,WAAU;oDAAU,MAAK;oDAAe,SAAQ;8DACnD,cAAA,8OAAC;wDAAK,GAAE;;;;;;;;;;;;;;;;;sDAGZ,8OAAC;4CAAE,MAAK;4CAAI,WAAU;;8DACpB,8OAAC;oDAAK,WAAU;8DAAU;;;;;;8DAC1B,8OAAC;oDAAI,WAAU;oDAAU,MAAK;oDAAe,SAAQ;8DACnD,cAAA,8OAAC;wDAAK,GAAE;;;;;;;;;;;;;;;;;sDAGZ,8OAAC;4CAAE,MAAK;4CAAI,WAAU;;8DACpB,8OAAC;oDAAK,WAAU;8DAAU;;;;;;8DAC1B,8OAAC;oDAAI,WAAU;oDAAU,MAAK;oDAAe,SAAQ;8DACnD,cAAA,8OAAC;wDAAK,GAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;sCAOhB,8OAAC;;8CACC,8OAAC;oCAAG,WAAU;8CAA6B;;;;;;8CAC3C,8OAAC;oCAAG,WAAU;;sDACZ,8OAAC;sDACC,cAAA,8OAAC,4JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAS,WAAU;0DAAmD;;;;;;;;;;;sDAInF,8OAAC;sDACC,cAAA,8OAAC,4JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAgB,WAAU;0DAAmD;;;;;;;;;;;sDAI1F,8OAAC;sDACC,cAAA,8OAAC,4JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAe,WAAU;0DAAmD;;;;;;;;;;;sDAIzF,8OAAC;sDACC,cAAA,8OAAC,4JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAW,WAAU;0DAAmD;;;;;;;;;;;sDAIrF,8OAAC;sDACC,cAAA,8OAAC,4JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAW,WAAU;0DAAmD;;;;;;;;;;;;;;;;;;;;;;;sCAQzF,8OAAC;;8CACC,8OAAC;oCAAG,WAAU;8CAA6B;;;;;;8CAC3C,8OAAC;oCAAG,WAAU;;sDACZ,8OAAC;sDACC,cAAA,8OAAC,4JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAQ,WAAU;0DAAmD;;;;;;;;;;;sDAIlF,8OAAC;sDACC,cAAA,8OAAC,4JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAS,WAAU;0DAAmD;;;;;;;;;;;sDAInF,8OAAC;sDACC,cAAA,8OAAC,4JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAW,WAAU;0DAAmD;;;;;;;;;;;sDAIrF,8OAAC;sDACC,cAAA,8OAAC,4JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAU,WAAU;0DAAmD;;;;;;;;;;;sDAIpF,8OAAC;sDACC,cAAA,8OAAC,4JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAO,WAAU;0DAAmD;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8BASvF,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAG,WAAU;kDAAqB;;;;;;kDACnC,8OAAC;wCAAE,WAAU;kDAAgB;;;;;;;;;;;;0CAE/B,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAG,WAAU;kDAAqB;;;;;;kDACnC,8OAAC;wCAAE,WAAU;wCAAgB,KAAI;kDAAM;;;;;;;;;;;;0CAEzC,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAG,WAAU;kDAAqB;;;;;;kDACnC,8OAAC;wCAAE,WAAU;kDAAgB;;;;;;;;;;;;;;;;;;;;;;;8BAMnC,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAE,WAAU;sCAAgB;;;;;;sCAG7B,8OAAC;4BAAE,WAAU;sCAA6B;;;;;;;;;;;;;;;;;;;;;;;AAOpD", "debugId": null}}, {"offset": {"line": 1006, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/kaliha/khalihatanbaa/src/components/ClientOnly.tsx"], "sourcesContent": ["'use client'\n\nimport { useEffect, useState } from 'react'\n\ninterface ClientOnlyProps {\n  children: React.ReactNode\n  fallback?: React.ReactNode\n}\n\nexport function ClientOnly({ children, fallback = null }: ClientOnlyProps) {\n  const [hasMounted, setHasMounted] = useState(false)\n\n  useEffect(() => {\n    setHasMounted(true)\n  }, [])\n\n  if (!hasMounted) {\n    return <>{fallback}</>\n  }\n\n  return <>{children}</>\n}\n"], "names": [], "mappings": ";;;;AAEA;AAFA;;;AASO,SAAS,WAAW,EAAE,QAAQ,EAAE,WAAW,IAAI,EAAmB;IACvE,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE7C,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,cAAc;IAChB,GAAG,EAAE;IAEL,IAAI,CAAC,YAAY;QACf,qBAAO;sBAAG;;IACZ;IAEA,qBAAO;kBAAG;;AACZ", "debugId": null}}, {"offset": {"line": 1034, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/kaliha/khalihatanbaa/src/hooks/useMessages.ts"], "sourcesContent": ["import { useState, useEffect, useCallback, useRef } from \"react\";\nimport { useSession } from \"next-auth/react\";\n\ninterface Message {\n  id: string;\n  content: string;\n  isRead: boolean;\n  createdAt: string;\n  fromId: string;\n  toId: string;\n  adId?: string;\n  from: {\n    id: string;\n    name: string;\n    avatar?: string;\n  };\n  to: {\n    id: string;\n    name: string;\n    avatar?: string;\n  };\n  ad?: {\n    id: string;\n    title: string;\n    price: number;\n    imageUrls: string[];\n  };\n}\n\ninterface Conversation {\n  id: string;\n  content: string;\n  createdAt: string;\n  adId?: string;\n  otherUser: {\n    id: string;\n    name: string;\n    avatar?: string;\n  };\n  ad?: {\n    id: string;\n    title: string;\n    price: number;\n    imageUrls: string[];\n  };\n  unreadCount: number;\n  isOnline?: boolean;\n}\n\ninterface UseMessagesOptions {\n  conversationWith?: string;\n  adId?: string;\n  enableRealTime?: boolean;\n  pollingInterval?: number;\n}\n\nexport function useMessages(options: UseMessagesOptions = {}) {\n  const { data: session } = useSession();\n  const {\n    conversationWith,\n    adId,\n    enableRealTime = true,\n    pollingInterval = 30000, // 30 seconds\n  } = options;\n\n  const [conversations, setConversations] = useState<Conversation[]>([]);\n  const [messages, setMessages] = useState<Message[]>([]);\n  const [loading, setLoading] = useState(true);\n  const [sending, setSending] = useState(false);\n  const [error, setError] = useState<string | null>(null);\n  const [hasMore, setHasMore] = useState(false);\n  const [lastMessageId, setLastMessageId] = useState<string | null>(null);\n\n  const eventSourceRef = useRef<EventSource | null>(null);\n  const pollingIntervalRef = useRef<NodeJS.Timeout | null>(null);\n  const isConnectedRef = useRef(false);\n\n  // جلب المحادثات\n  const fetchConversations = useCallback(async () => {\n    if (!session?.user?.id) return;\n\n    try {\n      const response = await fetch(\"/api/messages\");\n      const data = await response.json();\n\n      if (data.success) {\n        setConversations(data.data);\n      } else {\n        setError(data.error || \"حدث خطأ في جلب المحادثات\");\n      }\n    } catch (error) {\n      console.error(\"Error fetching conversations:\", error);\n      setError(\"حدث خطأ في جلب المحادثات\");\n    }\n  }, [session?.user?.id]);\n\n  // جلب الرسائل لمحادثة محددة\n  const fetchMessages = useCallback(\n    async (userId: string, adId?: string, lastMsgId?: string) => {\n      if (!session?.user?.id) return;\n\n      try {\n        const url = new URL(\"/api/messages\", window.location.origin);\n        url.searchParams.set(\"with\", userId);\n        if (adId) url.searchParams.set(\"adId\", adId);\n        if (lastMsgId) url.searchParams.set(\"lastMessageId\", lastMsgId);\n\n        const response = await fetch(url.toString());\n        const data = await response.json();\n\n        if (data.success) {\n          if (lastMsgId) {\n            // إضافة الرسائل الجديدة\n            setMessages((prev) => [...prev, ...data.data]);\n          } else {\n            // استبدال الرسائل\n            setMessages(data.data);\n          }\n          setHasMore(data.hasMore || false);\n          setLastMessageId(data.lastMessageId);\n        } else {\n          setError(data.error || \"حدث خطأ في جلب الرسائل\");\n        }\n      } catch (error) {\n        console.error(\"Error fetching messages:\", error);\n        setError(\"حدث خطأ في جلب الرسائل\");\n      }\n    },\n    [session?.user?.id]\n  );\n\n  // إرسال رسالة\n  const sendMessage = useCallback(\n    async (content: string, toId: string, adId?: string) => {\n      if (!content.trim() || sending || !session?.user?.id) return false;\n\n      setSending(true);\n      setError(null);\n\n      // Optimistic update\n      const tempId = `temp-${Date.now()}`;\n      const optimisticMessage: Message = {\n        id: tempId,\n        content: content.trim(),\n        isRead: false,\n        createdAt: new Date().toISOString(),\n        fromId: session.user.id,\n        toId,\n        adId,\n        from: {\n          id: session.user.id,\n          name: session.user.name || \"\",\n          avatar: session.user.avatar,\n        },\n        to: {\n          id: toId,\n          name: \"مستخدم غير معروف\",\n          avatar: undefined,\n        },\n      };\n\n      setMessages((prev) => [...prev, optimisticMessage]);\n\n      try {\n        const response = await fetch(\"/api/messages\", {\n          method: \"POST\",\n          headers: {\n            \"Content-Type\": \"application/json\",\n          },\n          body: JSON.stringify({\n            toId,\n            content: content.trim(),\n            ...(adId && { adId }),\n          }),\n        });\n\n        const data = await response.json();\n\n        if (data.success) {\n          // استبدال الرسالة المؤقتة بالرسالة الحقيقية\n          setMessages((prev) =>\n            prev.map((msg) => (msg.id === tempId ? data.data : msg))\n          );\n          \n          // تحديث قائمة المحادثات\n          fetchConversations();\n          \n          return true;\n        } else {\n          // إزالة الرسالة المؤقتة\n          setMessages((prev) => prev.filter((msg) => msg.id !== tempId));\n          setError(data.error || \"حدث خطأ في إرسال الرسالة\");\n          return false;\n        }\n      } catch (error) {\n        console.error(\"Error sending message:\", error);\n        setMessages((prev) => prev.filter((msg) => msg.id !== tempId));\n        setError(\"حدث خطأ في إرسال الرسالة\");\n        return false;\n      } finally {\n        setSending(false);\n      }\n    },\n    [sending, session?.user?.id, fetchConversations]\n  );\n\n  // تحميل المزيد من الرسائل\n  const loadMoreMessages = useCallback(() => {\n    if (conversationWith && lastMessageId && hasMore) {\n      fetchMessages(conversationWith, adId, lastMessageId);\n    }\n  }, [conversationWith, adId, lastMessageId, hasMore, fetchMessages]);\n\n  // إعداد Server-Sent Events\n  const setupSSE = useCallback(() => {\n    if (!session?.user?.id || !enableRealTime || isConnectedRef.current) return;\n\n    try {\n      const eventSource = new EventSource(\"/api/messages/sse\");\n      eventSourceRef.current = eventSource;\n      isConnectedRef.current = true;\n\n      eventSource.onmessage = (event) => {\n        try {\n          const data = JSON.parse(event.data);\n          \n          switch (data.type) {\n            case \"connected\":\n              console.log(\"Connected to real-time messaging\");\n              break;\n              \n            case \"new_message\":\n              const newMessage = data.data;\n              \n              // إضافة الرسالة إذا كانت للمحادثة الحالية\n              if (conversationWith && \n                  (newMessage.fromId === conversationWith || newMessage.toId === conversationWith)) {\n                setMessages((prev) => {\n                  // تجنب الرسائل المكررة\n                  if (prev.some(msg => msg.id === newMessage.id)) {\n                    return prev;\n                  }\n                  return [...prev, newMessage];\n                });\n              }\n              \n              // تحديث قائمة المحادثات\n              fetchConversations();\n              break;\n              \n            case \"ping\":\n              // مجرد ping للحفاظ على الاتصال\n              break;\n              \n            default:\n              console.log(\"Unknown SSE message type:\", data.type);\n          }\n        } catch (error) {\n          console.error(\"Error parsing SSE message:\", error);\n        }\n      };\n\n      eventSource.onerror = (error) => {\n        console.error(\"SSE connection error:\", error);\n        isConnectedRef.current = false;\n        \n        // إعادة المحاولة بعد 5 ثوان\n        setTimeout(() => {\n          if (session?.user?.id && enableRealTime) {\n            setupSSE();\n          }\n        }, 5000);\n      };\n\n    } catch (error) {\n      console.error(\"Error setting up SSE:\", error);\n      isConnectedRef.current = false;\n    }\n  }, [session?.user?.id, enableRealTime, conversationWith, fetchConversations]);\n\n  // إعداد Polling كبديل احتياطي\n  const setupPolling = useCallback(() => {\n    if (!session?.user?.id || enableRealTime || pollingIntervalRef.current) return;\n\n    pollingIntervalRef.current = setInterval(() => {\n      if (conversationWith) {\n        fetchMessages(conversationWith, adId);\n      } else {\n        fetchConversations();\n      }\n    }, pollingInterval);\n  }, [session?.user?.id, enableRealTime, conversationWith, adId, pollingInterval, fetchMessages, fetchConversations]);\n\n  // تنظيف الاتصالات\n  const cleanup = useCallback(() => {\n    if (eventSourceRef.current) {\n      eventSourceRef.current.close();\n      eventSourceRef.current = null;\n      isConnectedRef.current = false;\n    }\n    \n    if (pollingIntervalRef.current) {\n      clearInterval(pollingIntervalRef.current);\n      pollingIntervalRef.current = null;\n    }\n  }, []);\n\n  // Effects\n  useEffect(() => {\n    if (session?.user?.id) {\n      setLoading(true);\n      \n      if (conversationWith) {\n        fetchMessages(conversationWith, adId).finally(() => setLoading(false));\n      } else {\n        fetchConversations().finally(() => setLoading(false));\n      }\n    }\n  }, [session?.user?.id, conversationWith, adId, fetchMessages, fetchConversations]);\n\n  useEffect(() => {\n    if (session?.user?.id) {\n      if (enableRealTime) {\n        setupSSE();\n      } else {\n        setupPolling();\n      }\n    }\n\n    return cleanup;\n  }, [session?.user?.id, enableRealTime, setupSSE, setupPolling, cleanup]);\n\n  return {\n    conversations,\n    messages,\n    loading,\n    sending,\n    error,\n    hasMore,\n    sendMessage,\n    loadMoreMessages,\n    fetchConversations,\n    fetchMessages,\n    setError,\n  };\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAuDO,SAAS,YAAY,UAA8B,CAAC,CAAC;IAC1D,MAAM,EAAE,MAAM,OAAO,EAAE,GAAG,CAAA,GAAA,8IAAA,CAAA,aAAU,AAAD;IACnC,MAAM,EACJ,gBAAgB,EAChB,IAAI,EACJ,iBAAiB,IAAI,EACrB,kBAAkB,KAAK,EACxB,GAAG;IAEJ,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAkB,EAAE;IACrE,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAa,EAAE;IACtD,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IAClD,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IAElE,MAAM,iBAAiB,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAsB;IAClD,MAAM,qBAAqB,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAyB;IACzD,MAAM,iBAAiB,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAE;IAE9B,gBAAgB;IAChB,MAAM,qBAAqB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;QACrC,IAAI,CAAC,SAAS,MAAM,IAAI;QAExB,IAAI;YACF,MAAM,WAAW,MAAM,MAAM;YAC7B,MAAM,OAAO,MAAM,SAAS,IAAI;YAEhC,IAAI,KAAK,OAAO,EAAE;gBAChB,iBAAiB,KAAK,IAAI;YAC5B,OAAO;gBACL,SAAS,KAAK,KAAK,IAAI;YACzB;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,iCAAiC;YAC/C,SAAS;QACX;IACF,GAAG;QAAC,SAAS,MAAM;KAAG;IAEtB,4BAA4B;IAC5B,MAAM,gBAAgB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAC9B,OAAO,QAAgB,MAAe;QACpC,IAAI,CAAC,SAAS,MAAM,IAAI;QAExB,IAAI;YACF,MAAM,MAAM,IAAI,IAAI,iBAAiB,OAAO,QAAQ,CAAC,MAAM;YAC3D,IAAI,YAAY,CAAC,GAAG,CAAC,QAAQ;YAC7B,IAAI,MAAM,IAAI,YAAY,CAAC,GAAG,CAAC,QAAQ;YACvC,IAAI,WAAW,IAAI,YAAY,CAAC,GAAG,CAAC,iBAAiB;YAErD,MAAM,WAAW,MAAM,MAAM,IAAI,QAAQ;YACzC,MAAM,OAAO,MAAM,SAAS,IAAI;YAEhC,IAAI,KAAK,OAAO,EAAE;gBAChB,IAAI,WAAW;oBACb,wBAAwB;oBACxB,YAAY,CAAC,OAAS;+BAAI;+BAAS,KAAK,IAAI;yBAAC;gBAC/C,OAAO;oBACL,kBAAkB;oBAClB,YAAY,KAAK,IAAI;gBACvB;gBACA,WAAW,KAAK,OAAO,IAAI;gBAC3B,iBAAiB,KAAK,aAAa;YACrC,OAAO;gBACL,SAAS,KAAK,KAAK,IAAI;YACzB;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,4BAA4B;YAC1C,SAAS;QACX;IACF,GACA;QAAC,SAAS,MAAM;KAAG;IAGrB,cAAc;IACd,MAAM,cAAc,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAC5B,OAAO,SAAiB,MAAc;QACpC,IAAI,CAAC,QAAQ,IAAI,MAAM,WAAW,CAAC,SAAS,MAAM,IAAI,OAAO;QAE7D,WAAW;QACX,SAAS;QAET,oBAAoB;QACpB,MAAM,SAAS,CAAC,KAAK,EAAE,KAAK,GAAG,IAAI;QACnC,MAAM,oBAA6B;YACjC,IAAI;YACJ,SAAS,QAAQ,IAAI;YACrB,QAAQ;YACR,WAAW,IAAI,OAAO,WAAW;YACjC,QAAQ,QAAQ,IAAI,CAAC,EAAE;YACvB;YACA;YACA,MAAM;gBACJ,IAAI,QAAQ,IAAI,CAAC,EAAE;gBACnB,MAAM,QAAQ,IAAI,CAAC,IAAI,IAAI;gBAC3B,QAAQ,QAAQ,IAAI,CAAC,MAAM;YAC7B;YACA,IAAI;gBACF,IAAI;gBACJ,MAAM;gBACN,QAAQ;YACV;QACF;QAEA,YAAY,CAAC,OAAS;mBAAI;gBAAM;aAAkB;QAElD,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,iBAAiB;gBAC5C,QAAQ;gBACR,SAAS;oBACP,gBAAgB;gBAClB;gBACA,MAAM,KAAK,SAAS,CAAC;oBACnB;oBACA,SAAS,QAAQ,IAAI;oBACrB,GAAI,QAAQ;wBAAE;oBAAK,CAAC;gBACtB;YACF;YAEA,MAAM,OAAO,MAAM,SAAS,IAAI;YAEhC,IAAI,KAAK,OAAO,EAAE;gBAChB,4CAA4C;gBAC5C,YAAY,CAAC,OACX,KAAK,GAAG,CAAC,CAAC,MAAS,IAAI,EAAE,KAAK,SAAS,KAAK,IAAI,GAAG;gBAGrD,wBAAwB;gBACxB;gBAEA,OAAO;YACT,OAAO;gBACL,wBAAwB;gBACxB,YAAY,CAAC,OAAS,KAAK,MAAM,CAAC,CAAC,MAAQ,IAAI,EAAE,KAAK;gBACtD,SAAS,KAAK,KAAK,IAAI;gBACvB,OAAO;YACT;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,0BAA0B;YACxC,YAAY,CAAC,OAAS,KAAK,MAAM,CAAC,CAAC,MAAQ,IAAI,EAAE,KAAK;YACtD,SAAS;YACT,OAAO;QACT,SAAU;YACR,WAAW;QACb;IACF,GACA;QAAC;QAAS,SAAS,MAAM;QAAI;KAAmB;IAGlD,0BAA0B;IAC1B,MAAM,mBAAmB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;QACnC,IAAI,oBAAoB,iBAAiB,SAAS;YAChD,cAAc,kBAAkB,MAAM;QACxC;IACF,GAAG;QAAC;QAAkB;QAAM;QAAe;QAAS;KAAc;IAElE,2BAA2B;IAC3B,MAAM,WAAW,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;QAC3B,IAAI,CAAC,SAAS,MAAM,MAAM,CAAC,kBAAkB,eAAe,OAAO,EAAE;QAErE,IAAI;YACF,MAAM,cAAc,IAAI,YAAY;YACpC,eAAe,OAAO,GAAG;YACzB,eAAe,OAAO,GAAG;YAEzB,YAAY,SAAS,GAAG,CAAC;gBACvB,IAAI;oBACF,MAAM,OAAO,KAAK,KAAK,CAAC,MAAM,IAAI;oBAElC,OAAQ,KAAK,IAAI;wBACf,KAAK;4BACH,QAAQ,GAAG,CAAC;4BACZ;wBAEF,KAAK;4BACH,MAAM,aAAa,KAAK,IAAI;4BAE5B,0CAA0C;4BAC1C,IAAI,oBACA,CAAC,WAAW,MAAM,KAAK,oBAAoB,WAAW,IAAI,KAAK,gBAAgB,GAAG;gCACpF,YAAY,CAAC;oCACX,uBAAuB;oCACvB,IAAI,KAAK,IAAI,CAAC,CAAA,MAAO,IAAI,EAAE,KAAK,WAAW,EAAE,GAAG;wCAC9C,OAAO;oCACT;oCACA,OAAO;2CAAI;wCAAM;qCAAW;gCAC9B;4BACF;4BAEA,wBAAwB;4BACxB;4BACA;wBAEF,KAAK;4BAEH;wBAEF;4BACE,QAAQ,GAAG,CAAC,6BAA6B,KAAK,IAAI;oBACtD;gBACF,EAAE,OAAO,OAAO;oBACd,QAAQ,KAAK,CAAC,8BAA8B;gBAC9C;YACF;YAEA,YAAY,OAAO,GAAG,CAAC;gBACrB,QAAQ,KAAK,CAAC,yBAAyB;gBACvC,eAAe,OAAO,GAAG;gBAEzB,4BAA4B;gBAC5B,WAAW;oBACT,IAAI,SAAS,MAAM,MAAM,gBAAgB;wBACvC;oBACF;gBACF,GAAG;YACL;QAEF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,yBAAyB;YACvC,eAAe,OAAO,GAAG;QAC3B;IACF,GAAG;QAAC,SAAS,MAAM;QAAI;QAAgB;QAAkB;KAAmB;IAE5E,8BAA8B;IAC9B,MAAM,eAAe,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;QAC/B,IAAI,CAAC,SAAS,MAAM,MAAM,kBAAkB,mBAAmB,OAAO,EAAE;QAExE,mBAAmB,OAAO,GAAG,YAAY;YACvC,IAAI,kBAAkB;gBACpB,cAAc,kBAAkB;YAClC,OAAO;gBACL;YACF;QACF,GAAG;IACL,GAAG;QAAC,SAAS,MAAM;QAAI;QAAgB;QAAkB;QAAM;QAAiB;QAAe;KAAmB;IAElH,kBAAkB;IAClB,MAAM,UAAU,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;QAC1B,IAAI,eAAe,OAAO,EAAE;YAC1B,eAAe,OAAO,CAAC,KAAK;YAC5B,eAAe,OAAO,GAAG;YACzB,eAAe,OAAO,GAAG;QAC3B;QAEA,IAAI,mBAAmB,OAAO,EAAE;YAC9B,cAAc,mBAAmB,OAAO;YACxC,mBAAmB,OAAO,GAAG;QAC/B;IACF,GAAG,EAAE;IAEL,UAAU;IACV,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,SAAS,MAAM,IAAI;YACrB,WAAW;YAEX,IAAI,kBAAkB;gBACpB,cAAc,kBAAkB,MAAM,OAAO,CAAC,IAAM,WAAW;YACjE,OAAO;gBACL,qBAAqB,OAAO,CAAC,IAAM,WAAW;YAChD;QACF;IACF,GAAG;QAAC,SAAS,MAAM;QAAI;QAAkB;QAAM;QAAe;KAAmB;IAEjF,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,SAAS,MAAM,IAAI;YACrB,IAAI,gBAAgB;gBAClB;YACF,OAAO;gBACL;YACF;QACF;QAEA,OAAO;IACT,GAAG;QAAC,SAAS,MAAM;QAAI;QAAgB;QAAU;QAAc;KAAQ;IAEvE,OAAO;QACL;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;IACF;AACF", "debugId": null}}, {"offset": {"line": 1332, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/kaliha/khalihatanbaa/src/app/messages/page.tsx"], "sourcesContent": ["\"use client\";\n\nimport { useState, useEffect } from \"react\";\nimport { useSession } from \"next-auth/react\";\nimport { useRouter, useSearchParams } from \"next/navigation\";\nimport { Header } from \"@/components/layout/Header\";\nimport { Footer } from \"@/components/layout/Footer\";\nimport { PlaceholderImage } from \"@/components/ui/PlaceholderImage\";\nimport {\n  PaperAirplaneIcon,\n  ChatBubbleLeftRightIcon,\n  UserIcon,\n  ClockIcon,\n  ExclamationCircleIcon,\n  CheckCircleIcon,\n} from \"@heroicons/react/24/outline\";\nimport { ClientOnly } from \"@/components/ClientOnly\";\nimport { useMessages } from \"@/hooks/useMessages\";\nimport {\n  ConversationList,\n  ConversationStats,\n} from \"@/components/messaging/ConversationList\";\nimport { ChatArea } from \"@/components/messaging/ChatArea\";\nimport { getTotalUnreadCount } from \"@/lib/messaging\";\n\nexport default function MessagesPage() {\n  const { data: session, status } = useSession();\n  const router = useRouter();\n  const searchParams = useSearchParams();\n\n  const [selectedConversation, setSelectedConversation] = useState<\n    string | null\n  >(null);\n  const [newMessage, setNewMessage] = useState(\"\");\n\n  // استخدام الـ hook المحسن\n  const {\n    conversations,\n    messages,\n    loading,\n    sending,\n    error,\n    sendMessage: sendMessageHook,\n    fetchConversations,\n  } = useMessages({\n    conversationWith: selectedConversation || undefined,\n    adId: searchParams.get(\"ad\") || undefined,\n    enableRealTime: true,\n  });\n\n  // دالة إرسال الرسالة المحسنة\n  const sendMessage = async () => {\n    if (!newMessage.trim() || !selectedConversation || sending) return;\n\n    const success = await sendMessageHook(\n      newMessage.trim(),\n      selectedConversation,\n      searchParams.get(\"ad\") || undefined\n    );\n\n    if (success) {\n      setNewMessage(\"\");\n    }\n  };\n\n  // Effects\n  useEffect(() => {\n    if (status === \"authenticated\") {\n      // التحقق من وجود محادثة محددة في URL\n      const withUser = searchParams.get(\"with\") || searchParams.get(\"user\");\n\n      if (withUser) {\n        setSelectedConversation(withUser);\n      }\n    }\n  }, [searchParams, status]);\n\n  useEffect(() => {\n    if (status === \"unauthenticated\") {\n      router.push(\"/auth/login\");\n    }\n  }, [status, router]);\n\n  // إعادة توجيه إذا لم يكن المستخدم مسجل دخول\n  if (status === \"loading\") {\n    return (\n      <div className=\"min-h-screen flex items-center justify-center\">\n        جاري التحميل...\n      </div>\n    );\n  }\n\n  if (status === \"unauthenticated\") {\n    return null;\n  }\n\n  const selectConversation = (conversation: any) => {\n    setSelectedConversation(conversation.otherUser.id);\n\n    // تحديث URL\n    const url = new URL(window.location.href);\n    url.searchParams.set(\"with\", conversation.otherUser.id);\n    if (conversation.adId) {\n      url.searchParams.set(\"ad\", conversation.adId);\n    } else {\n      url.searchParams.delete(\"ad\");\n    }\n    window.history.pushState({}, \"\", url.toString());\n  };\n\n  const formatTime = (dateString: string) => {\n    const date = new Date(dateString);\n    return date.toLocaleTimeString(\"ar-SY\", {\n      hour: \"2-digit\",\n      minute: \"2-digit\",\n      hour12: false,\n    });\n  };\n\n  if (loading) {\n    return (\n      <div className=\"min-h-screen bg-background\">\n        <Header />\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8\">\n          <div className=\"animate-pulse\">\n            <div className=\"h-8 bg-gray-200 rounded w-1/4 mb-6\"></div>\n            <div className=\"grid grid-cols-1 lg:grid-cols-3 gap-6\">\n              <div className=\"h-96 bg-gray-200 rounded-lg\"></div>\n              <div className=\"lg:col-span-2 h-96 bg-gray-200 rounded-lg\"></div>\n            </div>\n          </div>\n        </div>\n        <Footer />\n      </div>\n    );\n  }\n\n  return (\n    <ClientOnly\n      fallback={\n        <div className=\"min-h-screen bg-background\">\n          <Header />\n          <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8\">\n            <div className=\"animate-pulse\">\n              <div className=\"h-8 bg-gray-200 rounded w-1/4 mb-6\"></div>\n              <div className=\"grid grid-cols-1 lg:grid-cols-3 gap-6\">\n                <div className=\"h-96 bg-gray-200 rounded-lg\"></div>\n                <div className=\"lg:col-span-2 h-96 bg-gray-200 rounded-lg\"></div>\n              </div>\n            </div>\n          </div>\n          <Footer />\n        </div>\n      }\n    >\n      <div className=\"min-h-screen bg-background\">\n        <Header />\n\n        <main className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8\">\n          <h1 className=\"text-2xl font-bold text-dark-800 mb-6\">الرسائل</h1>\n\n          {/* عرض الأخطاء */}\n          {error && (\n            <div className=\"mb-4 p-4 bg-red-50 border border-red-200 rounded-lg flex items-center space-x-2 space-x-reverse\">\n              <ExclamationCircleIcon className=\"h-5 w-5 text-red-500 flex-shrink-0\" />\n              <p className=\"text-red-700\">{error}</p>\n            </div>\n          )}\n\n          <div className=\"grid grid-cols-1 lg:grid-cols-3 gap-6 h-[600px]\">\n            {/* قائمة المحادثات */}\n            <div className=\"bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden\">\n              <div className=\"p-4 border-b border-gray-200\">\n                <h2 className=\"font-semibold text-dark-800\">المحادثات</h2>\n              </div>\n\n              <div className=\"overflow-y-auto h-full\">\n                {conversations.length === 0 ? (\n                  <div className=\"p-6 text-center text-gray-500\">\n                    <ChatBubbleLeftRightIcon className=\"h-12 w-12 mx-auto mb-3 text-gray-300\" />\n                    <p>لا توجد محادثات حتى الآن</p>\n                    <p className=\"text-sm mt-1\">\n                      ابدأ محادثة من خلال التواصل مع بائع\n                    </p>\n                  </div>\n                ) : (\n                  conversations.map((conversation) => (\n                    <button\n                      key={`${conversation.otherUser.id}-${\n                        conversation.adId || \"general\"\n                      }`}\n                      onClick={() => selectConversation(conversation)}\n                      className={`w-full p-4 border-b border-gray-100 hover:bg-gray-50 text-right transition-colors ${\n                        selectedConversation === conversation.otherUser.id\n                          ? \"bg-primary-50\"\n                          : \"\"\n                      }`}\n                    >\n                      <div className=\"flex items-start space-x-3 space-x-reverse\">\n                        <div className=\"relative w-10 h-10 bg-primary-100 rounded-full flex items-center justify-center flex-shrink-0\">\n                          {conversation.otherUser.avatar ? (\n                            <img\n                              src={conversation.otherUser.avatar}\n                              alt={conversation.otherUser.name}\n                              className=\"w-full h-full rounded-full object-cover\"\n                            />\n                          ) : (\n                            <UserIcon className=\"h-5 w-5 text-primary-600\" />\n                          )}\n                          {/* مؤشر الحالة المتصلة */}\n                          {conversation.isOnline && (\n                            <div className=\"absolute -bottom-0.5 -right-0.5 w-3 h-3 bg-green-500 border-2 border-white rounded-full\"></div>\n                          )}\n                        </div>\n\n                        <div className=\"flex-1 min-w-0\">\n                          <div className=\"flex items-center justify-between mb-1\">\n                            <p className=\"font-medium text-dark-800 truncate\">\n                              {conversation.otherUser.name}\n                            </p>\n                            {conversation.unreadCount > 0 && (\n                              <span className=\"bg-primary-500 text-white text-xs rounded-full px-2 py-1 min-w-[20px] text-center\">\n                                {conversation.unreadCount}\n                              </span>\n                            )}\n                          </div>\n\n                          {conversation.ad && (\n                            <p className=\"text-sm text-gray-600 truncate mb-1\">\n                              {conversation.ad.title}\n                            </p>\n                          )}\n\n                          <p className=\"text-sm text-gray-500 truncate\">\n                            {conversation.content}\n                          </p>\n\n                          <div className=\"flex items-center mt-1\">\n                            <ClockIcon className=\"h-3 w-3 text-gray-400 ml-1\" />\n                            <span className=\"text-xs text-gray-400\">\n                              {formatTime(conversation.createdAt)}\n                            </span>\n                          </div>\n                        </div>\n                      </div>\n                    </button>\n                  ))\n                )}\n              </div>\n            </div>\n\n            {/* منطقة المحادثة */}\n            <div className=\"lg:col-span-2 bg-white rounded-lg shadow-sm border border-gray-200 flex flex-col\">\n              {selectedConversation ? (\n                <>\n                  {/* رأس المحادثة */}\n                  <div className=\"p-4 border-b border-gray-200\">\n                    <div className=\"flex items-center space-x-3 space-x-reverse\">\n                      <div className=\"w-10 h-10 bg-primary-100 rounded-full flex items-center justify-center\">\n                        <UserIcon className=\"h-5 w-5 text-primary-600\" />\n                      </div>\n                      <div>\n                        <p className=\"font-medium text-dark-800\">\n                          {\n                            conversations.find(\n                              (c) => c.otherUser.id === selectedConversation\n                            )?.otherUser.name\n                          }\n                        </p>\n                        {searchParams.get(\"ad\") && (\n                          <p className=\"text-sm text-gray-500\">\n                            حول:{\" \"}\n                            {\n                              conversations.find(\n                                (c) => c.otherUser.id === selectedConversation\n                              )?.ad?.title\n                            }\n                          </p>\n                        )}\n                      </div>\n                    </div>\n                  </div>\n\n                  {/* الرسائل */}\n                  <div className=\"flex-1 overflow-y-auto p-4 space-y-4\">\n                    {messages.map((message) => (\n                      <div\n                        key={message.id}\n                        className={`flex ${\n                          message.fromId === session?.user?.id\n                            ? \"justify-end\"\n                            : \"justify-start\"\n                        }`}\n                      >\n                        <div\n                          className={`max-w-xs lg:max-w-md px-4 py-2 rounded-lg ${\n                            message.fromId === session?.user?.id\n                              ? \"bg-primary-500 text-white\"\n                              : \"bg-gray-100 text-dark-800\"\n                          }`}\n                        >\n                          <p className=\"text-sm\">{message.content}</p>\n                          <p\n                            className={`text-xs mt-1 ${\n                              message.fromId === session?.user?.id\n                                ? \"text-primary-100\"\n                                : \"text-gray-500\"\n                            }`}\n                          >\n                            {formatTime(message.createdAt)}\n                          </p>\n                        </div>\n                      </div>\n                    ))}\n                  </div>\n\n                  {/* إرسال رسالة */}\n                  <div className=\"p-4 border-t border-gray-200\">\n                    <div className=\"flex space-x-2 space-x-reverse\">\n                      <input\n                        type=\"text\"\n                        value={newMessage}\n                        onChange={(e) => setNewMessage(e.target.value)}\n                        onKeyDown={(e) =>\n                          e.key === \"Enter\" && !e.shiftKey && sendMessage()\n                        }\n                        placeholder=\"اكتب رسالتك...\"\n                        className=\"flex-1 px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent\"\n                        disabled={sending}\n                      />\n                      <button\n                        onClick={sendMessage}\n                        disabled={!newMessage.trim() || sending}\n                        className=\"px-4 py-2 bg-primary-500 text-white rounded-lg hover:bg-primary-600 disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center\"\n                      >\n                        {sending ? (\n                          <div className=\"w-5 h-5 border-2 border-white border-t-transparent rounded-full animate-spin\"></div>\n                        ) : (\n                          <PaperAirplaneIcon className=\"h-5 w-5\" />\n                        )}\n                      </button>\n                    </div>\n                  </div>\n                </>\n              ) : (\n                <div className=\"flex-1 flex items-center justify-center text-gray-500\">\n                  <div className=\"text-center\">\n                    <ChatBubbleLeftRightIcon className=\"h-16 w-16 mx-auto mb-4 text-gray-300\" />\n                    <p className=\"text-lg font-medium\">\n                      اختر محادثة لبدء المراسلة\n                    </p>\n                    <p className=\"text-sm mt-1\">\n                      اختر محادثة من القائمة الجانبية\n                    </p>\n                  </div>\n                </div>\n              )}\n            </div>\n          </div>\n        </main>\n\n        <Footer />\n      </div>\n    </ClientOnly>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AAEA;AAAA;AAAA;AAAA;AAAA;AAQA;AACA;AAjBA;;;;;;;;;;AAyBe,SAAS;IACtB,MAAM,EAAE,MAAM,OAAO,EAAE,MAAM,EAAE,GAAG,CAAA,GAAA,8IAAA,CAAA,aAAU,AAAD;IAC3C,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,eAAe,CAAA,GAAA,kIAAA,CAAA,kBAAe,AAAD;IAEnC,MAAM,CAAC,sBAAsB,wBAAwB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAE7D;IACF,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE7C,0BAA0B;IAC1B,MAAM,EACJ,aAAa,EACb,QAAQ,EACR,OAAO,EACP,OAAO,EACP,KAAK,EACL,aAAa,eAAe,EAC5B,kBAAkB,EACnB,GAAG,CAAA,GAAA,2HAAA,CAAA,cAAW,AAAD,EAAE;QACd,kBAAkB,wBAAwB;QAC1C,MAAM,aAAa,GAAG,CAAC,SAAS;QAChC,gBAAgB;IAClB;IAEA,6BAA6B;IAC7B,MAAM,cAAc;QAClB,IAAI,CAAC,WAAW,IAAI,MAAM,CAAC,wBAAwB,SAAS;QAE5D,MAAM,UAAU,MAAM,gBACpB,WAAW,IAAI,IACf,sBACA,aAAa,GAAG,CAAC,SAAS;QAG5B,IAAI,SAAS;YACX,cAAc;QAChB;IACF;IAEA,UAAU;IACV,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,WAAW,iBAAiB;YAC9B,qCAAqC;YACrC,MAAM,WAAW,aAAa,GAAG,CAAC,WAAW,aAAa,GAAG,CAAC;YAE9D,IAAI,UAAU;gBACZ,wBAAwB;YAC1B;QACF;IACF,GAAG;QAAC;QAAc;KAAO;IAEzB,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,WAAW,mBAAmB;YAChC,OAAO,IAAI,CAAC;QACd;IACF,GAAG;QAAC;QAAQ;KAAO;IAEnB,4CAA4C;IAC5C,IAAI,WAAW,WAAW;QACxB,qBACE,8OAAC;YAAI,WAAU;sBAAgD;;;;;;IAInE;IAEA,IAAI,WAAW,mBAAmB;QAChC,OAAO;IACT;IAEA,MAAM,qBAAqB,CAAC;QAC1B,wBAAwB,aAAa,SAAS,CAAC,EAAE;QAEjD,YAAY;QACZ,MAAM,MAAM,IAAI,IAAI,OAAO,QAAQ,CAAC,IAAI;QACxC,IAAI,YAAY,CAAC,GAAG,CAAC,QAAQ,aAAa,SAAS,CAAC,EAAE;QACtD,IAAI,aAAa,IAAI,EAAE;YACrB,IAAI,YAAY,CAAC,GAAG,CAAC,MAAM,aAAa,IAAI;QAC9C,OAAO;YACL,IAAI,YAAY,CAAC,MAAM,CAAC;QAC1B;QACA,OAAO,OAAO,CAAC,SAAS,CAAC,CAAC,GAAG,IAAI,IAAI,QAAQ;IAC/C;IAEA,MAAM,aAAa,CAAC;QAClB,MAAM,OAAO,IAAI,KAAK;QACtB,OAAO,KAAK,kBAAkB,CAAC,SAAS;YACtC,MAAM;YACN,QAAQ;YACR,QAAQ;QACV;IACF;IAEA,IAAI,SAAS;QACX,qBACE,8OAAC;YAAI,WAAU;;8BACb,8OAAC,sIAAA,CAAA,SAAM;;;;;8BACP,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;;;;;0CACf,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;;;;;kDACf,8OAAC;wCAAI,WAAU;;;;;;;;;;;;;;;;;;;;;;;8BAIrB,8OAAC,sIAAA,CAAA,SAAM;;;;;;;;;;;IAGb;IAEA,qBACE,8OAAC,gIAAA,CAAA,aAAU;QACT,wBACE,8OAAC;YAAI,WAAU;;8BACb,8OAAC,sIAAA,CAAA,SAAM;;;;;8BACP,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;;;;;0CACf,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;;;;;kDACf,8OAAC;wCAAI,WAAU;;;;;;;;;;;;;;;;;;;;;;;8BAIrB,8OAAC,sIAAA,CAAA,SAAM;;;;;;;;;;;kBAIX,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC,sIAAA,CAAA,SAAM;;;;;8BAEP,8OAAC;oBAAK,WAAU;;sCACd,8OAAC;4BAAG,WAAU;sCAAwC;;;;;;wBAGrD,uBACC,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,yOAAA,CAAA,wBAAqB;oCAAC,WAAU;;;;;;8CACjC,8OAAC;oCAAE,WAAU;8CAAgB;;;;;;;;;;;;sCAIjC,8OAAC;4BAAI,WAAU;;8CAEb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDAAG,WAAU;0DAA8B;;;;;;;;;;;sDAG9C,8OAAC;4CAAI,WAAU;sDACZ,cAAc,MAAM,KAAK,kBACxB,8OAAC;gDAAI,WAAU;;kEACb,8OAAC,6OAAA,CAAA,0BAAuB;wDAAC,WAAU;;;;;;kEACnC,8OAAC;kEAAE;;;;;;kEACH,8OAAC;wDAAE,WAAU;kEAAe;;;;;;;;;;;uDAK9B,cAAc,GAAG,CAAC,CAAC,6BACjB,8OAAC;oDAIC,SAAS,IAAM,mBAAmB;oDAClC,WAAW,CAAC,kFAAkF,EAC5F,yBAAyB,aAAa,SAAS,CAAC,EAAE,GAC9C,kBACA,IACJ;8DAEF,cAAA,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAI,WAAU;;oEACZ,aAAa,SAAS,CAAC,MAAM,iBAC5B,8OAAC;wEACC,KAAK,aAAa,SAAS,CAAC,MAAM;wEAClC,KAAK,aAAa,SAAS,CAAC,IAAI;wEAChC,WAAU;;;;;6FAGZ,8OAAC,+MAAA,CAAA,WAAQ;wEAAC,WAAU;;;;;;oEAGrB,aAAa,QAAQ,kBACpB,8OAAC;wEAAI,WAAU;;;;;;;;;;;;0EAInB,8OAAC;gEAAI,WAAU;;kFACb,8OAAC;wEAAI,WAAU;;0FACb,8OAAC;gFAAE,WAAU;0FACV,aAAa,SAAS,CAAC,IAAI;;;;;;4EAE7B,aAAa,WAAW,GAAG,mBAC1B,8OAAC;gFAAK,WAAU;0FACb,aAAa,WAAW;;;;;;;;;;;;oEAK9B,aAAa,EAAE,kBACd,8OAAC;wEAAE,WAAU;kFACV,aAAa,EAAE,CAAC,KAAK;;;;;;kFAI1B,8OAAC;wEAAE,WAAU;kFACV,aAAa,OAAO;;;;;;kFAGvB,8OAAC;wEAAI,WAAU;;0FACb,8OAAC,iNAAA,CAAA,YAAS;gFAAC,WAAU;;;;;;0FACrB,8OAAC;gFAAK,WAAU;0FACb,WAAW,aAAa,SAAS;;;;;;;;;;;;;;;;;;;;;;;;mDApDrC,GAAG,aAAa,SAAS,CAAC,EAAE,CAAC,CAAC,EACjC,aAAa,IAAI,IAAI,WACrB;;;;;;;;;;;;;;;;8CA8DZ,8OAAC;oCAAI,WAAU;8CACZ,qCACC;;0DAEE,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAI,WAAU;sEACb,cAAA,8OAAC,+MAAA,CAAA,WAAQ;gEAAC,WAAU;;;;;;;;;;;sEAEtB,8OAAC;;8EACC,8OAAC;oEAAE,WAAU;8EAET,cAAc,IAAI,CAChB,CAAC,IAAM,EAAE,SAAS,CAAC,EAAE,KAAK,uBACzB,UAAU;;;;;;gEAGhB,aAAa,GAAG,CAAC,uBAChB,8OAAC;oEAAE,WAAU;;wEAAwB;wEAC9B;wEAEH,cAAc,IAAI,CAChB,CAAC,IAAM,EAAE,SAAS,CAAC,EAAE,KAAK,uBACzB,IAAI;;;;;;;;;;;;;;;;;;;;;;;;0DASnB,8OAAC;gDAAI,WAAU;0DACZ,SAAS,GAAG,CAAC,CAAC,wBACb,8OAAC;wDAEC,WAAW,CAAC,KAAK,EACf,QAAQ,MAAM,KAAK,SAAS,MAAM,KAC9B,gBACA,iBACJ;kEAEF,cAAA,8OAAC;4DACC,WAAW,CAAC,0CAA0C,EACpD,QAAQ,MAAM,KAAK,SAAS,MAAM,KAC9B,8BACA,6BACJ;;8EAEF,8OAAC;oEAAE,WAAU;8EAAW,QAAQ,OAAO;;;;;;8EACvC,8OAAC;oEACC,WAAW,CAAC,aAAa,EACvB,QAAQ,MAAM,KAAK,SAAS,MAAM,KAC9B,qBACA,iBACJ;8EAED,WAAW,QAAQ,SAAS;;;;;;;;;;;;uDAtB5B,QAAQ,EAAE;;;;;;;;;;0DA8BrB,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DACC,MAAK;4DACL,OAAO;4DACP,UAAU,CAAC,IAAM,cAAc,EAAE,MAAM,CAAC,KAAK;4DAC7C,WAAW,CAAC,IACV,EAAE,GAAG,KAAK,WAAW,CAAC,EAAE,QAAQ,IAAI;4DAEtC,aAAY;4DACZ,WAAU;4DACV,UAAU;;;;;;sEAEZ,8OAAC;4DACC,SAAS;4DACT,UAAU,CAAC,WAAW,IAAI,MAAM;4DAChC,WAAU;sEAET,wBACC,8OAAC;gEAAI,WAAU;;;;;qFAEf,8OAAC,iOAAA,CAAA,oBAAiB;gEAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;qEAOvC,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,6OAAA,CAAA,0BAAuB;oDAAC,WAAU;;;;;;8DACnC,8OAAC;oDAAE,WAAU;8DAAsB;;;;;;8DAGnC,8OAAC;oDAAE,WAAU;8DAAe;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8BAUxC,8OAAC,sIAAA,CAAA,SAAM;;;;;;;;;;;;;;;;AAIf", "debugId": null}}]}