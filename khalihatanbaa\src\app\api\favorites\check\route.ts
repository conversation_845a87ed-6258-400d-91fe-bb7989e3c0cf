import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'

export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    if (!session?.user?.id) {
      return NextResponse.json(
        { success: false, error: 'يجب تسجيل الدخول أولاً' },
        { status: 401 }
      )
    }

    const { searchParams } = new URL(request.url)
    const adId = searchParams.get('adId')

    if (!adId) {
      return NextResponse.json(
        { success: false, error: 'معرف الإعلان مطلوب' },
        { status: 400 }
      )
    }

    // التحقق من وجود الإعلان في المفضلة
    const favorite = await prisma.favorite.findUnique({
      where: {
        userId_adId: {
          userId: session.user.id,
          adId: adId
        }
      }
    })

    return NextResponse.json({
      success: true,
      isFavorite: !!favorite
    })

  } catch (error) {
    console.error('Error checking favorite status:', error)
    return NextResponse.json(
      { success: false, error: 'حدث خطأ في التحقق من حالة المفضلة' },
      { status: 500 }
    )
  }
}
