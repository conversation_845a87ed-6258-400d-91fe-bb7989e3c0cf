import { NextRequest, NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import { authOptions } from "@/lib/auth";
import { prisma } from "@/lib/prisma";

// إنشاء طلب تجريبي مكتمل (للاختبار فقط)
export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user?.id) {
      return NextResponse.json(
        { success: false, error: "يجب تسجيل الدخول أولاً" },
        { status: 401 }
      );
    }

    const body = await request.json();
    const { adId, sellerId } = body;

    if (!adId || !sellerId) {
      return NextResponse.json(
        { success: false, error: "معرف الإعلان والبائع مطلوبان" },
        { status: 400 }
      );
    }

    // التحقق من وجود الإعلان والبائع
    const ad = await prisma.ad.findUnique({
      where: { id: adId },
      include: {
        user: {
          select: { id: true, name: true, avatar: true },
        },
      },
    });

    if (!ad) {
      return NextResponse.json(
        { success: false, error: "الإعلان غير موجود" },
        { status: 404 }
      );
    }

    if (ad.userId !== sellerId) {
      return NextResponse.json(
        { success: false, error: "البائع غير صحيح لهذا الإعلان" },
        { status: 400 }
      );
    }

    // التحقق من عدم وجود طلب سابق
    const existingOrder = await prisma.order.findUnique({
      where: {
        buyerId_adId: {
          buyerId: session.user.id,
          adId: adId,
        },
      },
    });

    if (existingOrder) {
      // إذا كان الطلب موجود، قم بتحديثه إلى مكتمل
      const updatedOrder = await prisma.order.update({
        where: { id: existingOrder.id },
        data: {
          status: "completed",
          completedAt: new Date(),
        },
        include: {
          ad: {
            select: { id: true, title: true, price: true },
          },
          seller: {
            select: { id: true, name: true, avatar: true },
          },
          buyer: {
            select: { id: true, name: true, avatar: true },
          },
        },
      });

      return NextResponse.json({
        success: true,
        data: updatedOrder,
        message: "تم تحديث الطلب إلى مكتمل",
      });
    }

    // إنشاء طلب جديد مكتمل
    const order = await prisma.order.create({
      data: {
        buyerId: session.user.id,
        sellerId: sellerId,
        adId: adId,
        totalPrice: ad.price,
        status: "completed",
        completedAt: new Date(),
        notes: "طلب تجريبي مكتمل للاختبار",
      },
      include: {
        ad: {
          select: { id: true, title: true, price: true },
        },
        seller: {
          select: { id: true, name: true, avatar: true },
        },
        buyer: {
          select: { id: true, name: true, avatar: true },
        },
      },
    });

    return NextResponse.json({
      success: true,
      data: order,
      message: "تم إنشاء طلب تجريبي مكتمل بنجاح",
    });
  } catch (error) {
    console.error("Error creating test order:", error);
    return NextResponse.json(
      { success: false, error: "حدث خطأ في إنشاء الطلب التجريبي" },
      { status: 500 }
    );
  }
}

// حذف جميع الطلبات التجريبية (للتنظيف)
export async function DELETE(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user?.id) {
      return NextResponse.json(
        { success: false, error: "يجب تسجيل الدخول أولاً" },
        { status: 401 }
      );
    }

    // حذف الطلبات التجريبية للمستخدم الحالي
    const deletedOrders = await prisma.order.deleteMany({
      where: {
        buyerId: session.user.id,
        notes: "طلب تجريبي مكتمل للاختبار",
      },
    });

    return NextResponse.json({
      success: true,
      data: { deletedCount: deletedOrders.count },
      message: `تم حذف ${deletedOrders.count} طلب تجريبي`,
    });
  } catch (error) {
    console.error("Error deleting test orders:", error);
    return NextResponse.json(
      { success: false, error: "حدث خطأ في حذف الطلبات التجريبية" },
      { status: 500 }
    );
  }
}
