{"version": 3, "sources": [], "sections": [{"offset": {"line": 31, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/kaliha/khalihatanbaa/src/components/layout/Header.tsx"], "sourcesContent": ["\"use client\";\n\nimport { useState } from \"react\";\nimport Link from \"next/link\";\nimport { useSession, signOut } from \"next-auth/react\";\nimport {\n  MagnifyingGlassIcon,\n  PlusIcon,\n  ChatBubbleLeftRightIcon,\n  HeartIcon,\n  UserIcon,\n  Bars3Icon,\n  XMarkIcon,\n  ChartBarIcon,\n} from \"@heroicons/react/24/outline\";\n\nexport function Header() {\n  const { data: session } = useSession();\n  const [isMenuOpen, setIsMenuOpen] = useState(false);\n  const [searchQuery, setSearchQuery] = useState(\"\");\n\n  const handleSearch = (e: React.FormEvent) => {\n    e.preventDefault();\n    if (searchQuery.trim()) {\n      window.location.href = `/search?q=${encodeURIComponent(searchQuery)}`;\n    }\n  };\n\n  return (\n    <header className=\"bg-white shadow-sm border-b border-gray-200 sticky top-0 z-50\">\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n        <div className=\"flex items-center justify-between h-16\">\n          {/* الشعار */}\n          <Link\n            href=\"/\"\n            className=\"flex items-center space-x-2 space-x-reverse\"\n          >\n            <div className=\"w-10 h-10 bg-primary-500 rounded-lg flex items-center justify-center\">\n              <span className=\"text-white font-bold text-lg\">خ</span>\n            </div>\n            <div className=\"hidden sm:block\">\n              <h1 className=\"text-xl font-bold text-dark-800\">\n                خَلّيها تنْباع\n              </h1>\n              <p className=\"text-xs text-dark-500\">منصة البيع والشراء</p>\n            </div>\n          </Link>\n\n          {/* شريط البحث */}\n          <div className=\"flex-1 max-w-lg mx-4\">\n            <form onSubmit={handleSearch} className=\"relative\">\n              <input\n                type=\"text\"\n                placeholder=\"ابحث عن أي شيء...\"\n                value={searchQuery}\n                onChange={(e) => setSearchQuery(e.target.value)}\n                className=\"w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent\"\n              />\n              <MagnifyingGlassIcon className=\"absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400\" />\n            </form>\n          </div>\n\n          {/* أزرار التنقل */}\n          <div className=\"flex items-center space-x-4 space-x-reverse\">\n            {session ? (\n              <>\n                {/* نشر إعلان */}\n                <Link\n                  href=\"/ads/new\"\n                  className=\"bg-primary-500 text-white px-4 py-2 rounded-lg hover:bg-primary-600 transition-colors flex items-center space-x-2 space-x-reverse\"\n                >\n                  <PlusIcon className=\"h-5 w-5\" />\n                  <span className=\"hidden sm:inline\">نشر إعلان</span>\n                </Link>\n\n                {/* الرسائل */}\n                <Link\n                  href=\"/messages\"\n                  className=\"p-2 text-dark-600 hover:text-primary-500 transition-colors relative\"\n                >\n                  <ChatBubbleLeftRightIcon className=\"h-6 w-6\" />\n                  {/* نقطة الإشعارات */}\n                  <span className=\"absolute -top-1 -right-1 h-3 w-3 bg-primary-500 rounded-full\"></span>\n                </Link>\n\n                {/* المفضلة */}\n                <Link\n                  href=\"/favorites\"\n                  className=\"p-2 text-dark-600 hover:text-primary-500 transition-colors\"\n                >\n                  <HeartIcon className=\"h-6 w-6\" />\n                </Link>\n\n                {/* لوحة التحكم */}\n                <Link\n                  href=\"/dashboard\"\n                  className=\"p-2 text-dark-600 hover:text-primary-500 transition-colors\"\n                >\n                  <ChartBarIcon className=\"h-6 w-6\" />\n                </Link>\n\n                {/* قائمة المستخدم */}\n                <div className=\"relative\">\n                  <button\n                    onClick={() => setIsMenuOpen(!isMenuOpen)}\n                    className=\"flex items-center space-x-2 space-x-reverse p-2 text-dark-600 hover:text-primary-500 transition-colors\"\n                  >\n                    <UserIcon className=\"h-6 w-6\" />\n                    <span className=\"hidden sm:inline\">\n                      {session.user.name}\n                    </span>\n                  </button>\n\n                  {isMenuOpen && (\n                    <div className=\"absolute right-0 mt-2 w-48 bg-white rounded-lg shadow-lg border border-gray-200 py-2\">\n                      <Link\n                        href=\"/profile\"\n                        className=\"block px-4 py-2 text-dark-700 hover:bg-gray-50\"\n                      >\n                        الملف الشخصي\n                      </Link>\n                      <Link\n                        href=\"/profile/ads\"\n                        className=\"block px-4 py-2 text-dark-700 hover:bg-gray-50\"\n                      >\n                        إعلاناتي\n                      </Link>\n                      <Link\n                        href=\"/profile/settings\"\n                        className=\"block px-4 py-2 text-dark-700 hover:bg-gray-50\"\n                      >\n                        الإعدادات\n                      </Link>\n                      <hr className=\"my-2\" />\n                      <button\n                        onClick={() => signOut()}\n                        className=\"block w-full text-right px-4 py-2 text-red-600 hover:bg-gray-50\"\n                      >\n                        تسجيل الخروج\n                      </button>\n                    </div>\n                  )}\n                </div>\n              </>\n            ) : (\n              <>\n                <Link\n                  href=\"/auth/login\"\n                  className=\"text-dark-600 hover:text-primary-500 transition-colors\"\n                >\n                  تسجيل الدخول\n                </Link>\n                <Link\n                  href=\"/auth/register\"\n                  className=\"bg-primary-500 text-white px-4 py-2 rounded-lg hover:bg-primary-600 transition-colors\"\n                >\n                  إنشاء حساب\n                </Link>\n              </>\n            )}\n\n            {/* قائمة الموبايل */}\n            <button\n              onClick={() => setIsMenuOpen(!isMenuOpen)}\n              className=\"sm:hidden p-2 text-dark-600\"\n            >\n              {isMenuOpen ? (\n                <XMarkIcon className=\"h-6 w-6\" />\n              ) : (\n                <Bars3Icon className=\"h-6 w-6\" />\n              )}\n            </button>\n          </div>\n        </div>\n\n        {/* قائمة الموبايل */}\n        {isMenuOpen && (\n          <div className=\"sm:hidden border-t border-gray-200 py-4\">\n            <div className=\"space-y-2\">\n              <Link\n                href=\"/ads\"\n                className=\"block px-4 py-2 text-dark-700 hover:bg-gray-50 rounded\"\n              >\n                جميع الإعلانات\n              </Link>\n              <Link\n                href=\"/categories\"\n                className=\"block px-4 py-2 text-dark-700 hover:bg-gray-50 rounded\"\n              >\n                جميع الفئات\n              </Link>\n              {session && (\n                <>\n                  <Link\n                    href=\"/ads/new\"\n                    className=\"block px-4 py-2 text-primary-600 hover:bg-gray-50 rounded\"\n                  >\n                    نشر إعلان جديد\n                  </Link>\n                  <Link\n                    href=\"/messages\"\n                    className=\"block px-4 py-2 text-dark-700 hover:bg-gray-50 rounded\"\n                  >\n                    الرسائل\n                  </Link>\n                  <Link\n                    href=\"/favorites\"\n                    className=\"block px-4 py-2 text-dark-700 hover:bg-gray-50 rounded\"\n                  >\n                    المفضلة\n                  </Link>\n                  <Link\n                    href=\"/dashboard\"\n                    className=\"block px-4 py-2 text-dark-700 hover:bg-gray-50 rounded\"\n                  >\n                    لوحة التحكم\n                  </Link>\n                </>\n              )}\n            </div>\n          </div>\n        )}\n      </div>\n    </header>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AALA;;;;;;AAgBO,SAAS;IACd,MAAM,EAAE,MAAM,OAAO,EAAE,GAAG,CAAA,GAAA,8IAAA,CAAA,aAAU,AAAD;IACnC,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE/C,MAAM,eAAe,CAAC;QACpB,EAAE,cAAc;QAChB,IAAI,YAAY,IAAI,IAAI;YACtB,OAAO,QAAQ,CAAC,IAAI,GAAG,CAAC,UAAU,EAAE,mBAAmB,cAAc;QACvE;IACF;IAEA,qBACE,8OAAC;QAAO,WAAU;kBAChB,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC,4JAAA,CAAA,UAAI;4BACH,MAAK;4BACL,WAAU;;8CAEV,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCAAK,WAAU;kDAA+B;;;;;;;;;;;8CAEjD,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAG,WAAU;sDAAkC;;;;;;sDAGhD,8OAAC;4CAAE,WAAU;sDAAwB;;;;;;;;;;;;;;;;;;sCAKzC,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAK,UAAU;gCAAc,WAAU;;kDACtC,8OAAC;wCACC,MAAK;wCACL,aAAY;wCACZ,OAAO;wCACP,UAAU,CAAC,IAAM,eAAe,EAAE,MAAM,CAAC,KAAK;wCAC9C,WAAU;;;;;;kDAEZ,8OAAC,qOAAA,CAAA,sBAAmB;wCAAC,WAAU;;;;;;;;;;;;;;;;;sCAKnC,8OAAC;4BAAI,WAAU;;gCACZ,wBACC;;sDAEE,8OAAC,4JAAA,CAAA,UAAI;4CACH,MAAK;4CACL,WAAU;;8DAEV,8OAAC,+MAAA,CAAA,WAAQ;oDAAC,WAAU;;;;;;8DACpB,8OAAC;oDAAK,WAAU;8DAAmB;;;;;;;;;;;;sDAIrC,8OAAC,4JAAA,CAAA,UAAI;4CACH,MAAK;4CACL,WAAU;;8DAEV,8OAAC,6OAAA,CAAA,0BAAuB;oDAAC,WAAU;;;;;;8DAEnC,8OAAC;oDAAK,WAAU;;;;;;;;;;;;sDAIlB,8OAAC,4JAAA,CAAA,UAAI;4CACH,MAAK;4CACL,WAAU;sDAEV,cAAA,8OAAC,iNAAA,CAAA,YAAS;gDAAC,WAAU;;;;;;;;;;;sDAIvB,8OAAC,4JAAA,CAAA,UAAI;4CACH,MAAK;4CACL,WAAU;sDAEV,cAAA,8OAAC,uNAAA,CAAA,eAAY;gDAAC,WAAU;;;;;;;;;;;sDAI1B,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDACC,SAAS,IAAM,cAAc,CAAC;oDAC9B,WAAU;;sEAEV,8OAAC,+MAAA,CAAA,WAAQ;4DAAC,WAAU;;;;;;sEACpB,8OAAC;4DAAK,WAAU;sEACb,QAAQ,IAAI,CAAC,IAAI;;;;;;;;;;;;gDAIrB,4BACC,8OAAC;oDAAI,WAAU;;sEACb,8OAAC,4JAAA,CAAA,UAAI;4DACH,MAAK;4DACL,WAAU;sEACX;;;;;;sEAGD,8OAAC,4JAAA,CAAA,UAAI;4DACH,MAAK;4DACL,WAAU;sEACX;;;;;;sEAGD,8OAAC,4JAAA,CAAA,UAAI;4DACH,MAAK;4DACL,WAAU;sEACX;;;;;;sEAGD,8OAAC;4DAAG,WAAU;;;;;;sEACd,8OAAC;4DACC,SAAS,IAAM,CAAA,GAAA,8IAAA,CAAA,UAAO,AAAD;4DACrB,WAAU;sEACX;;;;;;;;;;;;;;;;;;;iEAQT;;sDACE,8OAAC,4JAAA,CAAA,UAAI;4CACH,MAAK;4CACL,WAAU;sDACX;;;;;;sDAGD,8OAAC,4JAAA,CAAA,UAAI;4CACH,MAAK;4CACL,WAAU;sDACX;;;;;;;;8CAOL,8OAAC;oCACC,SAAS,IAAM,cAAc,CAAC;oCAC9B,WAAU;8CAET,2BACC,8OAAC,iNAAA,CAAA,YAAS;wCAAC,WAAU;;;;;6DAErB,8OAAC,iNAAA,CAAA,YAAS;wCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;gBAO5B,4BACC,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,4JAAA,CAAA,UAAI;gCACH,MAAK;gCACL,WAAU;0CACX;;;;;;0CAGD,8OAAC,4JAAA,CAAA,UAAI;gCACH,MAAK;gCACL,WAAU;0CACX;;;;;;4BAGA,yBACC;;kDACE,8OAAC,4JAAA,CAAA,UAAI;wCACH,MAAK;wCACL,WAAU;kDACX;;;;;;kDAGD,8OAAC,4JAAA,CAAA,UAAI;wCACH,MAAK;wCACL,WAAU;kDACX;;;;;;kDAGD,8OAAC,4JAAA,CAAA,UAAI;wCACH,MAAK;wCACL,WAAU;kDACX;;;;;;kDAGD,8OAAC,4JAAA,CAAA,UAAI;wCACH,MAAK;wCACL,WAAU;kDACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAWnB", "debugId": null}}, {"offset": {"line": 473, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/kaliha/khalihatanbaa/src/components/layout/Footer.tsx"], "sourcesContent": ["import Link from 'next/link'\n\nexport function Footer() {\n  return (\n    <footer className=\"bg-dark-800 text-white\">\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12\">\n        <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8\">\n          {/* معلومات الموقع */}\n          <div className=\"lg:col-span-2\">\n            <div className=\"flex items-center space-x-2 space-x-reverse mb-4\">\n              <div className=\"w-10 h-10 bg-primary-500 rounded-lg flex items-center justify-center\">\n                <span className=\"text-white font-bold text-lg\">خ</span>\n              </div>\n              <div>\n                <h3 className=\"text-xl font-bold\">خَلّيها تنْباع</h3>\n                <p className=\"text-sm text-gray-400\">منصة البيع والشراء</p>\n              </div>\n            </div>\n            <p className=\"text-gray-300 mb-6 max-w-md\">\n              منصة إلكترونية سورية لبيع وشراء المنتجات المستعملة والجديدة. \n              نوفر بيئة آمنة وسهلة للتجارة الإلكترونية في سوريا.\n            </p>\n            <div className=\"flex space-x-4 space-x-reverse\">\n              <a href=\"#\" className=\"text-gray-400 hover:text-white transition-colors\">\n                <span className=\"sr-only\">فيسبوك</span>\n                <svg className=\"h-6 w-6\" fill=\"currentColor\" viewBox=\"0 0 24 24\">\n                  <path d=\"M24 12.073c0-6.627-5.373-12-12-12s-12 5.373-12 12c0 5.99 4.388 10.954 10.125 11.854v-8.385H7.078v-3.47h3.047V9.43c0-3.007 1.792-4.669 4.533-4.669 1.312 0 2.686.235 2.686.235v2.953H15.83c-1.491 0-1.956.925-1.956 1.874v2.25h3.328l-.532 3.47h-2.796v8.385C19.612 23.027 24 18.062 24 12.073z\"/>\n                </svg>\n              </a>\n              <a href=\"#\" className=\"text-gray-400 hover:text-white transition-colors\">\n                <span className=\"sr-only\">تلغرام</span>\n                <svg className=\"h-6 w-6\" fill=\"currentColor\" viewBox=\"0 0 24 24\">\n                  <path d=\"M11.944 0A12 12 0 0 0 0 12a12 12 0 0 0 12 12 12 12 0 0 0 12-12A12 12 0 0 0 12 0a12 12 0 0 0-.056 0zm4.962 7.224c.1-.002.321.023.465.14a.506.506 0 0 1 .171.325c.016.093.036.306.02.472-.18 1.898-.962 6.502-1.36 8.627-.168.9-.499 1.201-.82 1.23-.696.065-1.225-.46-1.9-.902-1.056-.693-1.653-1.124-2.678-1.8-1.185-.78-.417-1.21.258-1.91.177-.184 3.247-2.977 3.307-3.23.007-.032.014-.15-.056-.212s-.174-.041-.249-.024c-.106.024-1.793 1.14-5.061 3.345-.48.33-.913.49-1.302.48-.428-.008-1.252-.241-1.865-.44-.752-.245-1.349-.374-1.297-.789.027-.216.325-.437.893-.663 3.498-1.524 5.83-2.529 6.998-3.014 3.332-1.386 4.025-1.627 4.476-1.635z\"/>\n                </svg>\n              </a>\n              <a href=\"#\" className=\"text-gray-400 hover:text-white transition-colors\">\n                <span className=\"sr-only\">واتساب</span>\n                <svg className=\"h-6 w-6\" fill=\"currentColor\" viewBox=\"0 0 24 24\">\n                  <path d=\"M17.472 14.382c-.297-.149-1.758-.867-2.03-.967-.273-.099-.471-.148-.67.15-.197.297-.767.966-.94 1.164-.173.199-.347.223-.644.075-.297-.15-1.255-.463-2.39-1.475-.883-.788-1.48-1.761-1.653-2.059-.173-.297-.018-.458.13-.606.134-.133.298-.347.446-.52.149-.174.198-.298.298-.497.099-.198.05-.371-.025-.52-.075-.149-.669-1.612-.916-2.207-.242-.579-.487-.5-.669-.51-.173-.008-.371-.01-.57-.01-.198 0-.52.074-.792.372-.272.297-1.04 1.016-1.04 2.479 0 1.462 1.065 2.875 1.213 3.074.149.198 2.096 3.2 5.077 4.487.709.306 1.262.489 1.694.625.712.227 1.36.195 1.871.118.571-.085 1.758-.719 2.006-1.413.248-.694.248-1.289.173-1.413-.074-.124-.272-.198-.57-.347m-5.421 7.403h-.004a9.87 9.87 0 01-5.031-1.378l-.361-.214-3.741.982.998-3.648-.235-.374a9.86 9.86 0 01-1.51-5.26c.001-5.45 4.436-9.884 9.888-9.884 2.64 0 5.122 1.03 6.988 2.898a9.825 9.825 0 012.893 6.994c-.003 5.45-4.437 9.884-9.885 9.884m8.413-18.297A11.815 11.815 0 0012.05 0C5.495 0 .16 5.335.157 11.892c0 2.096.547 4.142 1.588 5.945L.057 24l6.305-1.654a11.882 11.882 0 005.683 1.448h.005c6.554 0 11.89-5.335 11.893-11.893A11.821 11.821 0 0020.885 3.097\"/>\n                </svg>\n              </a>\n            </div>\n          </div>\n\n          {/* روابط سريعة */}\n          <div>\n            <h4 className=\"text-lg font-semibold mb-4\">روابط سريعة</h4>\n            <ul className=\"space-y-2\">\n              <li>\n                <Link href=\"/about\" className=\"text-gray-300 hover:text-white transition-colors\">\n                  من نحن\n                </Link>\n              </li>\n              <li>\n                <Link href=\"/how-it-works\" className=\"text-gray-300 hover:text-white transition-colors\">\n                  كيف يعمل الموقع\n                </Link>\n              </li>\n              <li>\n                <Link href=\"/safety-tips\" className=\"text-gray-300 hover:text-white transition-colors\">\n                  نصائح الأمان\n                </Link>\n              </li>\n              <li>\n                <Link href=\"/pricing\" className=\"text-gray-300 hover:text-white transition-colors\">\n                  باقات الإعلانات\n                </Link>\n              </li>\n              <li>\n                <Link href=\"/contact\" className=\"text-gray-300 hover:text-white transition-colors\">\n                  اتصل بنا\n                </Link>\n              </li>\n            </ul>\n          </div>\n\n          {/* الدعم والمساعدة */}\n          <div>\n            <h4 className=\"text-lg font-semibold mb-4\">الدعم والمساعدة</h4>\n            <ul className=\"space-y-2\">\n              <li>\n                <Link href=\"/help\" className=\"text-gray-300 hover:text-white transition-colors\">\n                  مركز المساعدة\n                </Link>\n              </li>\n              <li>\n                <Link href=\"/terms\" className=\"text-gray-300 hover:text-white transition-colors\">\n                  شروط الاستخدام\n                </Link>\n              </li>\n              <li>\n                <Link href=\"/privacy\" className=\"text-gray-300 hover:text-white transition-colors\">\n                  سياسة الخصوصية\n                </Link>\n              </li>\n              <li>\n                <Link href=\"/report\" className=\"text-gray-300 hover:text-white transition-colors\">\n                  الإبلاغ عن مشكلة\n                </Link>\n              </li>\n              <li>\n                <Link href=\"/faq\" className=\"text-gray-300 hover:text-white transition-colors\">\n                  الأسئلة الشائعة\n                </Link>\n              </li>\n            </ul>\n          </div>\n        </div>\n\n        {/* معلومات الاتصال */}\n        <div className=\"border-t border-gray-700 mt-8 pt-8\">\n          <div className=\"grid grid-cols-1 md:grid-cols-3 gap-6\">\n            <div className=\"text-center md:text-right\">\n              <h5 className=\"font-semibold mb-2\">البريد الإلكتروني</h5>\n              <p className=\"text-gray-300\"><EMAIL></p>\n            </div>\n            <div className=\"text-center\">\n              <h5 className=\"font-semibold mb-2\">رقم الهاتف</h5>\n              <p className=\"text-gray-300\" dir=\"ltr\">+963 11 123 4567</p>\n            </div>\n            <div className=\"text-center md:text-left\">\n              <h5 className=\"font-semibold mb-2\">العنوان</h5>\n              <p className=\"text-gray-300\">دمشق، سوريا</p>\n            </div>\n          </div>\n        </div>\n\n        {/* حقوق النشر */}\n        <div className=\"border-t border-gray-700 mt-8 pt-8 text-center\">\n          <p className=\"text-gray-400\">\n            © 2024 خَلّيها تنْباع. جميع الحقوق محفوظة.\n          </p>\n          <p className=\"text-gray-500 text-sm mt-2\">\n            تم التطوير بـ ❤️ في سوريا\n          </p>\n        </div>\n      </div>\n    </footer>\n  )\n}\n"], "names": [], "mappings": ";;;;AAAA;;;AAEO,SAAS;IACd,qBACE,8OAAC;QAAO,WAAU;kBAChB,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDAAK,WAAU;0DAA+B;;;;;;;;;;;sDAEjD,8OAAC;;8DACC,8OAAC;oDAAG,WAAU;8DAAoB;;;;;;8DAClC,8OAAC;oDAAE,WAAU;8DAAwB;;;;;;;;;;;;;;;;;;8CAGzC,8OAAC;oCAAE,WAAU;8CAA8B;;;;;;8CAI3C,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAE,MAAK;4CAAI,WAAU;;8DACpB,8OAAC;oDAAK,WAAU;8DAAU;;;;;;8DAC1B,8OAAC;oDAAI,WAAU;oDAAU,MAAK;oDAAe,SAAQ;8DACnD,cAAA,8OAAC;wDAAK,GAAE;;;;;;;;;;;;;;;;;sDAGZ,8OAAC;4CAAE,MAAK;4CAAI,WAAU;;8DACpB,8OAAC;oDAAK,WAAU;8DAAU;;;;;;8DAC1B,8OAAC;oDAAI,WAAU;oDAAU,MAAK;oDAAe,SAAQ;8DACnD,cAAA,8OAAC;wDAAK,GAAE;;;;;;;;;;;;;;;;;sDAGZ,8OAAC;4CAAE,MAAK;4CAAI,WAAU;;8DACpB,8OAAC;oDAAK,WAAU;8DAAU;;;;;;8DAC1B,8OAAC;oDAAI,WAAU;oDAAU,MAAK;oDAAe,SAAQ;8DACnD,cAAA,8OAAC;wDAAK,GAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;sCAOhB,8OAAC;;8CACC,8OAAC;oCAAG,WAAU;8CAA6B;;;;;;8CAC3C,8OAAC;oCAAG,WAAU;;sDACZ,8OAAC;sDACC,cAAA,8OAAC,4JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAS,WAAU;0DAAmD;;;;;;;;;;;sDAInF,8OAAC;sDACC,cAAA,8OAAC,4JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAgB,WAAU;0DAAmD;;;;;;;;;;;sDAI1F,8OAAC;sDACC,cAAA,8OAAC,4JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAe,WAAU;0DAAmD;;;;;;;;;;;sDAIzF,8OAAC;sDACC,cAAA,8OAAC,4JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAW,WAAU;0DAAmD;;;;;;;;;;;sDAIrF,8OAAC;sDACC,cAAA,8OAAC,4JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAW,WAAU;0DAAmD;;;;;;;;;;;;;;;;;;;;;;;sCAQzF,8OAAC;;8CACC,8OAAC;oCAAG,WAAU;8CAA6B;;;;;;8CAC3C,8OAAC;oCAAG,WAAU;;sDACZ,8OAAC;sDACC,cAAA,8OAAC,4JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAQ,WAAU;0DAAmD;;;;;;;;;;;sDAIlF,8OAAC;sDACC,cAAA,8OAAC,4JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAS,WAAU;0DAAmD;;;;;;;;;;;sDAInF,8OAAC;sDACC,cAAA,8OAAC,4JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAW,WAAU;0DAAmD;;;;;;;;;;;sDAIrF,8OAAC;sDACC,cAAA,8OAAC,4JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAU,WAAU;0DAAmD;;;;;;;;;;;sDAIpF,8OAAC;sDACC,cAAA,8OAAC,4JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAO,WAAU;0DAAmD;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8BASvF,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAG,WAAU;kDAAqB;;;;;;kDACnC,8OAAC;wCAAE,WAAU;kDAAgB;;;;;;;;;;;;0CAE/B,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAG,WAAU;kDAAqB;;;;;;kDACnC,8OAAC;wCAAE,WAAU;wCAAgB,KAAI;kDAAM;;;;;;;;;;;;0CAEzC,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAG,WAAU;kDAAqB;;;;;;kDACnC,8OAAC;wCAAE,WAAU;kDAAgB;;;;;;;;;;;;;;;;;;;;;;;8BAMnC,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAE,WAAU;sCAAgB;;;;;;sCAG7B,8OAAC;4BAAE,WAAU;sCAA6B;;;;;;;;;;;;;;;;;;;;;;;AAOpD", "debugId": null}}, {"offset": {"line": 1006, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/kaliha/khalihatanbaa/src/components/ClientOnly.tsx"], "sourcesContent": ["'use client'\n\nimport { useEffect, useState } from 'react'\n\ninterface ClientOnlyProps {\n  children: React.ReactNode\n  fallback?: React.ReactNode\n}\n\nexport function ClientOnly({ children, fallback = null }: ClientOnlyProps) {\n  const [hasMounted, setHasMounted] = useState(false)\n\n  useEffect(() => {\n    setHasMounted(true)\n  }, [])\n\n  if (!hasMounted) {\n    return <>{fallback}</>\n  }\n\n  return <>{children}</>\n}\n"], "names": [], "mappings": ";;;;AAEA;AAFA;;;AASO,SAAS,WAAW,EAAE,QAAQ,EAAE,WAAW,IAAI,EAAmB;IACvE,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE7C,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,cAAc;IAChB,GAAG,EAAE;IAEL,IAAI,CAAC,YAAY;QACf,qBAAO;sBAAG;;IACZ;IAEA,qBAAO;kBAAG;;AACZ", "debugId": null}}, {"offset": {"line": 1034, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/kaliha/khalihatanbaa/src/hooks/useMessages.ts"], "sourcesContent": ["import { useState, useEffect, useCallback, useRef } from \"react\";\nimport { useSession } from \"next-auth/react\";\n\ninterface Message {\n  id: string;\n  content: string;\n  isRead: boolean;\n  createdAt: string;\n  fromId: string;\n  toId: string;\n  adId?: string;\n  from: {\n    id: string;\n    name: string;\n    avatar?: string;\n  };\n  to: {\n    id: string;\n    name: string;\n    avatar?: string;\n  };\n  ad?: {\n    id: string;\n    title: string;\n    price: number;\n    imageUrls: string[];\n  };\n}\n\ninterface Conversation {\n  id: string;\n  content: string;\n  createdAt: string;\n  adId?: string;\n  otherUser: {\n    id: string;\n    name: string;\n    avatar?: string;\n  };\n  ad?: {\n    id: string;\n    title: string;\n    price: number;\n    imageUrls: string[];\n  };\n  unreadCount: number;\n  isOnline?: boolean;\n}\n\ninterface UseMessagesOptions {\n  conversationWith?: string;\n  adId?: string;\n  enableRealTime?: boolean;\n  pollingInterval?: number;\n}\n\nexport function useMessages(options: UseMessagesOptions = {}) {\n  const { data: session } = useSession();\n  const {\n    conversationWith,\n    adId,\n    enableRealTime = true,\n    pollingInterval = 30000, // 30 seconds\n  } = options;\n\n  const [conversations, setConversations] = useState<Conversation[]>([]);\n  const [messages, setMessages] = useState<Message[]>([]);\n  const [loading, setLoading] = useState(true);\n  const [sending, setSending] = useState(false);\n  const [error, setError] = useState<string | null>(null);\n  const [hasMore, setHasMore] = useState(false);\n  const [lastMessageId, setLastMessageId] = useState<string | null>(null);\n\n  const eventSourceRef = useRef<EventSource | null>(null);\n  const pollingIntervalRef = useRef<NodeJS.Timeout | null>(null);\n  const isConnectedRef = useRef(false);\n\n  // جلب المحادثات\n  const fetchConversations = useCallback(async () => {\n    if (!session?.user?.id) return;\n\n    try {\n      const response = await fetch(\"/api/messages\");\n      const data = await response.json();\n\n      if (data.success) {\n        setConversations(data.data);\n      } else {\n        setError(data.error || \"حدث خطأ في جلب المحادثات\");\n      }\n    } catch (error) {\n      console.error(\"Error fetching conversations:\", error);\n      setError(\"حدث خطأ في جلب المحادثات\");\n    }\n  }, [session?.user?.id]);\n\n  // جلب الرسائل لمحادثة محددة\n  const fetchMessages = useCallback(\n    async (userId: string, adId?: string, lastMsgId?: string) => {\n      if (!session?.user?.id) return;\n\n      try {\n        const url = new URL(\"/api/messages\", window.location.origin);\n        url.searchParams.set(\"with\", userId);\n        if (adId) url.searchParams.set(\"adId\", adId);\n        if (lastMsgId) url.searchParams.set(\"lastMessageId\", lastMsgId);\n\n        const response = await fetch(url.toString());\n        const data = await response.json();\n\n        if (data.success) {\n          if (lastMsgId) {\n            // إضافة الرسائل الأقدم في بداية المصفوفة\n            setMessages((prev) => [...data.data, ...prev]);\n          } else {\n            // استبدال الرسائل (التحميل الأول)\n            setMessages(data.data);\n          }\n          setHasMore(data.hasMore || false);\n          setLastMessageId(data.lastMessageId);\n        } else {\n          setError(data.error || \"حدث خطأ في جلب الرسائل\");\n        }\n      } catch (error) {\n        console.error(\"Error fetching messages:\", error);\n        setError(\"حدث خطأ في جلب الرسائل\");\n      }\n    },\n    [session?.user?.id]\n  );\n\n  // إرسال رسالة\n  const sendMessage = useCallback(\n    async (content: string, toId: string, adId?: string) => {\n      if (!content.trim() || sending || !session?.user?.id) return false;\n\n      // التحقق من وجود adId\n      if (!adId) {\n        setError(\"معرف الإعلان مطلوب لإرسال الرسالة\");\n        return false;\n      }\n\n      setSending(true);\n      setError(null);\n\n      // Optimistic update\n      const tempId = `temp-${Date.now()}`;\n      const optimisticMessage: Message = {\n        id: tempId,\n        content: content.trim(),\n        isRead: false,\n        createdAt: new Date().toISOString(),\n        fromId: session.user.id,\n        toId,\n        adId,\n        from: {\n          id: session.user.id,\n          name: session.user.name || \"\",\n          avatar: session.user.avatar,\n        },\n        to: {\n          id: toId,\n          name: \"مستخدم غير معروف\",\n          avatar: undefined,\n        },\n      };\n\n      setMessages((prev) => [...prev, optimisticMessage]);\n\n      try {\n        const response = await fetch(\"/api/messages\", {\n          method: \"POST\",\n          headers: {\n            \"Content-Type\": \"application/json\",\n          },\n          body: JSON.stringify({\n            toId,\n            content: content.trim(),\n            ...(adId && { adId }),\n          }),\n        });\n\n        const data = await response.json();\n\n        if (data.success) {\n          // استبدال الرسالة المؤقتة بالرسالة الحقيقية\n          setMessages((prev) =>\n            prev.map((msg) => (msg.id === tempId ? data.data : msg))\n          );\n\n          // تحديث قائمة المحادثات\n          fetchConversations();\n\n          return true;\n        } else {\n          // إزالة الرسالة المؤقتة\n          setMessages((prev) => prev.filter((msg) => msg.id !== tempId));\n          setError(data.error || \"حدث خطأ في إرسال الرسالة\");\n          return false;\n        }\n      } catch (error) {\n        console.error(\"Error sending message:\", error);\n        setMessages((prev) => prev.filter((msg) => msg.id !== tempId));\n        setError(\"حدث خطأ في إرسال الرسالة\");\n        return false;\n      } finally {\n        setSending(false);\n      }\n    },\n    [sending, session?.user?.id, fetchConversations]\n  );\n\n  // تحميل المزيد من الرسائل (الرسائل الأقدم)\n  const loadMoreMessages = useCallback(() => {\n    if (conversationWith && hasMore && !loading) {\n      // استخدام أقدم رسالة كنقطة مرجعية للتحميل\n      const oldestMessage = messages.length > 0 ? messages[0] : null;\n      const oldestMessageId = oldestMessage ? oldestMessage.id : null;\n\n      fetchMessages(conversationWith, adId, oldestMessageId);\n    }\n  }, [conversationWith, adId, hasMore, loading, messages, fetchMessages]);\n\n  // إعداد Server-Sent Events\n  const setupSSE = useCallback(() => {\n    if (!session?.user?.id || !enableRealTime || isConnectedRef.current) return;\n\n    try {\n      const eventSource = new EventSource(\"/api/messages/sse\");\n      eventSourceRef.current = eventSource;\n      isConnectedRef.current = true;\n\n      eventSource.onmessage = (event) => {\n        try {\n          const data = JSON.parse(event.data);\n\n          switch (data.type) {\n            case \"connected\":\n              console.log(\"Connected to real-time messaging\");\n              break;\n\n            case \"new_message\":\n              const newMessage = data.data;\n\n              // إضافة الرسالة إذا كانت للمحادثة الحالية\n              if (\n                conversationWith &&\n                (newMessage.fromId === conversationWith ||\n                  newMessage.toId === conversationWith)\n              ) {\n                setMessages((prev) => {\n                  // تجنب الرسائل المكررة\n                  if (prev.some((msg) => msg.id === newMessage.id)) {\n                    return prev;\n                  }\n                  return [...prev, newMessage];\n                });\n              }\n\n              // تحديث قائمة المحادثات\n              fetchConversations();\n              break;\n\n            case \"ping\":\n              // مجرد ping للحفاظ على الاتصال\n              break;\n\n            default:\n              console.log(\"Unknown SSE message type:\", data.type);\n          }\n        } catch (error) {\n          console.error(\"Error parsing SSE message:\", error);\n        }\n      };\n\n      eventSource.onerror = (error) => {\n        console.error(\"SSE connection error:\", error);\n        isConnectedRef.current = false;\n\n        // إعادة المحاولة بعد 5 ثوان\n        setTimeout(() => {\n          if (session?.user?.id && enableRealTime) {\n            setupSSE();\n          }\n        }, 5000);\n      };\n    } catch (error) {\n      console.error(\"Error setting up SSE:\", error);\n      isConnectedRef.current = false;\n    }\n  }, [session?.user?.id, enableRealTime, conversationWith, fetchConversations]);\n\n  // إعداد Polling كبديل احتياطي\n  const setupPolling = useCallback(() => {\n    if (!session?.user?.id || enableRealTime || pollingIntervalRef.current)\n      return;\n\n    pollingIntervalRef.current = setInterval(() => {\n      if (conversationWith) {\n        fetchMessages(conversationWith, adId);\n      } else {\n        fetchConversations();\n      }\n    }, pollingInterval);\n  }, [\n    session?.user?.id,\n    enableRealTime,\n    conversationWith,\n    adId,\n    pollingInterval,\n    fetchMessages,\n    fetchConversations,\n  ]);\n\n  // تنظيف الاتصالات\n  const cleanup = useCallback(() => {\n    if (eventSourceRef.current) {\n      eventSourceRef.current.close();\n      eventSourceRef.current = null;\n      isConnectedRef.current = false;\n    }\n\n    if (pollingIntervalRef.current) {\n      clearInterval(pollingIntervalRef.current);\n      pollingIntervalRef.current = null;\n    }\n  }, []);\n\n  // Effects\n  useEffect(() => {\n    if (session?.user?.id) {\n      setLoading(true);\n\n      if (conversationWith) {\n        fetchMessages(conversationWith, adId).finally(() => setLoading(false));\n      } else {\n        fetchConversations().finally(() => setLoading(false));\n      }\n    }\n  }, [\n    session?.user?.id,\n    conversationWith,\n    adId,\n    fetchMessages,\n    fetchConversations,\n  ]);\n\n  useEffect(() => {\n    if (session?.user?.id) {\n      if (enableRealTime) {\n        setupSSE();\n      } else {\n        setupPolling();\n      }\n    }\n\n    return cleanup;\n  }, [session?.user?.id, enableRealTime, setupSSE, setupPolling, cleanup]);\n\n  return {\n    conversations,\n    messages,\n    loading,\n    sending,\n    error,\n    hasMore,\n    sendMessage,\n    loadMoreMessages,\n    fetchConversations,\n    fetchMessages,\n    setError,\n  };\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAuDO,SAAS,YAAY,UAA8B,CAAC,CAAC;IAC1D,MAAM,EAAE,MAAM,OAAO,EAAE,GAAG,CAAA,GAAA,8IAAA,CAAA,aAAU,AAAD;IACnC,MAAM,EACJ,gBAAgB,EAChB,IAAI,EACJ,iBAAiB,IAAI,EACrB,kBAAkB,KAAK,EACxB,GAAG;IAEJ,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAkB,EAAE;IACrE,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAa,EAAE;IACtD,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IAClD,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IAElE,MAAM,iBAAiB,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAsB;IAClD,MAAM,qBAAqB,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAyB;IACzD,MAAM,iBAAiB,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAE;IAE9B,gBAAgB;IAChB,MAAM,qBAAqB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;QACrC,IAAI,CAAC,SAAS,MAAM,IAAI;QAExB,IAAI;YACF,MAAM,WAAW,MAAM,MAAM;YAC7B,MAAM,OAAO,MAAM,SAAS,IAAI;YAEhC,IAAI,KAAK,OAAO,EAAE;gBAChB,iBAAiB,KAAK,IAAI;YAC5B,OAAO;gBACL,SAAS,KAAK,KAAK,IAAI;YACzB;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,iCAAiC;YAC/C,SAAS;QACX;IACF,GAAG;QAAC,SAAS,MAAM;KAAG;IAEtB,4BAA4B;IAC5B,MAAM,gBAAgB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAC9B,OAAO,QAAgB,MAAe;QACpC,IAAI,CAAC,SAAS,MAAM,IAAI;QAExB,IAAI;YACF,MAAM,MAAM,IAAI,IAAI,iBAAiB,OAAO,QAAQ,CAAC,MAAM;YAC3D,IAAI,YAAY,CAAC,GAAG,CAAC,QAAQ;YAC7B,IAAI,MAAM,IAAI,YAAY,CAAC,GAAG,CAAC,QAAQ;YACvC,IAAI,WAAW,IAAI,YAAY,CAAC,GAAG,CAAC,iBAAiB;YAErD,MAAM,WAAW,MAAM,MAAM,IAAI,QAAQ;YACzC,MAAM,OAAO,MAAM,SAAS,IAAI;YAEhC,IAAI,KAAK,OAAO,EAAE;gBAChB,IAAI,WAAW;oBACb,yCAAyC;oBACzC,YAAY,CAAC,OAAS;+BAAI,KAAK,IAAI;+BAAK;yBAAK;gBAC/C,OAAO;oBACL,kCAAkC;oBAClC,YAAY,KAAK,IAAI;gBACvB;gBACA,WAAW,KAAK,OAAO,IAAI;gBAC3B,iBAAiB,KAAK,aAAa;YACrC,OAAO;gBACL,SAAS,KAAK,KAAK,IAAI;YACzB;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,4BAA4B;YAC1C,SAAS;QACX;IACF,GACA;QAAC,SAAS,MAAM;KAAG;IAGrB,cAAc;IACd,MAAM,cAAc,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAC5B,OAAO,SAAiB,MAAc;QACpC,IAAI,CAAC,QAAQ,IAAI,MAAM,WAAW,CAAC,SAAS,MAAM,IAAI,OAAO;QAE7D,sBAAsB;QACtB,IAAI,CAAC,MAAM;YACT,SAAS;YACT,OAAO;QACT;QAEA,WAAW;QACX,SAAS;QAET,oBAAoB;QACpB,MAAM,SAAS,CAAC,KAAK,EAAE,KAAK,GAAG,IAAI;QACnC,MAAM,oBAA6B;YACjC,IAAI;YACJ,SAAS,QAAQ,IAAI;YACrB,QAAQ;YACR,WAAW,IAAI,OAAO,WAAW;YACjC,QAAQ,QAAQ,IAAI,CAAC,EAAE;YACvB;YACA;YACA,MAAM;gBACJ,IAAI,QAAQ,IAAI,CAAC,EAAE;gBACnB,MAAM,QAAQ,IAAI,CAAC,IAAI,IAAI;gBAC3B,QAAQ,QAAQ,IAAI,CAAC,MAAM;YAC7B;YACA,IAAI;gBACF,IAAI;gBACJ,MAAM;gBACN,QAAQ;YACV;QACF;QAEA,YAAY,CAAC,OAAS;mBAAI;gBAAM;aAAkB;QAElD,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,iBAAiB;gBAC5C,QAAQ;gBACR,SAAS;oBACP,gBAAgB;gBAClB;gBACA,MAAM,KAAK,SAAS,CAAC;oBACnB;oBACA,SAAS,QAAQ,IAAI;oBACrB,GAAI,QAAQ;wBAAE;oBAAK,CAAC;gBACtB;YACF;YAEA,MAAM,OAAO,MAAM,SAAS,IAAI;YAEhC,IAAI,KAAK,OAAO,EAAE;gBAChB,4CAA4C;gBAC5C,YAAY,CAAC,OACX,KAAK,GAAG,CAAC,CAAC,MAAS,IAAI,EAAE,KAAK,SAAS,KAAK,IAAI,GAAG;gBAGrD,wBAAwB;gBACxB;gBAEA,OAAO;YACT,OAAO;gBACL,wBAAwB;gBACxB,YAAY,CAAC,OAAS,KAAK,MAAM,CAAC,CAAC,MAAQ,IAAI,EAAE,KAAK;gBACtD,SAAS,KAAK,KAAK,IAAI;gBACvB,OAAO;YACT;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,0BAA0B;YACxC,YAAY,CAAC,OAAS,KAAK,MAAM,CAAC,CAAC,MAAQ,IAAI,EAAE,KAAK;YACtD,SAAS;YACT,OAAO;QACT,SAAU;YACR,WAAW;QACb;IACF,GACA;QAAC;QAAS,SAAS,MAAM;QAAI;KAAmB;IAGlD,2CAA2C;IAC3C,MAAM,mBAAmB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;QACnC,IAAI,oBAAoB,WAAW,CAAC,SAAS;YAC3C,0CAA0C;YAC1C,MAAM,gBAAgB,SAAS,MAAM,GAAG,IAAI,QAAQ,CAAC,EAAE,GAAG;YAC1D,MAAM,kBAAkB,gBAAgB,cAAc,EAAE,GAAG;YAE3D,cAAc,kBAAkB,MAAM;QACxC;IACF,GAAG;QAAC;QAAkB;QAAM;QAAS;QAAS;QAAU;KAAc;IAEtE,2BAA2B;IAC3B,MAAM,WAAW,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;QAC3B,IAAI,CAAC,SAAS,MAAM,MAAM,CAAC,kBAAkB,eAAe,OAAO,EAAE;QAErE,IAAI;YACF,MAAM,cAAc,IAAI,YAAY;YACpC,eAAe,OAAO,GAAG;YACzB,eAAe,OAAO,GAAG;YAEzB,YAAY,SAAS,GAAG,CAAC;gBACvB,IAAI;oBACF,MAAM,OAAO,KAAK,KAAK,CAAC,MAAM,IAAI;oBAElC,OAAQ,KAAK,IAAI;wBACf,KAAK;4BACH,QAAQ,GAAG,CAAC;4BACZ;wBAEF,KAAK;4BACH,MAAM,aAAa,KAAK,IAAI;4BAE5B,0CAA0C;4BAC1C,IACE,oBACA,CAAC,WAAW,MAAM,KAAK,oBACrB,WAAW,IAAI,KAAK,gBAAgB,GACtC;gCACA,YAAY,CAAC;oCACX,uBAAuB;oCACvB,IAAI,KAAK,IAAI,CAAC,CAAC,MAAQ,IAAI,EAAE,KAAK,WAAW,EAAE,GAAG;wCAChD,OAAO;oCACT;oCACA,OAAO;2CAAI;wCAAM;qCAAW;gCAC9B;4BACF;4BAEA,wBAAwB;4BACxB;4BACA;wBAEF,KAAK;4BAEH;wBAEF;4BACE,QAAQ,GAAG,CAAC,6BAA6B,KAAK,IAAI;oBACtD;gBACF,EAAE,OAAO,OAAO;oBACd,QAAQ,KAAK,CAAC,8BAA8B;gBAC9C;YACF;YAEA,YAAY,OAAO,GAAG,CAAC;gBACrB,QAAQ,KAAK,CAAC,yBAAyB;gBACvC,eAAe,OAAO,GAAG;gBAEzB,4BAA4B;gBAC5B,WAAW;oBACT,IAAI,SAAS,MAAM,MAAM,gBAAgB;wBACvC;oBACF;gBACF,GAAG;YACL;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,yBAAyB;YACvC,eAAe,OAAO,GAAG;QAC3B;IACF,GAAG;QAAC,SAAS,MAAM;QAAI;QAAgB;QAAkB;KAAmB;IAE5E,8BAA8B;IAC9B,MAAM,eAAe,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;QAC/B,IAAI,CAAC,SAAS,MAAM,MAAM,kBAAkB,mBAAmB,OAAO,EACpE;QAEF,mBAAmB,OAAO,GAAG,YAAY;YACvC,IAAI,kBAAkB;gBACpB,cAAc,kBAAkB;YAClC,OAAO;gBACL;YACF;QACF,GAAG;IACL,GAAG;QACD,SAAS,MAAM;QACf;QACA;QACA;QACA;QACA;QACA;KACD;IAED,kBAAkB;IAClB,MAAM,UAAU,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;QAC1B,IAAI,eAAe,OAAO,EAAE;YAC1B,eAAe,OAAO,CAAC,KAAK;YAC5B,eAAe,OAAO,GAAG;YACzB,eAAe,OAAO,GAAG;QAC3B;QAEA,IAAI,mBAAmB,OAAO,EAAE;YAC9B,cAAc,mBAAmB,OAAO;YACxC,mBAAmB,OAAO,GAAG;QAC/B;IACF,GAAG,EAAE;IAEL,UAAU;IACV,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,SAAS,MAAM,IAAI;YACrB,WAAW;YAEX,IAAI,kBAAkB;gBACpB,cAAc,kBAAkB,MAAM,OAAO,CAAC,IAAM,WAAW;YACjE,OAAO;gBACL,qBAAqB,OAAO,CAAC,IAAM,WAAW;YAChD;QACF;IACF,GAAG;QACD,SAAS,MAAM;QACf;QACA;QACA;QACA;KACD;IAED,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,SAAS,MAAM,IAAI;YACrB,IAAI,gBAAgB;gBAClB;YACF,OAAO;gBACL;YACF;QACF;QAEA,OAAO;IACT,GAAG;QAAC,SAAS,MAAM;QAAI;QAAgB;QAAU;QAAc;KAAQ;IAEvE,OAAO;QACL;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;IACF;AACF", "debugId": null}}, {"offset": {"line": 1341, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/kaliha/khalihatan<PERSON><PERSON>/src/lib/messaging.ts"], "sourcesContent": ["// Utilities للرسائل والمحادثات\n\nexport interface Message {\n  id: string;\n  content: string;\n  isRead: boolean;\n  createdAt: string;\n  fromId: string;\n  toId: string;\n  adId?: string;\n  from: {\n    id: string;\n    name: string;\n    avatar?: string;\n  };\n  to: {\n    id: string;\n    name: string;\n    avatar?: string;\n  };\n  ad?: {\n    id: string;\n    title: string;\n    price: number;\n    imageUrls: string[];\n  };\n}\n\nexport interface Conversation {\n  id: string;\n  content: string;\n  createdAt: string;\n  adId?: string;\n  otherUser: {\n    id: string;\n    name: string;\n    avatar?: string;\n  };\n  ad?: {\n    id: string;\n    title: string;\n    price: number;\n    imageUrls: string[];\n  };\n  unreadCount: number;\n  isOnline?: boolean;\n}\n\n// تنسيق الوقت للرسائل\nexport function formatMessageTime(dateString: string): string {\n  const date = new Date(dateString);\n  const now = new Date();\n  const diffInHours = (now.getTime() - date.getTime()) / (1000 * 60 * 60);\n\n  if (diffInHours < 24) {\n    // إذا كان اليوم نفسه، اعرض الوقت فقط\n    return date.toLocaleTimeString(\"ar-SY\", {\n      hour: \"2-digit\",\n      minute: \"2-digit\",\n      hour12: false,\n    });\n  } else if (diffInHours < 48) {\n    // إذا كان أمس\n    return \"أمس \" + date.toLocaleTimeString(\"ar-SY\", {\n      hour: \"2-digit\",\n      minute: \"2-digit\",\n      hour12: false,\n    });\n  } else if (diffInHours < 168) {\n    // إذا كان خلال الأسبوع الماضي\n    return date.toLocaleDateString(\"ar-SY\", { weekday: \"long\" });\n  } else {\n    // إذا كان أقدم من أسبوع\n    return date.toLocaleDateString(\"ar-SY\", {\n      day: \"numeric\",\n      month: \"short\",\n    });\n  }\n}\n\n// تنسيق السعر\nexport function formatPrice(price: number): string {\n  return new Intl.NumberFormat(\"ar-SY\").format(price);\n}\n\n// التحقق من صحة الرسالة\nexport function validateMessage(content: string): { isValid: boolean; error?: string } {\n  if (!content || !content.trim()) {\n    return { isValid: false, error: \"محتوى الرسالة مطلوب\" };\n  }\n\n  if (content.trim().length > 1000) {\n    return { isValid: false, error: \"الرسالة طويلة جداً (الحد الأقصى 1000 حرف)\" };\n  }\n\n  if (content.trim().length < 1) {\n    return { isValid: false, error: \"الرسالة قصيرة جداً\" };\n  }\n\n  return { isValid: true };\n}\n\n// تنظيف محتوى الرسالة\nexport function sanitizeMessage(content: string): string {\n  return content.trim().replace(/\\s+/g, \" \");\n}\n\n// تجميع الرسائل حسب التاريخ\nexport function groupMessagesByDate(messages: Message[]): { [date: string]: Message[] } {\n  const grouped: { [date: string]: Message[] } = {};\n\n  messages.forEach((message) => {\n    const date = new Date(message.createdAt);\n    const dateKey = date.toLocaleDateString(\"ar-SY\", {\n      year: \"numeric\",\n      month: \"long\",\n      day: \"numeric\",\n    });\n\n    if (!grouped[dateKey]) {\n      grouped[dateKey] = [];\n    }\n    grouped[dateKey].push(message);\n  });\n\n  return grouped;\n}\n\n// البحث في المحادثات\nexport function searchConversations(\n  conversations: Conversation[],\n  query: string\n): Conversation[] {\n  if (!query.trim()) return conversations;\n\n  const searchTerm = query.toLowerCase().trim();\n\n  return conversations.filter(\n    (conversation) =>\n      conversation.otherUser.name.toLowerCase().includes(searchTerm) ||\n      conversation.content.toLowerCase().includes(searchTerm) ||\n      (conversation.ad?.title && conversation.ad.title.toLowerCase().includes(searchTerm))\n  );\n}\n\n// البحث في الرسائل\nexport function searchMessages(messages: Message[], query: string): Message[] {\n  if (!query.trim()) return messages;\n\n  const searchTerm = query.toLowerCase().trim();\n\n  return messages.filter((message) =>\n    message.content.toLowerCase().includes(searchTerm)\n  );\n}\n\n// ترتيب المحادثات (غير المقروءة أولاً، ثم حسب آخر رسالة)\nexport function sortConversations(conversations: Conversation[]): Conversation[] {\n  return [...conversations].sort((a, b) => {\n    // المحادثات غير المقروءة أولاً\n    if (a.unreadCount > 0 && b.unreadCount === 0) return -1;\n    if (b.unreadCount > 0 && a.unreadCount === 0) return 1;\n\n    // ثم حسب آخر رسالة\n    return new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime();\n  });\n}\n\n// إنشاء معرف فريد للرسالة المؤقتة\nexport function generateTempMessageId(): string {\n  return `temp-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;\n}\n\n// التحقق من كون الرسالة مؤقتة\nexport function isTempMessage(messageId: string): boolean {\n  return messageId.startsWith(\"temp-\");\n}\n\n// حساب عدد الرسائل غير المقروءة الإجمالي\nexport function getTotalUnreadCount(conversations: Conversation[]): number {\n  return conversations.reduce((total, conv) => total + conv.unreadCount, 0);\n}\n\n// تحديث حالة قراءة الرسائل\nexport function markMessagesAsRead(\n  messages: Message[],\n  currentUserId: string,\n  otherUserId: string\n): Message[] {\n  return messages.map((message) => {\n    if (message.fromId === otherUserId && message.toId === currentUserId && !message.isRead) {\n      return { ...message, isRead: true };\n    }\n    return message;\n  });\n}\n\n// دمج الرسائل الجديدة مع الموجودة (تجنب التكرار)\nexport function mergeMessages(existingMessages: Message[], newMessages: Message[]): Message[] {\n  const existingIds = new Set(existingMessages.map(msg => msg.id));\n  const uniqueNewMessages = newMessages.filter(msg => !existingIds.has(msg.id));\n  \n  return [...existingMessages, ...uniqueNewMessages].sort(\n    (a, b) => new Date(a.createdAt).getTime() - new Date(b.createdAt).getTime()\n  );\n}\n\n// تحديث محادثة في القائمة\nexport function updateConversationInList(\n  conversations: Conversation[],\n  updatedConversation: Conversation\n): Conversation[] {\n  const index = conversations.findIndex(\n    conv => conv.otherUser.id === updatedConversation.otherUser.id\n  );\n\n  if (index >= 0) {\n    const updated = [...conversations];\n    updated[index] = updatedConversation;\n    return sortConversations(updated);\n  } else {\n    return sortConversations([updatedConversation, ...conversations]);\n  }\n}\n\n// إنشاء محادثة من رسالة\nexport function createConversationFromMessage(\n  message: Message,\n  currentUserId: string\n): Conversation {\n  const isFromCurrentUser = message.fromId === currentUserId;\n  const otherUser = isFromCurrentUser ? message.to : message.from;\n\n  return {\n    id: message.id,\n    content: message.content,\n    createdAt: message.createdAt,\n    adId: message.adId,\n    otherUser,\n    ad: message.ad,\n    unreadCount: isFromCurrentUser ? 0 : (message.isRead ? 0 : 1),\n    isOnline: false, // سيتم تحديثها من الخادم\n  };\n}\n\n// تحديث حالة الاتصال للمستخدمين\nexport function updateOnlineStatus(\n  conversations: Conversation[],\n  onlineUsers: string[]\n): Conversation[] {\n  return conversations.map(conv => ({\n    ...conv,\n    isOnline: onlineUsers.includes(conv.otherUser.id),\n  }));\n}\n"], "names": [], "mappings": "AAAA,+BAA+B;;;;;;;;;;;;;;;;;;;AAiDxB,SAAS,kBAAkB,UAAkB;IAClD,MAAM,OAAO,IAAI,KAAK;IACtB,MAAM,MAAM,IAAI;IAChB,MAAM,cAAc,CAAC,IAAI,OAAO,KAAK,KAAK,OAAO,EAAE,IAAI,CAAC,OAAO,KAAK,EAAE;IAEtE,IAAI,cAAc,IAAI;QACpB,qCAAqC;QACrC,OAAO,KAAK,kBAAkB,CAAC,SAAS;YACtC,MAAM;YACN,QAAQ;YACR,QAAQ;QACV;IACF,OAAO,IAAI,cAAc,IAAI;QAC3B,cAAc;QACd,OAAO,SAAS,KAAK,kBAAkB,CAAC,SAAS;YAC/C,MAAM;YACN,QAAQ;YACR,QAAQ;QACV;IACF,OAAO,IAAI,cAAc,KAAK;QAC5B,8BAA8B;QAC9B,OAAO,KAAK,kBAAkB,CAAC,SAAS;YAAE,SAAS;QAAO;IAC5D,OAAO;QACL,wBAAwB;QACxB,OAAO,KAAK,kBAAkB,CAAC,SAAS;YACtC,KAAK;YACL,OAAO;QACT;IACF;AACF;AAGO,SAAS,YAAY,KAAa;IACvC,OAAO,IAAI,KAAK,YAAY,CAAC,SAAS,MAAM,CAAC;AAC/C;AAGO,SAAS,gBAAgB,OAAe;IAC7C,IAAI,CAAC,WAAW,CAAC,QAAQ,IAAI,IAAI;QAC/B,OAAO;YAAE,SAAS;YAAO,OAAO;QAAsB;IACxD;IAEA,IAAI,QAAQ,IAAI,GAAG,MAAM,GAAG,MAAM;QAChC,OAAO;YAAE,SAAS;YAAO,OAAO;QAA4C;IAC9E;IAEA,IAAI,QAAQ,IAAI,GAAG,MAAM,GAAG,GAAG;QAC7B,OAAO;YAAE,SAAS;YAAO,OAAO;QAAqB;IACvD;IAEA,OAAO;QAAE,SAAS;IAAK;AACzB;AAGO,SAAS,gBAAgB,OAAe;IAC7C,OAAO,QAAQ,IAAI,GAAG,OAAO,CAAC,QAAQ;AACxC;AAGO,SAAS,oBAAoB,QAAmB;IACrD,MAAM,UAAyC,CAAC;IAEhD,SAAS,OAAO,CAAC,CAAC;QAChB,MAAM,OAAO,IAAI,KAAK,QAAQ,SAAS;QACvC,MAAM,UAAU,KAAK,kBAAkB,CAAC,SAAS;YAC/C,MAAM;YACN,OAAO;YACP,KAAK;QACP;QAEA,IAAI,CAAC,OAAO,CAAC,QAAQ,EAAE;YACrB,OAAO,CAAC,QAAQ,GAAG,EAAE;QACvB;QACA,OAAO,CAAC,QAAQ,CAAC,IAAI,CAAC;IACxB;IAEA,OAAO;AACT;AAGO,SAAS,oBACd,aAA6B,EAC7B,KAAa;IAEb,IAAI,CAAC,MAAM,IAAI,IAAI,OAAO;IAE1B,MAAM,aAAa,MAAM,WAAW,GAAG,IAAI;IAE3C,OAAO,cAAc,MAAM,CACzB,CAAC,eACC,aAAa,SAAS,CAAC,IAAI,CAAC,WAAW,GAAG,QAAQ,CAAC,eACnD,aAAa,OAAO,CAAC,WAAW,GAAG,QAAQ,CAAC,eAC3C,aAAa,EAAE,EAAE,SAAS,aAAa,EAAE,CAAC,KAAK,CAAC,WAAW,GAAG,QAAQ,CAAC;AAE9E;AAGO,SAAS,eAAe,QAAmB,EAAE,KAAa;IAC/D,IAAI,CAAC,MAAM,IAAI,IAAI,OAAO;IAE1B,MAAM,aAAa,MAAM,WAAW,GAAG,IAAI;IAE3C,OAAO,SAAS,MAAM,CAAC,CAAC,UACtB,QAAQ,OAAO,CAAC,WAAW,GAAG,QAAQ,CAAC;AAE3C;AAGO,SAAS,kBAAkB,aAA6B;IAC7D,OAAO;WAAI;KAAc,CAAC,IAAI,CAAC,CAAC,GAAG;QACjC,+BAA+B;QAC/B,IAAI,EAAE,WAAW,GAAG,KAAK,EAAE,WAAW,KAAK,GAAG,OAAO,CAAC;QACtD,IAAI,EAAE,WAAW,GAAG,KAAK,EAAE,WAAW,KAAK,GAAG,OAAO;QAErD,mBAAmB;QACnB,OAAO,IAAI,KAAK,EAAE,SAAS,EAAE,OAAO,KAAK,IAAI,KAAK,EAAE,SAAS,EAAE,OAAO;IACxE;AACF;AAGO,SAAS;IACd,OAAO,CAAC,KAAK,EAAE,KAAK,GAAG,GAAG,CAAC,EAAE,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,MAAM,CAAC,GAAG,IAAI;AACxE;AAGO,SAAS,cAAc,SAAiB;IAC7C,OAAO,UAAU,UAAU,CAAC;AAC9B;AAGO,SAAS,oBAAoB,aAA6B;IAC/D,OAAO,cAAc,MAAM,CAAC,CAAC,OAAO,OAAS,QAAQ,KAAK,WAAW,EAAE;AACzE;AAGO,SAAS,mBACd,QAAmB,EACnB,aAAqB,EACrB,WAAmB;IAEnB,OAAO,SAAS,GAAG,CAAC,CAAC;QACnB,IAAI,QAAQ,MAAM,KAAK,eAAe,QAAQ,IAAI,KAAK,iBAAiB,CAAC,QAAQ,MAAM,EAAE;YACvF,OAAO;gBAAE,GAAG,OAAO;gBAAE,QAAQ;YAAK;QACpC;QACA,OAAO;IACT;AACF;AAGO,SAAS,cAAc,gBAA2B,EAAE,WAAsB;IAC/E,MAAM,cAAc,IAAI,IAAI,iBAAiB,GAAG,CAAC,CAAA,MAAO,IAAI,EAAE;IAC9D,MAAM,oBAAoB,YAAY,MAAM,CAAC,CAAA,MAAO,CAAC,YAAY,GAAG,CAAC,IAAI,EAAE;IAE3E,OAAO;WAAI;WAAqB;KAAkB,CAAC,IAAI,CACrD,CAAC,GAAG,IAAM,IAAI,KAAK,EAAE,SAAS,EAAE,OAAO,KAAK,IAAI,KAAK,EAAE,SAAS,EAAE,OAAO;AAE7E;AAGO,SAAS,yBACd,aAA6B,EAC7B,mBAAiC;IAEjC,MAAM,QAAQ,cAAc,SAAS,CACnC,CAAA,OAAQ,KAAK,SAAS,CAAC,EAAE,KAAK,oBAAoB,SAAS,CAAC,EAAE;IAGhE,IAAI,SAAS,GAAG;QACd,MAAM,UAAU;eAAI;SAAc;QAClC,OAAO,CAAC,MAAM,GAAG;QACjB,OAAO,kBAAkB;IAC3B,OAAO;QACL,OAAO,kBAAkB;YAAC;eAAwB;SAAc;IAClE;AACF;AAGO,SAAS,8BACd,OAAgB,EAChB,aAAqB;IAErB,MAAM,oBAAoB,QAAQ,MAAM,KAAK;IAC7C,MAAM,YAAY,oBAAoB,QAAQ,EAAE,GAAG,QAAQ,IAAI;IAE/D,OAAO;QACL,IAAI,QAAQ,EAAE;QACd,SAAS,QAAQ,OAAO;QACxB,WAAW,QAAQ,SAAS;QAC5B,MAAM,QAAQ,IAAI;QAClB;QACA,IAAI,QAAQ,EAAE;QACd,aAAa,oBAAoB,IAAK,QAAQ,MAAM,GAAG,IAAI;QAC3D,UAAU;IACZ;AACF;AAGO,SAAS,mBACd,aAA6B,EAC7B,WAAqB;IAErB,OAAO,cAAc,GAAG,CAAC,CAAA,OAAQ,CAAC;YAChC,GAAG,IAAI;YACP,UAAU,YAAY,QAAQ,CAAC,KAAK,SAAS,CAAC,EAAE;QAClD,CAAC;AACH", "debugId": null}}, {"offset": {"line": 1526, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/kaliha/khalihatanbaa/src/components/messaging/ConversationList.tsx"], "sourcesContent": ["import { useState } from \"react\";\nimport {\n  ChatBubbleLeftRightIcon,\n  UserIcon,\n  ClockIcon,\n  MagnifyingGlassIcon,\n  XMarkIcon,\n} from \"@heroicons/react/24/outline\";\nimport {\n  formatMessageTime,\n  searchConversations,\n  sortConversations,\n} from \"@/lib/messaging\";\n\ninterface Conversation {\n  id: string;\n  content: string;\n  createdAt: string;\n  adId?: string;\n  otherUser: {\n    id: string;\n    name: string;\n    avatar?: string;\n  };\n  ad?: {\n    id: string;\n    title: string;\n    price: number;\n    imageUrls: string[];\n  };\n  unreadCount: number;\n  isOnline?: boolean;\n}\n\ninterface ConversationListProps {\n  conversations: Conversation[];\n  selectedConversationId?: string;\n  onSelectConversation: (conversation: Conversation) => void;\n  loading?: boolean;\n}\n\nexport function ConversationList({\n  conversations,\n  selectedConversationId,\n  onSelectConversation,\n  loading = false,\n}: ConversationListProps) {\n  const [searchQuery, setSearchQuery] = useState(\"\");\n\n  // تصفية وترتيب المحادثات\n  const filteredConversations = searchConversations(conversations, searchQuery);\n  const sortedConversations = sortConversations(filteredConversations);\n\n  if (loading) {\n    return (\n      <div className=\"bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden\">\n        <div className=\"p-4 border-b border-gray-200\">\n          <h2 className=\"font-semibold text-dark-800\">المحادثات</h2>\n        </div>\n        <div className=\"p-6\">\n          <div className=\"animate-pulse space-y-4\">\n            {[...Array(5)].map((_, i) => (\n              <div\n                key={i}\n                className=\"flex items-center space-x-3 space-x-reverse\"\n              >\n                <div className=\"w-10 h-10 bg-gray-200 rounded-full\"></div>\n                <div className=\"flex-1 space-y-2\">\n                  <div className=\"h-4 bg-gray-200 rounded w-3/4\"></div>\n                  <div className=\"h-3 bg-gray-200 rounded w-1/2\"></div>\n                </div>\n              </div>\n            ))}\n          </div>\n        </div>\n      </div>\n    );\n  }\n\n  return (\n    <div\n      className=\"bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden flex flex-col\"\n      style={{ height: \"calc(100vh - 250px)\" }}\n    >\n      {/* رأس قائمة المحادثات */}\n      <div className=\"p-4 border-b border-gray-200\">\n        <h2 className=\"font-semibold text-dark-800 mb-3\">المحادثات</h2>\n\n        {/* شريط البحث */}\n        <div className=\"relative\">\n          <MagnifyingGlassIcon className=\"absolute right-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400\" />\n          <input\n            type=\"text\"\n            placeholder=\"البحث في المحادثات...\"\n            value={searchQuery}\n            onChange={(e) => setSearchQuery(e.target.value)}\n            className=\"w-full pr-10 pl-8 py-2 text-sm border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent\"\n          />\n          {searchQuery && (\n            <button\n              onClick={() => setSearchQuery(\"\")}\n              className=\"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600\"\n            >\n              <XMarkIcon className=\"h-4 w-4\" />\n            </button>\n          )}\n        </div>\n      </div>\n\n      {/* قائمة المحادثات */}\n      <div className=\"flex-1 overflow-y-auto\">\n        {sortedConversations.length === 0 ? (\n          <div className=\"p-6 text-center text-gray-500\">\n            {searchQuery ? (\n              <>\n                <MagnifyingGlassIcon className=\"h-12 w-12 mx-auto mb-3 text-gray-300\" />\n                <p>لا توجد نتائج للبحث</p>\n                <p className=\"text-sm mt-1\">جرب كلمات مختلفة</p>\n              </>\n            ) : (\n              <>\n                <ChatBubbleLeftRightIcon className=\"h-12 w-12 mx-auto mb-3 text-gray-300\" />\n                <p>لا توجد محادثات حتى الآن</p>\n                <p className=\"text-sm mt-1\">\n                  ابدأ محادثة من خلال التواصل مع بائع\n                </p>\n              </>\n            )}\n          </div>\n        ) : (\n          sortedConversations.map((conversation) => (\n            <ConversationItem\n              key={`${conversation.otherUser.id}-${\n                conversation.adId || \"general\"\n              }`}\n              conversation={conversation}\n              isSelected={selectedConversationId === conversation.otherUser.id}\n              onClick={() => onSelectConversation(conversation)}\n            />\n          ))\n        )}\n      </div>\n    </div>\n  );\n}\n\n// مكون عنصر المحادثة\ninterface ConversationItemProps {\n  conversation: Conversation;\n  isSelected: boolean;\n  onClick: () => void;\n}\n\nfunction ConversationItem({\n  conversation,\n  isSelected,\n  onClick,\n}: ConversationItemProps) {\n  return (\n    <button\n      onClick={onClick}\n      className={`w-full p-4 border-b border-gray-100 hover:bg-gray-50 text-right transition-colors ${\n        isSelected ? \"bg-primary-50 border-primary-100\" : \"\"\n      }`}\n    >\n      <div className=\"flex items-start space-x-3 space-x-reverse\">\n        {/* صورة المستخدم مع مؤشر الحالة */}\n        <div className=\"relative w-10 h-10 bg-primary-100 rounded-full flex items-center justify-center flex-shrink-0\">\n          {conversation.otherUser.avatar ? (\n            <img\n              src={conversation.otherUser.avatar}\n              alt={conversation.otherUser.name}\n              className=\"w-full h-full rounded-full object-cover\"\n            />\n          ) : (\n            <UserIcon className=\"h-5 w-5 text-primary-600\" />\n          )}\n\n          {/* مؤشر الحالة المتصلة */}\n          {conversation.isOnline && (\n            <div className=\"absolute -bottom-0.5 -right-0.5 w-3 h-3 bg-green-500 border-2 border-white rounded-full\"></div>\n          )}\n        </div>\n\n        {/* محتوى المحادثة */}\n        <div className=\"flex-1 min-w-0\">\n          {/* اسم المستخدم وعدد الرسائل غير المقروءة */}\n          <div className=\"flex items-center justify-between mb-1\">\n            <p\n              className={`font-medium truncate ${\n                conversation.unreadCount > 0 ? \"text-dark-800\" : \"text-dark-600\"\n              }`}\n            >\n              {conversation.otherUser.name}\n            </p>\n            {conversation.unreadCount > 0 && (\n              <span className=\"bg-primary-500 text-white text-xs rounded-full px-2 py-1 min-w-[20px] text-center\">\n                {conversation.unreadCount > 99\n                  ? \"99+\"\n                  : conversation.unreadCount}\n              </span>\n            )}\n          </div>\n\n          {/* عنوان الإعلان إن وجد */}\n          {conversation.ad && (\n            <p className=\"text-sm text-primary-600 truncate mb-1\">\n              📦 {conversation.ad.title}\n            </p>\n          )}\n\n          {/* آخر رسالة */}\n          <p\n            className={`text-sm truncate ${\n              conversation.unreadCount > 0\n                ? \"text-gray-700 font-medium\"\n                : \"text-gray-500\"\n            }`}\n          >\n            {conversation.content}\n          </p>\n\n          {/* وقت آخر رسالة */}\n          <div className=\"flex items-center mt-1\">\n            <ClockIcon className=\"h-3 w-3 text-gray-400 ml-1\" />\n            <span className=\"text-xs text-gray-400\">\n              {formatMessageTime(conversation.createdAt)}\n            </span>\n          </div>\n        </div>\n      </div>\n    </button>\n  );\n}\n\n// مكون لعرض إحصائيات المحادثات\ninterface ConversationStatsProps {\n  totalConversations: number;\n  unreadCount: number;\n}\n\nexport function ConversationStats({\n  totalConversations,\n  unreadCount,\n}: ConversationStatsProps) {\n  return (\n    <div className=\"px-4 py-2 bg-gray-50 border-b border-gray-200 text-sm text-gray-600\">\n      <div className=\"flex items-center justify-between\">\n        <span>{totalConversations} محادثة</span>\n        {unreadCount > 0 && (\n          <span className=\"text-primary-600 font-medium\">\n            {unreadCount} غير مقروءة\n          </span>\n        )}\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;;AAAA;AACA;AAAA;AAAA;AAAA;AAAA;AAOA;;;;;AAiCO,SAAS,iBAAiB,EAC/B,aAAa,EACb,sBAAsB,EACtB,oBAAoB,EACpB,UAAU,KAAK,EACO;IACtB,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE/C,yBAAyB;IACzB,MAAM,wBAAwB,CAAA,GAAA,uHAAA,CAAA,sBAAmB,AAAD,EAAE,eAAe;IACjE,MAAM,sBAAsB,CAAA,GAAA,uHAAA,CAAA,oBAAiB,AAAD,EAAE;IAE9C,IAAI,SAAS;QACX,qBACE,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAG,WAAU;kCAA8B;;;;;;;;;;;8BAE9C,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;kCACZ;+BAAI,MAAM;yBAAG,CAAC,GAAG,CAAC,CAAC,GAAG,kBACrB,8OAAC;gCAEC,WAAU;;kDAEV,8OAAC;wCAAI,WAAU;;;;;;kDACf,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;;;;;;0DACf,8OAAC;gDAAI,WAAU;;;;;;;;;;;;;+BANZ;;;;;;;;;;;;;;;;;;;;;IAcnB;IAEA,qBACE,8OAAC;QACC,WAAU;QACV,OAAO;YAAE,QAAQ;QAAsB;;0BAGvC,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAG,WAAU;kCAAmC;;;;;;kCAGjD,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,qOAAA,CAAA,sBAAmB;gCAAC,WAAU;;;;;;0CAC/B,8OAAC;gCACC,MAAK;gCACL,aAAY;gCACZ,OAAO;gCACP,UAAU,CAAC,IAAM,eAAe,EAAE,MAAM,CAAC,KAAK;gCAC9C,WAAU;;;;;;4BAEX,6BACC,8OAAC;gCACC,SAAS,IAAM,eAAe;gCAC9B,WAAU;0CAEV,cAAA,8OAAC,iNAAA,CAAA,YAAS;oCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;0BAO7B,8OAAC;gBAAI,WAAU;0BACZ,oBAAoB,MAAM,KAAK,kBAC9B,8OAAC;oBAAI,WAAU;8BACZ,4BACC;;0CACE,8OAAC,qOAAA,CAAA,sBAAmB;gCAAC,WAAU;;;;;;0CAC/B,8OAAC;0CAAE;;;;;;0CACH,8OAAC;gCAAE,WAAU;0CAAe;;;;;;;qDAG9B;;0CACE,8OAAC,6OAAA,CAAA,0BAAuB;gCAAC,WAAU;;;;;;0CACnC,8OAAC;0CAAE;;;;;;0CACH,8OAAC;gCAAE,WAAU;0CAAe;;;;;;;;;;;;2BAOlC,oBAAoB,GAAG,CAAC,CAAC,6BACvB,8OAAC;wBAIC,cAAc;wBACd,YAAY,2BAA2B,aAAa,SAAS,CAAC,EAAE;wBAChE,SAAS,IAAM,qBAAqB;uBAL/B,GAAG,aAAa,SAAS,CAAC,EAAE,CAAC,CAAC,EACjC,aAAa,IAAI,IAAI,WACrB;;;;;;;;;;;;;;;;AAUhB;AASA,SAAS,iBAAiB,EACxB,YAAY,EACZ,UAAU,EACV,OAAO,EACe;IACtB,qBACE,8OAAC;QACC,SAAS;QACT,WAAW,CAAC,kFAAkF,EAC5F,aAAa,qCAAqC,IAClD;kBAEF,cAAA,8OAAC;YAAI,WAAU;;8BAEb,8OAAC;oBAAI,WAAU;;wBACZ,aAAa,SAAS,CAAC,MAAM,iBAC5B,8OAAC;4BACC,KAAK,aAAa,SAAS,CAAC,MAAM;4BAClC,KAAK,aAAa,SAAS,CAAC,IAAI;4BAChC,WAAU;;;;;iDAGZ,8OAAC,+MAAA,CAAA,WAAQ;4BAAC,WAAU;;;;;;wBAIrB,aAAa,QAAQ,kBACpB,8OAAC;4BAAI,WAAU;;;;;;;;;;;;8BAKnB,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCACC,WAAW,CAAC,qBAAqB,EAC/B,aAAa,WAAW,GAAG,IAAI,kBAAkB,iBACjD;8CAED,aAAa,SAAS,CAAC,IAAI;;;;;;gCAE7B,aAAa,WAAW,GAAG,mBAC1B,8OAAC;oCAAK,WAAU;8CACb,aAAa,WAAW,GAAG,KACxB,QACA,aAAa,WAAW;;;;;;;;;;;;wBAMjC,aAAa,EAAE,kBACd,8OAAC;4BAAE,WAAU;;gCAAyC;gCAChD,aAAa,EAAE,CAAC,KAAK;;;;;;;sCAK7B,8OAAC;4BACC,WAAW,CAAC,iBAAiB,EAC3B,aAAa,WAAW,GAAG,IACvB,8BACA,iBACJ;sCAED,aAAa,OAAO;;;;;;sCAIvB,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,iNAAA,CAAA,YAAS;oCAAC,WAAU;;;;;;8CACrB,8OAAC;oCAAK,WAAU;8CACb,CAAA,GAAA,uHAAA,CAAA,oBAAiB,AAAD,EAAE,aAAa,SAAS;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOvD;AAQO,SAAS,kBAAkB,EAChC,kBAAkB,EAClB,WAAW,EACY;IACvB,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC;;wBAAM;wBAAmB;;;;;;;gBACzB,cAAc,mBACb,8OAAC;oBAAK,WAAU;;wBACb;wBAAY;;;;;;;;;;;;;;;;;;AAMzB", "debugId": null}}, {"offset": {"line": 1945, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/kaliha/khalihatanbaa/src/components/messaging/MessageBubble.tsx"], "sourcesContent": ["import { CheckIcon, ClockIcon } from \"@heroicons/react/24/outline\";\nimport { formatMessageTime, isTempMessage } from \"@/lib/messaging\";\n\ninterface Message {\n  id: string;\n  content: string;\n  isRead: boolean;\n  createdAt: string;\n  fromId: string;\n  toId: string;\n  from: {\n    id: string;\n    name: string;\n    avatar?: string;\n  };\n  to: {\n    id: string;\n    name: string;\n    avatar?: string;\n  };\n}\n\ninterface MessageBubbleProps {\n  message: Message;\n  isOwn: boolean;\n  showAvatar?: boolean;\n  isLastInGroup?: boolean;\n}\n\nexport function MessageBubble({\n  message,\n  isOwn,\n  showAvatar = false,\n  isLastInGroup = true,\n}: MessageBubbleProps) {\n  const isTemp = isTempMessage(message.id);\n\n  return (\n    <div className={`flex ${isOwn ? \"justify-end\" : \"justify-start\"} mb-2`}>\n      <div className=\"flex items-end space-x-2 space-x-reverse max-w-xs lg:max-w-md\">\n        {/* Avatar للرسائل الواردة */}\n        {!isOwn && showAvatar && (\n          <div className=\"w-6 h-6 rounded-full bg-primary-100 flex items-center justify-center flex-shrink-0\">\n            {message.from.avatar ? (\n              <img\n                src={message.from.avatar}\n                alt={message.from.name}\n                className=\"w-full h-full rounded-full object-cover\"\n              />\n            ) : (\n              <span className=\"text-xs font-medium text-primary-600\">\n                {message.from.name.charAt(0)}\n              </span>\n            )}\n          </div>\n        )}\n\n        {/* فقاعة الرسالة */}\n        <div\n          className={`px-4 py-2 rounded-2xl shadow-sm relative ${\n            isOwn\n              ? `bg-primary-500 text-white ${\n                  isTemp ? \"opacity-70\" : \"\"\n                }`\n              : \"bg-white text-gray-900 border border-gray-200\"\n          } ${isTemp ? \"animate-pulse\" : \"\"} ${\n            isLastInGroup\n              ? isOwn\n                ? \"rounded-br-md\"\n                : \"rounded-bl-md\"\n              : \"\"\n          }`}\n        >\n          {/* محتوى الرسالة */}\n          <p className=\"text-sm leading-relaxed break-words whitespace-pre-wrap\">\n            {message.content}\n          </p>\n\n          {/* معلومات الرسالة */}\n          <div\n            className={`flex items-center justify-between mt-2 space-x-2 space-x-reverse ${\n              isOwn ? \"text-primary-100\" : \"text-gray-500\"\n            }`}\n          >\n            <span className=\"text-xs\">\n              {formatMessageTime(message.createdAt)}\n            </span>\n\n            {/* حالة الرسالة للرسائل المرسلة */}\n            {isOwn && (\n              <div className=\"flex items-center\">\n                {isTemp ? (\n                  <ClockIcon className=\"w-3 h-3\" />\n                ) : (\n                  <div className=\"flex\">\n                    <CheckIcon className=\"w-3 h-3\" />\n                    {message.isRead && (\n                      <CheckIcon className=\"w-3 h-3 -mr-1\" />\n                    )}\n                  </div>\n                )}\n              </div>\n            )}\n          </div>\n\n          {/* مؤشر التحميل للرسائل المؤقتة */}\n          {isTemp && (\n            <div className=\"absolute -bottom-1 -right-1\">\n              <div className=\"w-2 h-2 bg-current rounded-full animate-ping\"></div>\n            </div>\n          )}\n        </div>\n      </div>\n    </div>\n  );\n}\n\n// مكون لتجميع الرسائل حسب المرسل\ninterface MessageGroupProps {\n  messages: Message[];\n  currentUserId: string;\n}\n\nexport function MessageGroup({ messages, currentUserId }: MessageGroupProps) {\n  if (messages.length === 0) return null;\n\n  const firstMessage = messages[0];\n  const isOwn = firstMessage.fromId === currentUserId;\n\n  return (\n    <div className={`flex flex-col space-y-1 ${isOwn ? \"items-end\" : \"items-start\"}`}>\n      {messages.map((message, index) => (\n        <MessageBubble\n          key={message.id}\n          message={message}\n          isOwn={isOwn}\n          showAvatar={!isOwn && index === messages.length - 1}\n          isLastInGroup={index === messages.length - 1}\n        />\n      ))}\n    </div>\n  );\n}\n\n// دالة لتجميع الرسائل المتتالية من نفس المرسل\nexport function groupConsecutiveMessages(messages: Message[]): Message[][] {\n  if (messages.length === 0) return [];\n\n  const groups: Message[][] = [];\n  let currentGroup: Message[] = [messages[0]];\n\n  for (let i = 1; i < messages.length; i++) {\n    const currentMessage = messages[i];\n    const previousMessage = messages[i - 1];\n\n    // تحقق من كون الرسالة من نفس المرسل والوقت قريب (أقل من 5 دقائق)\n    const timeDiff = new Date(currentMessage.createdAt).getTime() - new Date(previousMessage.createdAt).getTime();\n    const isSameSender = currentMessage.fromId === previousMessage.fromId;\n    const isWithinTimeLimit = timeDiff < 5 * 60 * 1000; // 5 دقائق\n\n    if (isSameSender && isWithinTimeLimit) {\n      currentGroup.push(currentMessage);\n    } else {\n      groups.push(currentGroup);\n      currentGroup = [currentMessage];\n    }\n  }\n\n  groups.push(currentGroup);\n  return groups;\n}\n\n// مكون لعرض فاصل التاريخ\ninterface DateSeparatorProps {\n  date: string;\n}\n\nexport function DateSeparator({ date }: DateSeparatorProps) {\n  return (\n    <div className=\"flex items-center justify-center my-4\">\n      <div className=\"bg-gray-100 text-gray-600 text-xs px-3 py-1 rounded-full\">\n        {date}\n      </div>\n    </div>\n  );\n}\n\n// مكون لعرض مؤشر الكتابة\ninterface TypingIndicatorProps {\n  userName: string;\n}\n\nexport function TypingIndicator({ userName }: TypingIndicatorProps) {\n  return (\n    <div className=\"flex items-start space-x-2 space-x-reverse mb-2\">\n      <div className=\"w-6 h-6 rounded-full bg-primary-100 flex items-center justify-center flex-shrink-0\">\n        <span className=\"text-xs font-medium text-primary-600\">\n          {userName.charAt(0)}\n        </span>\n      </div>\n      \n      <div className=\"bg-white border border-gray-200 rounded-2xl rounded-bl-md px-4 py-2 shadow-sm\">\n        <div className=\"flex space-x-1\">\n          <div className=\"w-2 h-2 bg-gray-400 rounded-full animate-bounce\"></div>\n          <div className=\"w-2 h-2 bg-gray-400 rounded-full animate-bounce\" style={{ animationDelay: '0.1s' }}></div>\n          <div className=\"w-2 h-2 bg-gray-400 rounded-full animate-bounce\" style={{ animationDelay: '0.2s' }}></div>\n        </div>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;;;;;AAAA;AAAA;AACA;;;;AA4BO,SAAS,cAAc,EAC5B,OAAO,EACP,KAAK,EACL,aAAa,KAAK,EAClB,gBAAgB,IAAI,EACD;IACnB,MAAM,SAAS,CAAA,GAAA,uHAAA,CAAA,gBAAa,AAAD,EAAE,QAAQ,EAAE;IAEvC,qBACE,8OAAC;QAAI,WAAW,CAAC,KAAK,EAAE,QAAQ,gBAAgB,gBAAgB,KAAK,CAAC;kBACpE,cAAA,8OAAC;YAAI,WAAU;;gBAEZ,CAAC,SAAS,4BACT,8OAAC;oBAAI,WAAU;8BACZ,QAAQ,IAAI,CAAC,MAAM,iBAClB,8OAAC;wBACC,KAAK,QAAQ,IAAI,CAAC,MAAM;wBACxB,KAAK,QAAQ,IAAI,CAAC,IAAI;wBACtB,WAAU;;;;;6CAG<PERSON>,8OAAC;wBAAK,WAAU;kCACb,QAAQ,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC;;;;;;;;;;;8BAOlC,8OAAC;oBACC,WAAW,CAAC,yCAAyC,EACnD,QACI,CAAC,0BAA0B,EACzB,SAAS,eAAe,IACxB,GACF,gDACL,CAAC,EAAE,SAAS,kBAAkB,GAAG,CAAC,EACjC,gBACI,QACE,kBACA,kBACF,IACJ;;sCAGF,8OAAC;4BAAE,WAAU;sCACV,QAAQ,OAAO;;;;;;sCAIlB,8OAAC;4BACC,WAAW,CAAC,iEAAiE,EAC3E,QAAQ,qBAAqB,iBAC7B;;8CAEF,8OAAC;oCAAK,WAAU;8CACb,CAAA,GAAA,uHAAA,CAAA,oBAAiB,AAAD,EAAE,QAAQ,SAAS;;;;;;gCAIrC,uBACC,8OAAC;oCAAI,WAAU;8CACZ,uBACC,8OAAC,iNAAA,CAAA,YAAS;wCAAC,WAAU;;;;;6DAErB,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,iNAAA,CAAA,YAAS;gDAAC,WAAU;;;;;;4CACpB,QAAQ,MAAM,kBACb,8OAAC,iNAAA,CAAA,YAAS;gDAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;wBAShC,wBACC,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAI,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAO7B;AAQO,SAAS,aAAa,EAAE,QAAQ,EAAE,aAAa,EAAqB;IACzE,IAAI,SAAS,MAAM,KAAK,GAAG,OAAO;IAElC,MAAM,eAAe,QAAQ,CAAC,EAAE;IAChC,MAAM,QAAQ,aAAa,MAAM,KAAK;IAEtC,qBACE,8OAAC;QAAI,WAAW,CAAC,wBAAwB,EAAE,QAAQ,cAAc,eAAe;kBAC7E,SAAS,GAAG,CAAC,CAAC,SAAS,sBACtB,8OAAC;gBAEC,SAAS;gBACT,OAAO;gBACP,YAAY,CAAC,SAAS,UAAU,SAAS,MAAM,GAAG;gBAClD,eAAe,UAAU,SAAS,MAAM,GAAG;eAJtC,QAAQ,EAAE;;;;;;;;;;AASzB;AAGO,SAAS,yBAAyB,QAAmB;IAC1D,IAAI,SAAS,MAAM,KAAK,GAAG,OAAO,EAAE;IAEpC,MAAM,SAAsB,EAAE;IAC9B,IAAI,eAA0B;QAAC,QAAQ,CAAC,EAAE;KAAC;IAE3C,IAAK,IAAI,IAAI,GAAG,IAAI,SAAS,MAAM,EAAE,IAAK;QACxC,MAAM,iBAAiB,QAAQ,CAAC,EAAE;QAClC,MAAM,kBAAkB,QAAQ,CAAC,IAAI,EAAE;QAEvC,iEAAiE;QACjE,MAAM,WAAW,IAAI,KAAK,eAAe,SAAS,EAAE,OAAO,KAAK,IAAI,KAAK,gBAAgB,SAAS,EAAE,OAAO;QAC3G,MAAM,eAAe,eAAe,MAAM,KAAK,gBAAgB,MAAM;QACrE,MAAM,oBAAoB,WAAW,IAAI,KAAK,MAAM,UAAU;QAE9D,IAAI,gBAAgB,mBAAmB;YACrC,aAAa,IAAI,CAAC;QACpB,OAAO;YACL,OAAO,IAAI,CAAC;YACZ,eAAe;gBAAC;aAAe;QACjC;IACF;IAEA,OAAO,IAAI,CAAC;IACZ,OAAO;AACT;AAOO,SAAS,cAAc,EAAE,IAAI,EAAsB;IACxD,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC;YAAI,WAAU;sBACZ;;;;;;;;;;;AAIT;AAOO,SAAS,gBAAgB,EAAE,QAAQ,EAAwB;IAChE,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAK,WAAU;8BACb,SAAS,MAAM,CAAC;;;;;;;;;;;0BAIrB,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;;;;;sCACf,8OAAC;4BAAI,WAAU;4BAAkD,OAAO;gCAAE,gBAAgB;4BAAO;;;;;;sCACjG,8OAAC;4BAAI,WAAU;4BAAkD,OAAO;gCAAE,gBAAgB;4BAAO;;;;;;;;;;;;;;;;;;;;;;;AAK3G", "debugId": null}}, {"offset": {"line": 2224, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/kaliha/khalihatanbaa/src/components/messaging/ChatArea.tsx"], "sourcesContent": ["import { useState, useRef, useEffect } from \"react\";\nimport {\n  PaperAirplaneIcon,\n  UserIcon,\n  PhotoIcon,\n  FaceSmileIcon,\n  ArrowUpIcon,\n} from \"@heroicons/react/24/outline\";\nimport {\n  MessageGroup,\n  groupConsecutiveMessages,\n  DateSeparator,\n  TypingIndicator,\n} from \"./MessageBubble\";\nimport { groupMessagesByDate, validateMessage } from \"@/lib/messaging\";\n\ninterface Message {\n  id: string;\n  content: string;\n  isRead: boolean;\n  createdAt: string;\n  fromId: string;\n  toId: string;\n  from: {\n    id: string;\n    name: string;\n    avatar?: string;\n  };\n  to: {\n    id: string;\n    name: string;\n    avatar?: string;\n  };\n}\n\ninterface ChatAreaProps {\n  messages: Message[];\n  currentUserId: string;\n  otherUser?: {\n    id: string;\n    name: string;\n    avatar?: string;\n  };\n  ad?: {\n    id: string;\n    title: string;\n    price: number;\n    imageUrls: string[];\n  };\n  onSendMessage: (content: string) => Promise<boolean>;\n  sending: boolean;\n  hasMore?: boolean;\n  onLoadMore?: () => void;\n  loading?: boolean;\n}\n\nexport function ChatArea({\n  messages,\n  currentUserId,\n  otherUser,\n  ad,\n  onSendMessage,\n  sending,\n  hasMore = false,\n  onLoadMore,\n  loading = false,\n}: ChatAreaProps) {\n  const [newMessage, setNewMessage] = useState(\"\");\n  const [error, setError] = useState<string | null>(null);\n  const [showScrollButton, setShowScrollButton] = useState(false);\n  const [isTyping, setIsTyping] = useState(false);\n\n  const messagesContainerRef = useRef<HTMLDivElement>(null);\n  const messagesEndRef = useRef<HTMLDivElement>(null);\n  const inputRef = useRef<HTMLInputElement>(null);\n\n  // تجميع الرسائل حسب التاريخ\n  const messagesByDate = groupMessagesByDate(messages);\n\n  // التمرير إلى أسفل\n  const scrollToBottom = (smooth = true) => {\n    messagesEndRef.current?.scrollIntoView({\n      behavior: smooth ? \"smooth\" : \"auto\",\n    });\n  };\n\n  // مراقبة التمرير\n  const handleScroll = () => {\n    if (!messagesContainerRef.current) return;\n\n    const { scrollTop, scrollHeight, clientHeight } =\n      messagesContainerRef.current;\n    const isNearBottom = scrollHeight - scrollTop - clientHeight < 100;\n\n    setShowScrollButton(!isNearBottom);\n\n    // تحميل المزيد من الرسائل عند الوصول للأعلى (مع تحسين الأداء)\n    if (scrollTop < 100 && hasMore && onLoadMore && !loading) {\n      const currentScrollHeight = scrollHeight;\n      onLoadMore();\n\n      // الحفاظ على موضع التمرير بعد تحميل الرسائل الجديدة\n      setTimeout(() => {\n        if (messagesContainerRef.current) {\n          const newScrollHeight = messagesContainerRef.current.scrollHeight;\n          const scrollDiff = newScrollHeight - currentScrollHeight;\n          messagesContainerRef.current.scrollTop = scrollTop + scrollDiff;\n        }\n      }, 100);\n    }\n  };\n\n  // إرسال الرسالة\n  const handleSendMessage = async () => {\n    const validation = validateMessage(newMessage);\n    if (!validation.isValid) {\n      setError(validation.error || \"رسالة غير صحيحة\");\n      return;\n    }\n\n    setError(null);\n    const success = await onSendMessage(newMessage.trim());\n\n    if (success) {\n      setNewMessage(\"\");\n      // التركيز على حقل الإدخال\n      setTimeout(() => {\n        inputRef.current?.focus();\n      }, 100);\n    }\n  };\n\n  // معالجة الضغط على Enter\n  const handleKeyDown = (e: React.KeyboardEvent) => {\n    if (e.key === \"Enter\" && !e.shiftKey) {\n      e.preventDefault();\n      handleSendMessage();\n    }\n  };\n\n  // التمرير التلقائي فقط عند إرسال رسالة من المستخدم الحالي\n  useEffect(() => {\n    if (messages.length > 0) {\n      const lastMessage = messages[messages.length - 1];\n      // التمرير التلقائي فقط إذا كانت الرسالة من المستخدم الحالي وتم إرسالها حديثاً (آخر 5 ثوان)\n      const messageTime = new Date(lastMessage.createdAt).getTime();\n      const now = Date.now();\n      const isRecent = now - messageTime < 5000; // 5 ثوان\n\n      if (lastMessage.fromId === currentUserId && isRecent) {\n        scrollToBottom();\n      }\n    }\n  }, [messages, currentUserId]);\n\n  // التركيز على حقل الإدخال عند تحديد محادثة والتمرير للأسفل\n  useEffect(() => {\n    if (otherUser) {\n      inputRef.current?.focus();\n      // التمرير للأسفل عند تحميل محادثة جديدة\n      setTimeout(() => {\n        scrollToBottom(false); // بدون animation للسرعة\n      }, 100);\n    }\n  }, [otherUser]);\n\n  if (!otherUser) {\n    return (\n      <div\n        className=\"lg:col-span-2 bg-white rounded-lg shadow-sm border border-gray-200 flex items-center justify-center\"\n        style={{ height: \"calc(100vh - 250px)\" }}\n      >\n        <div className=\"text-center text-gray-500\">\n          <UserIcon className=\"h-16 w-16 mx-auto mb-4 text-gray-300\" />\n          <p className=\"text-lg font-medium\">اختر محادثة لبدء المراسلة</p>\n          <p className=\"text-sm mt-1\">اختر محادثة من القائمة الجانبية</p>\n        </div>\n      </div>\n    );\n  }\n\n  return (\n    <div\n      className=\"lg:col-span-2 bg-white rounded-lg shadow-sm border border-gray-200 flex flex-col\"\n      style={{ height: \"calc(100vh - 250px)\" }}\n    >\n      {/* رأس المحادثة */}\n      <div className=\"p-4 border-b border-gray-200 bg-gray-50\">\n        <div className=\"flex items-center space-x-3 space-x-reverse\">\n          <div className=\"relative w-10 h-10 bg-primary-100 rounded-full flex items-center justify-center\">\n            {otherUser.avatar ? (\n              <img\n                src={otherUser.avatar}\n                alt={otherUser.name}\n                className=\"w-full h-full rounded-full object-cover\"\n              />\n            ) : (\n              <UserIcon className=\"h-5 w-5 text-primary-600\" />\n            )}\n          </div>\n          <div className=\"flex-1\">\n            <p className=\"font-medium text-dark-800\">{otherUser.name}</p>\n            {ad && <p className=\"text-sm text-gray-500\">حول: {ad.title}</p>}\n          </div>\n        </div>\n      </div>\n\n      {/* منطقة الرسائل */}\n      <div\n        ref={messagesContainerRef}\n        onScroll={handleScroll}\n        className=\"flex-1 overflow-y-auto p-4 space-y-4 relative\"\n      >\n        {/* مؤشر التحميل للرسائل القديمة */}\n        {loading && (\n          <div className=\"flex justify-center py-4\">\n            <div className=\"w-6 h-6 border-2 border-primary-500 border-t-transparent rounded-full animate-spin\"></div>\n          </div>\n        )}\n\n        {/* عرض الرسائل مجمعة حسب التاريخ */}\n        {Object.entries(messagesByDate).map(([date, dateMessages]) => {\n          const messageGroups = groupConsecutiveMessages(dateMessages);\n\n          return (\n            <div key={date}>\n              <DateSeparator date={date} />\n              <div className=\"space-y-4\">\n                {messageGroups.map((group, groupIndex) => (\n                  <MessageGroup\n                    key={`${date}-${groupIndex}`}\n                    messages={group}\n                    currentUserId={currentUserId}\n                  />\n                ))}\n              </div>\n            </div>\n          );\n        })}\n\n        {/* مؤشر الكتابة */}\n        {isTyping && <TypingIndicator userName={otherUser.name} />}\n\n        <div ref={messagesEndRef} />\n\n        {/* زر التمرير للأسفل */}\n        {showScrollButton && (\n          <button\n            onClick={() => scrollToBottom()}\n            className=\"fixed bottom-24 left-1/2 transform -translate-x-1/2 bg-primary-500 text-white p-2 rounded-full shadow-lg hover:bg-primary-600 transition-colors z-10\"\n          >\n            <ArrowUpIcon className=\"h-5 w-5 transform rotate-180\" />\n          </button>\n        )}\n      </div>\n\n      {/* منطقة إرسال الرسالة */}\n      <div className=\"p-4 border-t border-gray-200 bg-gray-50\">\n        {/* عرض الأخطاء */}\n        {error && (\n          <div className=\"mb-3 p-2 bg-red-50 border border-red-200 rounded text-red-700 text-sm\">\n            {error}\n          </div>\n        )}\n\n        <div className=\"flex items-end space-x-2 space-x-reverse\">\n          {/* حقل إدخال الرسالة */}\n          <div className=\"flex-1\">\n            <input\n              ref={inputRef}\n              type=\"text\"\n              value={newMessage}\n              onChange={(e) => {\n                setNewMessage(e.target.value);\n                setError(null);\n              }}\n              onKeyDown={handleKeyDown}\n              placeholder=\"اكتب رسالتك...\"\n              className=\"w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent resize-none\"\n              disabled={sending}\n              maxLength={1000}\n            />\n\n            {/* عداد الأحرف */}\n            <div className=\"flex justify-between items-center mt-1 px-1\">\n              <span className=\"text-xs text-gray-400\">\n                {newMessage.length}/1000\n              </span>\n            </div>\n          </div>\n\n          {/* أزرار إضافية */}\n          <div className=\"flex items-center space-x-1 space-x-reverse\">\n            {/* زر الصور (مستقبلاً) */}\n            <button\n              type=\"button\"\n              className=\"p-2 text-gray-400 hover:text-gray-600 transition-colors\"\n              disabled\n            >\n              <PhotoIcon className=\"h-5 w-5\" />\n            </button>\n\n            {/* زر الإيموجي (مستقبلاً) */}\n            <button\n              type=\"button\"\n              className=\"p-2 text-gray-400 hover:text-gray-600 transition-colors\"\n              disabled\n            >\n              <FaceSmileIcon className=\"h-5 w-5\" />\n            </button>\n\n            {/* زر الإرسال */}\n            <button\n              onClick={handleSendMessage}\n              disabled={!newMessage.trim() || sending}\n              className=\"px-4 py-3 bg-primary-500 text-white rounded-lg hover:bg-primary-600 disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center transition-colors\"\n            >\n              {sending ? (\n                <div className=\"w-5 h-5 border-2 border-white border-t-transparent rounded-full animate-spin\"></div>\n              ) : (\n                <PaperAirplaneIcon className=\"h-5 w-5\" />\n              )}\n            </button>\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAAA;AACA;AAAA;AAAA;AAAA;AAAA;AAOA;AAMA;;;;;;AA0CO,SAAS,SAAS,EACvB,QAAQ,EACR,aAAa,EACb,SAAS,EACT,EAAE,EACF,aAAa,EACb,OAAO,EACP,UAAU,KAAK,EACf,UAAU,EACV,UAAU,KAAK,EACD;IACd,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IAClD,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACzD,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEzC,MAAM,uBAAuB,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAkB;IACpD,MAAM,iBAAiB,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAkB;IAC9C,MAAM,WAAW,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAoB;IAE1C,4BAA4B;IAC5B,MAAM,iBAAiB,CAAA,GAAA,uHAAA,CAAA,sBAAmB,AAAD,EAAE;IAE3C,mBAAmB;IACnB,MAAM,iBAAiB,CAAC,SAAS,IAAI;QACnC,eAAe,OAAO,EAAE,eAAe;YACrC,UAAU,SAAS,WAAW;QAChC;IACF;IAEA,iBAAiB;IACjB,MAAM,eAAe;QACnB,IAAI,CAAC,qBAAqB,OAAO,EAAE;QAEnC,MAAM,EAAE,SAAS,EAAE,YAAY,EAAE,YAAY,EAAE,GAC7C,qBAAqB,OAAO;QAC9B,MAAM,eAAe,eAAe,YAAY,eAAe;QAE/D,oBAAoB,CAAC;QAErB,8DAA8D;QAC9D,IAAI,YAAY,OAAO,WAAW,cAAc,CAAC,SAAS;YACxD,MAAM,sBAAsB;YAC5B;YAEA,oDAAoD;YACpD,WAAW;gBACT,IAAI,qBAAqB,OAAO,EAAE;oBAChC,MAAM,kBAAkB,qBAAqB,OAAO,CAAC,YAAY;oBACjE,MAAM,aAAa,kBAAkB;oBACrC,qBAAqB,OAAO,CAAC,SAAS,GAAG,YAAY;gBACvD;YACF,GAAG;QACL;IACF;IAEA,gBAAgB;IAChB,MAAM,oBAAoB;QACxB,MAAM,aAAa,CAAA,GAAA,uHAAA,CAAA,kBAAe,AAAD,EAAE;QACnC,IAAI,CAAC,WAAW,OAAO,EAAE;YACvB,SAAS,WAAW,KAAK,IAAI;YAC7B;QACF;QAEA,SAAS;QACT,MAAM,UAAU,MAAM,cAAc,WAAW,IAAI;QAEnD,IAAI,SAAS;YACX,cAAc;YACd,0BAA0B;YAC1B,WAAW;gBACT,SAAS,OAAO,EAAE;YACpB,GAAG;QACL;IACF;IAEA,yBAAyB;IACzB,MAAM,gBAAgB,CAAC;QACrB,IAAI,EAAE,GAAG,KAAK,WAAW,CAAC,EAAE,QAAQ,EAAE;YACpC,EAAE,cAAc;YAChB;QACF;IACF;IAEA,0DAA0D;IAC1D,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,SAAS,MAAM,GAAG,GAAG;YACvB,MAAM,cAAc,QAAQ,CAAC,SAAS,MAAM,GAAG,EAAE;YACjD,2FAA2F;YAC3F,MAAM,cAAc,IAAI,KAAK,YAAY,SAAS,EAAE,OAAO;YAC3D,MAAM,MAAM,KAAK,GAAG;YACpB,MAAM,WAAW,MAAM,cAAc,MAAM,SAAS;YAEpD,IAAI,YAAY,MAAM,KAAK,iBAAiB,UAAU;gBACpD;YACF;QACF;IACF,GAAG;QAAC;QAAU;KAAc;IAE5B,2DAA2D;IAC3D,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,WAAW;YACb,SAAS,OAAO,EAAE;YAClB,wCAAwC;YACxC,WAAW;gBACT,eAAe,QAAQ,wBAAwB;YACjD,GAAG;QACL;IACF,GAAG;QAAC;KAAU;IAEd,IAAI,CAAC,WAAW;QACd,qBACE,8OAAC;YACC,WAAU;YACV,OAAO;gBAAE,QAAQ;YAAsB;sBAEvC,cAAA,8OAAC;gBAAI,WAAU;;kCACb,8OAAC,+MAAA,CAAA,WAAQ;wBAAC,WAAU;;;;;;kCACpB,8OAAC;wBAAE,WAAU;kCAAsB;;;;;;kCACnC,8OAAC;wBAAE,WAAU;kCAAe;;;;;;;;;;;;;;;;;IAIpC;IAEA,qBACE,8OAAC;QACC,WAAU;QACV,OAAO;YAAE,QAAQ;QAAsB;;0BAGvC,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;sCACZ,UAAU,MAAM,iBACf,8OAAC;gCACC,KAAK,UAAU,MAAM;gCACrB,KAAK,UAAU,IAAI;gCACnB,WAAU;;;;;qDAGZ,8OAAC,+MAAA,CAAA,WAAQ;gCAAC,WAAU;;;;;;;;;;;sCAGxB,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAE,WAAU;8CAA6B,UAAU,IAAI;;;;;;gCACvD,oBAAM,8OAAC;oCAAE,WAAU;;wCAAwB;wCAAM,GAAG,KAAK;;;;;;;;;;;;;;;;;;;;;;;;0BAMhE,8OAAC;gBACC,KAAK;gBACL,UAAU;gBACV,WAAU;;oBAGT,yBACC,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;;;;;;;;;;;oBAKlB,OAAO,OAAO,CAAC,gBAAgB,GAAG,CAAC,CAAC,CAAC,MAAM,aAAa;wBACvD,MAAM,gBAAgB,CAAA,GAAA,gJAAA,CAAA,2BAAwB,AAAD,EAAE;wBAE/C,qBACE,8OAAC;;8CACC,8OAAC,gJAAA,CAAA,gBAAa;oCAAC,MAAM;;;;;;8CACrB,8OAAC;oCAAI,WAAU;8CACZ,cAAc,GAAG,CAAC,CAAC,OAAO,2BACzB,8OAAC,gJAAA,CAAA,eAAY;4CAEX,UAAU;4CACV,eAAe;2CAFV,GAAG,KAAK,CAAC,EAAE,YAAY;;;;;;;;;;;2BAL1B;;;;;oBAad;oBAGC,0BAAY,8OAAC,gJAAA,CAAA,kBAAe;wBAAC,UAAU,UAAU,IAAI;;;;;;kCAEtD,8OAAC;wBAAI,KAAK;;;;;;oBAGT,kCACC,8OAAC;wBACC,SAAS,IAAM;wBACf,WAAU;kCAEV,cAAA,8OAAC,qNAAA,CAAA,cAAW;4BAAC,WAAU;;;;;;;;;;;;;;;;;0BAM7B,8OAAC;gBAAI,WAAU;;oBAEZ,uBACC,8OAAC;wBAAI,WAAU;kCACZ;;;;;;kCAIL,8OAAC;wBAAI,WAAU;;0CAEb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCACC,KAAK;wCACL,MAAK;wCACL,OAAO;wCACP,UAAU,CAAC;4CACT,cAAc,EAAE,MAAM,CAAC,KAAK;4CAC5B,SAAS;wCACX;wCACA,WAAW;wCACX,aAAY;wCACZ,WAAU;wCACV,UAAU;wCACV,WAAW;;;;;;kDAIb,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CAAK,WAAU;;gDACb,WAAW,MAAM;gDAAC;;;;;;;;;;;;;;;;;;0CAMzB,8OAAC;gCAAI,WAAU;;kDAEb,8OAAC;wCACC,MAAK;wCACL,WAAU;wCACV,QAAQ;kDAER,cAAA,8OAAC,iNAAA,CAAA,YAAS;4CAAC,WAAU;;;;;;;;;;;kDAIvB,8OAAC;wCACC,MAAK;wCACL,WAAU;wCACV,QAAQ;kDAER,cAAA,8OAAC,yNAAA,CAAA,gBAAa;4CAAC,WAAU;;;;;;;;;;;kDAI3B,8OAAC;wCACC,SAAS;wCACT,UAAU,CAAC,WAAW,IAAI,MAAM;wCAChC,WAAU;kDAET,wBACC,8OAAC;4CAAI,WAAU;;;;;iEAEf,8OAAC,iOAAA,CAAA,oBAAiB;4CAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQ7C", "debugId": null}}, {"offset": {"line": 2679, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/kaliha/khalihatanbaa/src/app/messages/page.tsx"], "sourcesContent": ["\"use client\";\n\nimport { useState, useEffect } from \"react\";\nimport { useSession } from \"next-auth/react\";\nimport { useRouter, useSearchParams } from \"next/navigation\";\nimport { Header } from \"@/components/layout/Header\";\nimport { Footer } from \"@/components/layout/Footer\";\nimport { PlaceholderImage } from \"@/components/ui/PlaceholderImage\";\nimport { ExclamationCircleIcon } from \"@heroicons/react/24/outline\";\nimport { ClientOnly } from \"@/components/ClientOnly\";\nimport { useMessages } from \"@/hooks/useMessages\";\nimport {\n  ConversationList,\n  ConversationStats,\n} from \"@/components/messaging/ConversationList\";\nimport { ChatArea } from \"@/components/messaging/ChatArea\";\nimport { getTotalUnreadCount } from \"@/lib/messaging\";\n\nexport default function MessagesPage() {\n  const { data: session, status } = useSession();\n  const router = useRouter();\n  const searchParams = useSearchParams();\n\n  const [selectedConversation, setSelectedConversation] = useState<\n    string | null\n  >(null);\n\n  // استخدام الـ hook المحسن\n  const {\n    conversations,\n    messages,\n    loading,\n    sending,\n    error,\n    sendMessage: sendMessageHook,\n  } = useMessages({\n    conversationWith: selectedConversation || undefined,\n    adId: searchParams.get(\"ad\") || undefined,\n    enableRealTime: true,\n  });\n\n  // دالة إرسال الرسالة المحسنة\n  const handleSendMessage = async (content: string) => {\n    if (!selectedConversation) return false;\n\n    const adId = searchParams.get(\"ad\");\n    if (!adId) {\n      // البحث عن adId من المحادثة الحالية\n      const currentConversation = conversations.find(\n        (conv) => conv.otherUser.id === selectedConversation\n      );\n      if (!currentConversation?.adId) {\n        return false; // لا يمكن إرسال رسالة بدون adId\n      }\n      return await sendMessageHook(\n        content,\n        selectedConversation,\n        currentConversation.adId\n      );\n    }\n\n    return await sendMessageHook(content, selectedConversation, adId);\n  };\n\n  // Effects\n  useEffect(() => {\n    if (status === \"authenticated\") {\n      // التحقق من وجود محادثة محددة في URL\n      const withUser = searchParams.get(\"with\") || searchParams.get(\"user\");\n\n      if (withUser) {\n        setSelectedConversation(withUser);\n      }\n    }\n  }, [searchParams, status]);\n\n  useEffect(() => {\n    if (status === \"unauthenticated\") {\n      router.push(\"/auth/login\");\n    }\n  }, [status, router]);\n\n  // إعادة توجيه إذا لم يكن المستخدم مسجل دخول\n  if (status === \"loading\") {\n    return (\n      <div className=\"min-h-screen flex items-center justify-center\">\n        جاري التحميل...\n      </div>\n    );\n  }\n\n  if (status === \"unauthenticated\") {\n    return null;\n  }\n\n  const selectConversation = (conversation: any) => {\n    setSelectedConversation(conversation.otherUser.id);\n\n    // تحديث URL\n    const url = new URL(window.location.href);\n    url.searchParams.set(\"with\", conversation.otherUser.id);\n    if (conversation.adId) {\n      url.searchParams.set(\"ad\", conversation.adId);\n    } else {\n      url.searchParams.delete(\"ad\");\n    }\n    window.history.pushState({}, \"\", url.toString());\n  };\n\n  if (loading) {\n    return (\n      <div className=\"min-h-screen bg-background\">\n        <Header />\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8\">\n          <div className=\"animate-pulse\">\n            <div className=\"h-8 bg-gray-200 rounded w-1/4 mb-6\"></div>\n            <div className=\"grid grid-cols-1 lg:grid-cols-3 gap-6\">\n              <div className=\"h-96 bg-gray-200 rounded-lg\"></div>\n              <div className=\"lg:col-span-2 h-96 bg-gray-200 rounded-lg\"></div>\n            </div>\n          </div>\n        </div>\n        <Footer />\n      </div>\n    );\n  }\n\n  return (\n    <ClientOnly\n      fallback={\n        <div className=\"min-h-screen bg-background\">\n          <Header />\n          <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8\">\n            <div className=\"animate-pulse\">\n              <div className=\"h-8 bg-gray-200 rounded w-1/4 mb-6\"></div>\n              <div className=\"grid grid-cols-1 lg:grid-cols-3 gap-6\">\n                <div className=\"h-96 bg-gray-200 rounded-lg\"></div>\n                <div className=\"lg:col-span-2 h-96 bg-gray-200 rounded-lg\"></div>\n              </div>\n            </div>\n          </div>\n          <Footer />\n        </div>\n      }\n    >\n      <div className=\"min-h-screen bg-background\">\n        <Header />\n\n        <main className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8\">\n          <h1 className=\"text-2xl font-bold text-dark-800 mb-6\">الرسائل</h1>\n\n          {/* عرض الأخطاء */}\n          {error && (\n            <div className=\"mb-4 p-4 bg-red-50 border border-red-200 rounded-lg flex items-center space-x-2 space-x-reverse\">\n              <ExclamationCircleIcon className=\"h-5 w-5 text-red-500 flex-shrink-0\" />\n              <p className=\"text-red-700\">{error}</p>\n            </div>\n          )}\n\n          <div\n            className=\"grid grid-cols-1 lg:grid-cols-3 gap-6\"\n            style={{ height: \"calc(100vh - 200px)\" }}\n          >\n            {/* قائمة المحادثات */}\n            <div className=\"lg:col-span-1\">\n              <ConversationStats\n                totalConversations={conversations.length}\n                unreadCount={getTotalUnreadCount(conversations)}\n              />\n              <ConversationList\n                conversations={conversations}\n                selectedConversationId={selectedConversation || undefined}\n                onSelectConversation={selectConversation}\n                loading={loading}\n              />\n            </div>\n\n            {/* منطقة المحادثة */}\n            <ChatArea\n              messages={messages}\n              currentUserId={session?.user?.id || \"\"}\n              otherUser={\n                selectedConversation\n                  ? conversations.find(\n                      (c) => c.otherUser.id === selectedConversation\n                    )?.otherUser\n                  : undefined\n              }\n              ad={\n                selectedConversation\n                  ? conversations.find(\n                      (c) => c.otherUser.id === selectedConversation\n                    )?.ad\n                  : undefined\n              }\n              onSendMessage={handleSendMessage}\n              sending={sending}\n            />\n          </div>\n        </main>\n\n        <Footer />\n      </div>\n    </ClientOnly>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AAIA;AACA;AAhBA;;;;;;;;;;;;;AAkBe,SAAS;IACtB,MAAM,EAAE,MAAM,OAAO,EAAE,MAAM,EAAE,GAAG,CAAA,GAAA,8IAAA,CAAA,aAAU,AAAD;IAC3C,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,eAAe,CAAA,GAAA,kIAAA,CAAA,kBAAe,AAAD;IAEnC,MAAM,CAAC,sBAAsB,wBAAwB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAE7D;IAEF,0BAA0B;IAC1B,MAAM,EACJ,aAAa,EACb,QAAQ,EACR,OAAO,EACP,OAAO,EACP,KAAK,EACL,aAAa,eAAe,EAC7B,GAAG,CAAA,GAAA,2HAAA,CAAA,cAAW,AAAD,EAAE;QACd,kBAAkB,wBAAwB;QAC1C,MAAM,aAAa,GAAG,CAAC,SAAS;QAChC,gBAAgB;IAClB;IAEA,6BAA6B;IAC7B,MAAM,oBAAoB,OAAO;QAC/B,IAAI,CAAC,sBAAsB,OAAO;QAElC,MAAM,OAAO,aAAa,GAAG,CAAC;QAC9B,IAAI,CAAC,MAAM;YACT,oCAAoC;YACpC,MAAM,sBAAsB,cAAc,IAAI,CAC5C,CAAC,OAAS,KAAK,SAAS,CAAC,EAAE,KAAK;YAElC,IAAI,CAAC,qBAAqB,MAAM;gBAC9B,OAAO,OAAO,gCAAgC;YAChD;YACA,OAAO,MAAM,gBACX,SACA,sBACA,oBAAoB,IAAI;QAE5B;QAEA,OAAO,MAAM,gBAAgB,SAAS,sBAAsB;IAC9D;IAEA,UAAU;IACV,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,WAAW,iBAAiB;YAC9B,qCAAqC;YACrC,MAAM,WAAW,aAAa,GAAG,CAAC,WAAW,aAAa,GAAG,CAAC;YAE9D,IAAI,UAAU;gBACZ,wBAAwB;YAC1B;QACF;IACF,GAAG;QAAC;QAAc;KAAO;IAEzB,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,WAAW,mBAAmB;YAChC,OAAO,IAAI,CAAC;QACd;IACF,GAAG;QAAC;QAAQ;KAAO;IAEnB,4CAA4C;IAC5C,IAAI,WAAW,WAAW;QACxB,qBACE,8OAAC;YAAI,WAAU;sBAAgD;;;;;;IAInE;IAEA,IAAI,WAAW,mBAAmB;QAChC,OAAO;IACT;IAEA,MAAM,qBAAqB,CAAC;QAC1B,wBAAwB,aAAa,SAAS,CAAC,EAAE;QAEjD,YAAY;QACZ,MAAM,MAAM,IAAI,IAAI,OAAO,QAAQ,CAAC,IAAI;QACxC,IAAI,YAAY,CAAC,GAAG,CAAC,QAAQ,aAAa,SAAS,CAAC,EAAE;QACtD,IAAI,aAAa,IAAI,EAAE;YACrB,IAAI,YAAY,CAAC,GAAG,CAAC,MAAM,aAAa,IAAI;QAC9C,OAAO;YACL,IAAI,YAAY,CAAC,MAAM,CAAC;QAC1B;QACA,OAAO,OAAO,CAAC,SAAS,CAAC,CAAC,GAAG,IAAI,IAAI,QAAQ;IAC/C;IAEA,IAAI,SAAS;QACX,qBACE,8OAAC;YAAI,WAAU;;8BACb,8OAAC,sIAAA,CAAA,SAAM;;;;;8BACP,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;;;;;0CACf,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;;;;;kDACf,8OAAC;wCAAI,WAAU;;;;;;;;;;;;;;;;;;;;;;;8BAIrB,8OAAC,sIAAA,CAAA,SAAM;;;;;;;;;;;IAGb;IAEA,qBACE,8OAAC,gIAAA,CAAA,aAAU;QACT,wBACE,8OAAC;YAAI,WAAU;;8BACb,8OAAC,sIAAA,CAAA,SAAM;;;;;8BACP,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;;;;;0CACf,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;;;;;kDACf,8OAAC;wCAAI,WAAU;;;;;;;;;;;;;;;;;;;;;;;8BAIrB,8OAAC,sIAAA,CAAA,SAAM;;;;;;;;;;;kBAIX,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC,sIAAA,CAAA,SAAM;;;;;8BAEP,8OAAC;oBAAK,WAAU;;sCACd,8OAAC;4BAAG,WAAU;sCAAwC;;;;;;wBAGrD,uBACC,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,yOAAA,CAAA,wBAAqB;oCAAC,WAAU;;;;;;8CACjC,8OAAC;oCAAE,WAAU;8CAAgB;;;;;;;;;;;;sCAIjC,8OAAC;4BACC,WAAU;4BACV,OAAO;gCAAE,QAAQ;4BAAsB;;8CAGvC,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,mJAAA,CAAA,oBAAiB;4CAChB,oBAAoB,cAAc,MAAM;4CACxC,aAAa,CAAA,GAAA,uHAAA,CAAA,sBAAmB,AAAD,EAAE;;;;;;sDAEnC,8OAAC,mJAAA,CAAA,mBAAgB;4CACf,eAAe;4CACf,wBAAwB,wBAAwB;4CAChD,sBAAsB;4CACtB,SAAS;;;;;;;;;;;;8CAKb,8OAAC,2IAAA,CAAA,WAAQ;oCACP,UAAU;oCACV,eAAe,SAAS,MAAM,MAAM;oCACpC,WACE,uBACI,cAAc,IAAI,CAChB,CAAC,IAAM,EAAE,SAAS,CAAC,EAAE,KAAK,uBACzB,YACH;oCAEN,IACE,uBACI,cAAc,IAAI,CAChB,CAAC,IAAM,EAAE,SAAS,CAAC,EAAE,KAAK,uBACzB,KACH;oCAEN,eAAe;oCACf,SAAS;;;;;;;;;;;;;;;;;;8BAKf,8OAAC,sIAAA,CAAA,SAAM;;;;;;;;;;;;;;;;AAIf", "debugId": null}}]}