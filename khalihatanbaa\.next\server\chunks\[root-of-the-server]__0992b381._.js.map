{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 148, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/kaliha/khalihatan<PERSON>a/src/lib/prisma.ts"], "sourcesContent": ["import { PrismaClient } from '@prisma/client'\n\nconst globalForPrisma = globalThis as unknown as {\n  prisma: PrismaClient | undefined\n}\n\nexport const prisma =\n  globalForPrisma.prisma ??\n  new PrismaClient({\n    log: ['query'],\n  })\n\nif (process.env.NODE_ENV !== 'production') globalForPrisma.prisma = prisma\n"], "names": [], "mappings": ";;;AAAA;;AAEA,MAAM,kBAAkB;AAIjB,MAAM,SACX,gBAAgB,MAAM,IACtB,IAAI,6HAAA,CAAA,eAAY,CAAC;IACf,KAAK;QAAC;KAAQ;AAChB;AAEF,wCAA2C,gBAAgB,MAAM,GAAG", "debugId": null}}, {"offset": {"line": 166, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/kaliha/khalihatanbaa/src/lib/auth.ts"], "sourcesContent": ["import { NextAuthOptions } from \"next-auth\";\nimport Credentials<PERSON>rovider from \"next-auth/providers/credentials\";\nimport bcrypt from \"bcryptjs\";\nimport { prisma } from \"./prisma\";\n\nexport const authOptions: NextAuthOptions = {\n  providers: [\n    CredentialsProvider({\n      name: \"credentials\",\n      credentials: {\n        emailOrPhone: {\n          label: \"البريد الإلكتروني أو رقم الهاتف\",\n          type: \"text\",\n        },\n        password: { label: \"كلمة المرور\", type: \"password\" },\n      },\n      async authorize(credentials) {\n        if (!credentials?.emailOrPhone || !credentials?.password) {\n          return null;\n        }\n\n        // البحث عن المستخدم بالبريد الإلكتروني أو رقم الهاتف\n        const user = await prisma.user.findFirst({\n          where: {\n            OR: [\n              { email: credentials.emailOrPhone },\n              { phone: credentials.emailOrPhone },\n            ],\n            isActive: true,\n          },\n        });\n\n        if (!user) {\n          return null;\n        }\n\n        // التحقق من كلمة المرور\n        const isPasswordValid = await bcrypt.compare(\n          credentials.password,\n          user.passwordHash\n        );\n\n        if (!isPasswordValid) {\n          return null;\n        }\n\n        return {\n          id: user.id,\n          name: user.name,\n          email: user.email,\n          phone: user.phone,\n          role: user.role,\n          avatar: user.avatar || undefined,\n        };\n      },\n    }),\n  ],\n  session: {\n    strategy: \"jwt\",\n  },\n  callbacks: {\n    async jwt({ token, user }) {\n      if (user) {\n        token.role = user.role;\n        token.phone = user.phone;\n        token.avatar = user.avatar;\n      }\n      return token;\n    },\n    async session({ session, token }) {\n      if (token) {\n        session.user.id = token.sub!;\n        session.user.role = token.role as string;\n        session.user.phone = token.phone as string;\n        session.user.avatar = token.avatar as string;\n      }\n      return session;\n    },\n  },\n  pages: {\n    signIn: \"/auth/login\",\n    // signUp: \"/auth/register\", // غير مدعوم في NextAuth\n  },\n};\n"], "names": [], "mappings": ";;;AACA;AACA;AACA;;;;AAEO,MAAM,cAA+B;IAC1C,WAAW;QACT,CAAA,GAAA,0JAAA,CAAA,UAAmB,AAAD,EAAE;YAClB,MAAM;YACN,aAAa;gBACX,cAAc;oBACZ,OAAO;oBACP,MAAM;gBACR;gBACA,UAAU;oBAAE,OAAO;oBAAe,MAAM;gBAAW;YACrD;YACA,MAAM,WAAU,WAAW;gBACzB,IAAI,CAAC,aAAa,gBAAgB,CAAC,aAAa,UAAU;oBACxD,OAAO;gBACT;gBAEA,qDAAqD;gBACrD,MAAM,OAAO,MAAM,sHAAA,CAAA,SAAM,CAAC,IAAI,CAAC,SAAS,CAAC;oBACvC,OAAO;wBACL,IAAI;4BACF;gCAAE,OAAO,YAAY,YAAY;4BAAC;4BAClC;gCAAE,OAAO,YAAY,YAAY;4BAAC;yBACnC;wBACD,UAAU;oBACZ;gBACF;gBAEA,IAAI,CAAC,MAAM;oBACT,OAAO;gBACT;gBAEA,wBAAwB;gBACxB,MAAM,kBAAkB,MAAM,mIAAA,CAAA,UAAM,CAAC,OAAO,CAC1C,YAAY,QAAQ,EACpB,KAAK,YAAY;gBAGnB,IAAI,CAAC,iBAAiB;oBACpB,OAAO;gBACT;gBAEA,OAAO;oBACL,IAAI,KAAK,EAAE;oBACX,MAAM,KAAK,IAAI;oBACf,OAAO,KAAK,KAAK;oBACjB,OAAO,KAAK,KAAK;oBACjB,MAAM,KAAK,IAAI;oBACf,QAAQ,KAAK,MAAM,IAAI;gBACzB;YACF;QACF;KACD;IACD,SAAS;QACP,UAAU;IACZ;IACA,WAAW;QACT,MAAM,KAAI,EAAE,KAAK,EAAE,IAAI,EAAE;YACvB,IAAI,MAAM;gBACR,MAAM,IAAI,GAAG,KAAK,IAAI;gBACtB,MAAM,KAAK,GAAG,KAAK,KAAK;gBACxB,MAAM,MAAM,GAAG,KAAK,MAAM;YAC5B;YACA,OAAO;QACT;QACA,MAAM,SAAQ,EAAE,OAAO,EAAE,KAAK,EAAE;YAC9B,IAAI,OAAO;gBACT,QAAQ,IAAI,CAAC,EAAE,GAAG,MAAM,GAAG;gBAC3B,QAAQ,IAAI,CAAC,IAAI,GAAG,MAAM,IAAI;gBAC9B,QAAQ,IAAI,CAAC,KAAK,GAAG,MAAM,KAAK;gBAChC,QAAQ,IAAI,CAAC,MAAM,GAAG,MAAM,MAAM;YACpC;YACA,OAAO;QACT;IACF;IACA,OAAO;QACL,QAAQ;IAEV;AACF", "debugId": null}}, {"offset": {"line": 258, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/kaliha/khalihatan<PERSON><PERSON>/src/app/api/conversations/create/route.ts"], "sourcesContent": ["import { NextRequest, NextResponse } from \"next/server\";\nimport { getServerSession } from \"next-auth\";\nimport { authOptions } from \"@/lib/auth\";\nimport { prisma } from \"@/lib/prisma\";\nimport { z } from \"zod\";\n\n// مخطط التحقق من إنشاء محادثة\nconst createConversationSchema = z.object({\n  withUserId: z.string().min(1, \"معرف المستخدم مطلوب\"),\n  adId: z.string().min(1, \"معرف الإعلان مطلوب\"),\n});\n\nexport async function POST(request: NextRequest) {\n  try {\n    const session = await getServerSession(authOptions);\n    if (!session?.user?.id) {\n      return NextResponse.json(\n        { success: false, error: \"يجب تسجيل الدخول أولاً\" },\n        { status: 401 }\n      );\n    }\n\n    const body = await request.json();\n    const validatedData = createConversationSchema.parse(body);\n\n    // التحقق من عدم إنشاء محادثة مع النفس\n    if (validatedData.withUserId === session.user.id) {\n      return NextResponse.json(\n        { success: false, error: \"لا يمكن إنشاء محادثة مع نفسك\" },\n        { status: 400 }\n      );\n    }\n\n    // التحقق من وجود المستخدم المستهدف\n    const targetUser = await prisma.user.findUnique({\n      where: { id: validatedData.withUserId },\n      select: { id: true, name: true, avatar: true },\n    });\n\n    if (!targetUser) {\n      return NextResponse.json(\n        { success: false, error: \"المستخدم غير موجود\" },\n        { status: 404 }\n      );\n    }\n\n    // التحقق من وجود الإعلان\n    const ad = await prisma.ad.findUnique({\n      where: { id: validatedData.adId },\n      select: { id: true, title: true, price: true, imageUrls: true, userId: true },\n    });\n\n    if (!ad) {\n      return NextResponse.json(\n        { success: false, error: \"الإعلان غير موجود\" },\n        { status: 404 }\n      );\n    }\n\n    // التحقق من وجود محادثة سابقة\n    const existingMessage = await prisma.message.findFirst({\n      where: {\n        OR: [\n          { fromId: session.user.id, toId: validatedData.withUserId },\n          { fromId: validatedData.withUserId, toId: session.user.id },\n        ],\n        adId: validatedData.adId,\n      },\n      include: {\n        from: { select: { id: true, name: true, avatar: true } },\n        to: { select: { id: true, name: true, avatar: true } },\n        ad: { select: { id: true, title: true, price: true, imageUrls: true } },\n      },\n    });\n\n    if (existingMessage) {\n      // إذا كانت المحادثة موجودة، أرجع البيانات الموجودة\n      return NextResponse.json({\n        success: true,\n        data: {\n          conversation: existingMessage,\n          isNew: false,\n        },\n        message: \"المحادثة موجودة بالفعل\",\n      });\n    }\n\n    // إنشاء رسالة ترحيبية تلقائية\n    const welcomeMessage = `مرحباً! أنا مهتم بإعلانك \"${ad.title}\". هل يمكننا التحدث حول التفاصيل؟`;\n\n    const newMessage = await prisma.message.create({\n      data: {\n        fromId: session.user.id,\n        toId: validatedData.withUserId,\n        adId: validatedData.adId,\n        content: welcomeMessage,\n      },\n      include: {\n        from: { select: { id: true, name: true, avatar: true } },\n        to: { select: { id: true, name: true, avatar: true } },\n        ad: { select: { id: true, title: true, price: true, imageUrls: true } },\n      },\n    });\n\n    // إرسال تحديث لحظي للمستقبل\n    try {\n      const { sendMessageToUser } = await import(\"../sse/route\");\n      sendMessageToUser(validatedData.withUserId, {\n        type: \"new_message\",\n        data: newMessage,\n        timestamp: new Date().toISOString(),\n      });\n    } catch (error) {\n      console.error(\"Error sending real-time update:\", error);\n    }\n\n    return NextResponse.json({\n      success: true,\n      data: {\n        conversation: newMessage,\n        isNew: true,\n      },\n      message: \"تم إنشاء المحادثة بنجاح\",\n    });\n  } catch (error) {\n    console.error(\"Error creating conversation:\", error);\n\n    if (error instanceof z.ZodError) {\n      return NextResponse.json(\n        { success: false, error: error.errors[0].message },\n        { status: 400 }\n      );\n    }\n\n    return NextResponse.json(\n      { success: false, error: \"حدث خطأ في إنشاء المحادثة\" },\n      { status: 500 }\n    );\n  }\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;AACA;AACA;AACA;AAAA;;;;;;AAEA,8BAA8B;AAC9B,MAAM,2BAA2B,mLAAA,CAAA,IAAC,CAAC,MAAM,CAAC;IACxC,YAAY,mLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG;IAC9B,MAAM,mLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG;AAC1B;AAEO,eAAe,KAAK,OAAoB;IAC7C,IAAI;QACF,MAAM,UAAU,MAAM,CAAA,GAAA,uIAAA,CAAA,mBAAgB,AAAD,EAAE,oHAAA,CAAA,cAAW;QAClD,IAAI,CAAC,SAAS,MAAM,IAAI;YACtB,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,SAAS;gBAAO,OAAO;YAAyB,GAClD;gBAAE,QAAQ;YAAI;QAElB;QAEA,MAAM,OAAO,MAAM,QAAQ,IAAI;QAC/B,MAAM,gBAAgB,yBAAyB,KAAK,CAAC;QAErD,sCAAsC;QACtC,IAAI,cAAc,UAAU,KAAK,QAAQ,IAAI,CAAC,EAAE,EAAE;YAChD,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,SAAS;gBAAO,OAAO;YAA+B,GACxD;gBAAE,QAAQ;YAAI;QAElB;QAEA,mCAAmC;QACnC,MAAM,aAAa,MAAM,sHAAA,CAAA,SAAM,CAAC,IAAI,CAAC,UAAU,CAAC;YAC9C,OAAO;gBAAE,IAAI,cAAc,UAAU;YAAC;YACtC,QAAQ;gBAAE,IAAI;gBAAM,MAAM;gBAAM,QAAQ;YAAK;QAC/C;QAEA,IAAI,CAAC,YAAY;YACf,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,SAAS;gBAAO,OAAO;YAAqB,GAC9C;gBAAE,QAAQ;YAAI;QAElB;QAEA,yBAAyB;QACzB,MAAM,KAAK,MAAM,sHAAA,CAAA,SAAM,CAAC,EAAE,CAAC,UAAU,CAAC;YACpC,OAAO;gBAAE,IAAI,cAAc,IAAI;YAAC;YAChC,QAAQ;gBAAE,IAAI;gBAAM,OAAO;gBAAM,OAAO;gBAAM,WAAW;gBAAM,QAAQ;YAAK;QAC9E;QAEA,IAAI,CAAC,IAAI;YACP,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,SAAS;gBAAO,OAAO;YAAoB,GAC7C;gBAAE,QAAQ;YAAI;QAElB;QAEA,8BAA8B;QAC9B,MAAM,kBAAkB,MAAM,sHAAA,CAAA,SAAM,CAAC,OAAO,CAAC,SAAS,CAAC;YACrD,OAAO;gBACL,IAAI;oBACF;wBAAE,QAAQ,QAAQ,IAAI,CAAC,EAAE;wBAAE,MAAM,cAAc,UAAU;oBAAC;oBAC1D;wBAAE,QAAQ,cAAc,UAAU;wBAAE,MAAM,QAAQ,IAAI,CAAC,EAAE;oBAAC;iBAC3D;gBACD,MAAM,cAAc,IAAI;YAC1B;YACA,SAAS;gBACP,MAAM;oBAAE,QAAQ;wBAAE,IAAI;wBAAM,MAAM;wBAAM,QAAQ;oBAAK;gBAAE;gBACvD,IAAI;oBAAE,QAAQ;wBAAE,IAAI;wBAAM,MAAM;wBAAM,QAAQ;oBAAK;gBAAE;gBACrD,IAAI;oBAAE,QAAQ;wBAAE,IAAI;wBAAM,OAAO;wBAAM,OAAO;wBAAM,WAAW;oBAAK;gBAAE;YACxE;QACF;QAEA,IAAI,iBAAiB;YACnB,mDAAmD;YACnD,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;gBACvB,SAAS;gBACT,MAAM;oBACJ,cAAc;oBACd,OAAO;gBACT;gBACA,SAAS;YACX;QACF;QAEA,8BAA8B;QAC9B,MAAM,iBAAiB,CAAC,0BAA0B,EAAE,GAAG,KAAK,CAAC,iCAAiC,CAAC;QAE/F,MAAM,aAAa,MAAM,sHAAA,CAAA,SAAM,CAAC,OAAO,CAAC,MAAM,CAAC;YAC7C,MAAM;gBACJ,QAAQ,QAAQ,IAAI,CAAC,EAAE;gBACvB,MAAM,cAAc,UAAU;gBAC9B,MAAM,cAAc,IAAI;gBACxB,SAAS;YACX;YACA,SAAS;gBACP,MAAM;oBAAE,QAAQ;wBAAE,IAAI;wBAAM,MAAM;wBAAM,QAAQ;oBAAK;gBAAE;gBACvD,IAAI;oBAAE,QAAQ;wBAAE,IAAI;wBAAM,MAAM;wBAAM,QAAQ;oBAAK;gBAAE;gBACrD,IAAI;oBAAE,QAAQ;wBAAE,IAAI;wBAAM,OAAO;wBAAM,OAAO;wBAAM,WAAW;oBAAK;gBAAE;YACxE;QACF;QAEA,4BAA4B;QAC5B,IAAI;YACF,MAAM,EAAE,iBAAiB,EAAE,GAAG;;;;;YAC9B,kBAAkB,cAAc,UAAU,EAAE;gBAC1C,MAAM;gBACN,MAAM;gBACN,WAAW,IAAI,OAAO,WAAW;YACnC;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,mCAAmC;QACnD;QAEA,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YACvB,SAAS;YACT,MAAM;gBACJ,cAAc;gBACd,OAAO;YACT;YACA,SAAS;QACX;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,gCAAgC;QAE9C,IAAI,iBAAiB,mLAAA,CAAA,IAAC,CAAC,QAAQ,EAAE;YAC/B,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,SAAS;gBAAO,OAAO,MAAM,MAAM,CAAC,EAAE,CAAC,OAAO;YAAC,GACjD;gBAAE,QAAQ;YAAI;QAElB;QAEA,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YAAE,SAAS;YAAO,OAAO;QAA4B,GACrD;YAAE,QAAQ;QAAI;IAElB;AACF", "debugId": null}}]}