import { useState, useEffect, useCallback, useRef } from "react";
import { useSession } from "next-auth/react";

interface Message {
  id: string;
  content: string;
  isRead: boolean;
  createdAt: string;
  fromId: string;
  toId: string;
  adId?: string;
  from: {
    id: string;
    name: string;
    avatar?: string;
  };
  to: {
    id: string;
    name: string;
    avatar?: string;
  };
  ad?: {
    id: string;
    title: string;
    price: number;
    imageUrls: string[];
  };
}

interface Conversation {
  id: string;
  content: string;
  createdAt: string;
  adId?: string;
  otherUser: {
    id: string;
    name: string;
    avatar?: string;
  };
  ad?: {
    id: string;
    title: string;
    price: number;
    imageUrls: string[];
  };
  unreadCount: number;
  isOnline?: boolean;
}

interface UseMessagesOptions {
  conversationWith?: string;
  adId?: string;
  enableRealTime?: boolean;
  pollingInterval?: number;
}

export function useMessages(options: UseMessagesOptions = {}) {
  const { data: session } = useSession();
  const {
    conversationWith,
    adId,
    enableRealTime = true,
    pollingInterval = 30000, // 30 seconds
  } = options;

  const [conversations, setConversations] = useState<Conversation[]>([]);
  const [messages, setMessages] = useState<Message[]>([]);
  const [loading, setLoading] = useState(true);
  const [sending, setSending] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [hasMore, setHasMore] = useState(false);
  const [lastMessageId, setLastMessageId] = useState<string | null>(null);

  const eventSourceRef = useRef<EventSource | null>(null);
  const pollingIntervalRef = useRef<NodeJS.Timeout | null>(null);
  const isConnectedRef = useRef(false);

  // جلب المحادثات
  const fetchConversations = useCallback(async () => {
    if (!session?.user?.id) return;

    try {
      const response = await fetch("/api/messages");
      const data = await response.json();

      if (data.success) {
        setConversations(data.data);
      } else {
        setError(data.error || "حدث خطأ في جلب المحادثات");
      }
    } catch (error) {
      console.error("Error fetching conversations:", error);
      setError("حدث خطأ في جلب المحادثات");
    }
  }, [session?.user?.id]);

  // جلب الرسائل لمحادثة محددة
  const fetchMessages = useCallback(
    async (userId: string, adId?: string, lastMsgId?: string) => {
      if (!session?.user?.id) return;

      try {
        const url = new URL("/api/messages", window.location.origin);
        url.searchParams.set("with", userId);
        if (adId) url.searchParams.set("adId", adId);
        if (lastMsgId) url.searchParams.set("lastMessageId", lastMsgId);

        const response = await fetch(url.toString());
        const data = await response.json();

        if (data.success) {
          if (lastMsgId) {
            // إضافة الرسائل الأقدم في بداية المصفوفة (عكس الترتيب لأن البيانات تأتي مرتبة تنازلياً)
            setMessages((prev) => [...data.data.reverse(), ...prev]);
          } else {
            // التحميل الأول - عكس الترتيب لعرض الأحدث في الأسفل
            setMessages(data.data.reverse());
          }
          setHasMore(data.hasMore || false);
          setLastMessageId(data.lastMessageId);
        } else {
          setError(data.error || "حدث خطأ في جلب الرسائل");
        }
      } catch (error) {
        console.error("Error fetching messages:", error);
        setError("حدث خطأ في جلب الرسائل");
      }
    },
    [session?.user?.id]
  );

  // إرسال رسالة
  const sendMessage = useCallback(
    async (content: string, toId: string, adId?: string) => {
      if (!content.trim() || sending || !session?.user?.id) return false;

      // التحقق من وجود adId
      if (!adId) {
        setError("معرف الإعلان مطلوب لإرسال الرسالة");
        return false;
      }

      setSending(true);
      setError(null);

      // Optimistic update
      const tempId = `temp-${Date.now()}`;
      const optimisticMessage: Message = {
        id: tempId,
        content: content.trim(),
        isRead: false,
        createdAt: new Date().toISOString(),
        fromId: session.user.id,
        toId,
        adId,
        from: {
          id: session.user.id,
          name: session.user.name || "",
          avatar: session.user.avatar,
        },
        to: {
          id: toId,
          name: "مستخدم غير معروف",
          avatar: undefined,
        },
      };

      setMessages((prev) => [...prev, optimisticMessage]);

      try {
        const response = await fetch("/api/messages", {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify({
            toId,
            content: content.trim(),
            ...(adId && { adId }),
          }),
        });

        const data = await response.json();

        if (data.success) {
          // استبدال الرسالة المؤقتة بالرسالة الحقيقية
          setMessages((prev) =>
            prev.map((msg) => (msg.id === tempId ? data.data : msg))
          );

          // تحديث قائمة المحادثات
          fetchConversations();

          return true;
        } else {
          // إزالة الرسالة المؤقتة
          setMessages((prev) => prev.filter((msg) => msg.id !== tempId));
          setError(data.error || "حدث خطأ في إرسال الرسالة");
          return false;
        }
      } catch (error) {
        console.error("Error sending message:", error);
        setMessages((prev) => prev.filter((msg) => msg.id !== tempId));
        setError("حدث خطأ في إرسال الرسالة");
        return false;
      } finally {
        setSending(false);
      }
    },
    [sending, session?.user?.id, fetchConversations]
  );

  // تحميل المزيد من الرسائل (الرسائل الأقدم)
  const loadMoreMessages = useCallback(() => {
    if (conversationWith && hasMore && !loading && lastMessageId) {
      fetchMessages(conversationWith, adId, lastMessageId);
    }
  }, [conversationWith, adId, hasMore, loading, lastMessageId, fetchMessages]);

  // إعداد Server-Sent Events
  const setupSSE = useCallback(() => {
    if (!session?.user?.id || !enableRealTime || isConnectedRef.current) return;

    try {
      const eventSource = new EventSource("/api/messages/sse");
      eventSourceRef.current = eventSource;
      isConnectedRef.current = true;

      eventSource.onmessage = (event) => {
        try {
          const data = JSON.parse(event.data);

          switch (data.type) {
            case "connected":
              console.log("Connected to real-time messaging");
              break;

            case "new_message":
              const newMessage = data.data;

              // إضافة الرسالة إذا كانت للمحادثة الحالية
              if (
                conversationWith &&
                (newMessage.fromId === conversationWith ||
                  newMessage.toId === conversationWith)
              ) {
                setMessages((prev) => {
                  // تجنب الرسائل المكررة
                  if (prev.some((msg) => msg.id === newMessage.id)) {
                    return prev;
                  }
                  return [...prev, newMessage];
                });
              }

              // تحديث قائمة المحادثات
              fetchConversations();
              break;

            case "ping":
              // مجرد ping للحفاظ على الاتصال
              break;

            default:
              console.log("Unknown SSE message type:", data.type);
          }
        } catch (error) {
          console.error("Error parsing SSE message:", error);
        }
      };

      eventSource.onerror = (error) => {
        console.error("SSE connection error:", error);
        isConnectedRef.current = false;

        // إعادة المحاولة بعد 5 ثوان
        setTimeout(() => {
          if (session?.user?.id && enableRealTime) {
            setupSSE();
          }
        }, 5000);
      };
    } catch (error) {
      console.error("Error setting up SSE:", error);
      isConnectedRef.current = false;
    }
  }, [session?.user?.id, enableRealTime, conversationWith, fetchConversations]);

  // إعداد Polling كبديل احتياطي
  const setupPolling = useCallback(() => {
    if (!session?.user?.id || enableRealTime || pollingIntervalRef.current)
      return;

    pollingIntervalRef.current = setInterval(() => {
      if (conversationWith) {
        fetchMessages(conversationWith, adId);
      } else {
        fetchConversations();
      }
    }, pollingInterval);
  }, [
    session?.user?.id,
    enableRealTime,
    conversationWith,
    adId,
    pollingInterval,
    fetchMessages,
    fetchConversations,
  ]);

  // تنظيف الاتصالات
  const cleanup = useCallback(() => {
    if (eventSourceRef.current) {
      eventSourceRef.current.close();
      eventSourceRef.current = null;
      isConnectedRef.current = false;
    }

    if (pollingIntervalRef.current) {
      clearInterval(pollingIntervalRef.current);
      pollingIntervalRef.current = null;
    }
  }, []);

  // Effects
  useEffect(() => {
    if (session?.user?.id) {
      setLoading(true);

      if (conversationWith) {
        fetchMessages(conversationWith, adId).finally(() => setLoading(false));
      } else {
        fetchConversations().finally(() => setLoading(false));
      }
    }
  }, [
    session?.user?.id,
    conversationWith,
    adId,
    fetchMessages,
    fetchConversations,
  ]);

  useEffect(() => {
    if (session?.user?.id) {
      if (enableRealTime) {
        setupSSE();
      } else {
        setupPolling();
      }
    }

    return cleanup;
  }, [session?.user?.id, enableRealTime, setupSSE, setupPolling, cleanup]);

  return {
    conversations,
    messages,
    loading,
    sending,
    error,
    hasMore,
    sendMessage,
    loadMoreMessages,
    fetchConversations,
    fetchMessages,
    setError,
  };
}
