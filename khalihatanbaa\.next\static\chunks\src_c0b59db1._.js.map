{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/kaliha/khalihatanbaa/src/components/ClientOnly.tsx"], "sourcesContent": ["'use client'\n\nimport { useEffect, useState } from 'react'\n\ninterface ClientOnlyProps {\n  children: React.ReactNode\n  fallback?: React.ReactNode\n}\n\nexport function ClientOnly({ children, fallback = null }: ClientOnlyProps) {\n  const [hasMounted, setHasMounted] = useState(false)\n\n  useEffect(() => {\n    setHasMounted(true)\n  }, [])\n\n  if (!hasMounted) {\n    return <>{fallback}</>\n  }\n\n  return <>{children}</>\n}\n"], "names": [], "mappings": ";;;;AAEA;;;AAFA;;AASO,SAAS,WAAW,EAAE,QAAQ,EAAE,WAAW,IAAI,EAAmB;;IACvE,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE7C,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;gCAAE;YACR,cAAc;QAChB;+BAAG,EAAE;IAEL,IAAI,CAAC,YAAY;QACf,qBAAO;sBAAG;;IACZ;IAEA,qBAAO;kBAAG;;AACZ;GAZgB;KAAA", "debugId": null}}, {"offset": {"line": 46, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/kaliha/khalihatanbaa/src/app/messages/%5BuserId%5D/page.tsx"], "sourcesContent": ["\"use client\";\n\nimport { useState, useEffect, useRef, useCallback } from \"react\";\nimport { useSession } from \"next-auth/react\";\nimport { useRouter, useParams, useSearchParams } from \"next/navigation\";\nimport Link from \"next/link\";\nimport Image from \"next/image\";\nimport {\n  PaperAirplaneIcon,\n  ArrowLeftIcon,\n  UserIcon,\n  ExclamationCircleIcon,\n} from \"@heroicons/react/24/outline\";\nimport { ClientOnly } from \"@/components/ClientOnly\";\n\ninterface Message {\n  id: string;\n  content: string;\n  isRead: boolean;\n  createdAt: string;\n  fromId: string;\n  toId: string;\n  adId?: string;\n  from: {\n    id: string;\n    name: string;\n    avatar?: string;\n  };\n  to: {\n    id: string;\n    name: string;\n    avatar?: string;\n  };\n  ad?: {\n    id: string;\n    title: string;\n    price: number;\n    imageUrls: string[];\n  };\n}\n\nexport default function ConversationPage() {\n  const { data: session, status } = useSession();\n  const router = useRouter();\n  const params = useParams();\n  const searchParams = useSearchParams();\n\n  const userId = params.userId as string;\n  const adId = searchParams.get(\"adId\");\n\n  const [messages, setMessages] = useState<Message[]>([]);\n  const [newMessage, setNewMessage] = useState(\"\");\n  const [loading, setLoading] = useState(true);\n  const [sending, setSending] = useState(false);\n  const [error, setError] = useState(\"\");\n  const [otherUser, setOtherUser] = useState<any>(null);\n  const [ad, setAd] = useState<any>(null);\n  // Scroll-related variables removed\n  const inputRef = useRef<HTMLInputElement>(null);\n\n  useEffect(() => {\n    if (status === \"unauthenticated\") {\n      router.push(\"/auth/login\");\n      return;\n    }\n\n    if (status === \"authenticated\" && userId) {\n      fetchMessages();\n    }\n  }, [status, userId, adId, router]);\n\n  // Auto-scroll removed as requested\n\n  const fetchMessages = async () => {\n    try {\n      setLoading(true);\n      const url = new URL(\"/api/messages\", window.location.origin);\n      url.searchParams.set(\"with\", userId);\n      if (adId) url.searchParams.set(\"adId\", adId);\n\n      const response = await fetch(url.toString());\n      const data = await response.json();\n\n      if (data.success) {\n        setMessages(data.data);\n\n        // استخراج بيانات المستخدم الآخر والإعلان من أول رسالة\n        if (data.data.length > 0) {\n          const firstMessage = data.data[0];\n          setOtherUser(\n            firstMessage.fromId === session?.user?.id\n              ? firstMessage.to\n              : firstMessage.from\n          );\n          if (firstMessage.ad) {\n            setAd(firstMessage.ad);\n          }\n        }\n      } else {\n        setError(data.error || \"حدث خطأ في جلب الرسائل\");\n      }\n    } catch (error) {\n      console.error(\"Error fetching messages:\", error);\n      setError(\"حدث خطأ في الاتصال\");\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const sendMessage = useCallback(async () => {\n    if (!newMessage.trim() || sending) return;\n\n    const messageContent = newMessage.trim();\n    const tempId = `temp-${Date.now()}`;\n\n    // Optimistic update - add message immediately\n    const optimisticMessage: Message = {\n      id: tempId,\n      content: messageContent,\n      isRead: false,\n      createdAt: new Date().toISOString(),\n      fromId: session?.user?.id || \"\",\n      toId: userId,\n      adId: adId || undefined,\n      from: {\n        id: session?.user?.id || \"\",\n        name: session?.user?.name || \"\",\n        avatar: session?.user?.image || undefined,\n      },\n      to: otherUser || {\n        id: userId,\n        name: \"مستخدم غير معروف\",\n        avatar: undefined,\n      },\n      ad: ad || undefined,\n    };\n\n    // Add message immediately for instant feedback\n    setMessages((prev) => [...prev, optimisticMessage]);\n    setNewMessage(\"\");\n    setSending(true);\n    setError(\"\");\n\n    // Focus back to input\n    setTimeout(() => {\n      inputRef.current?.focus();\n    }, 100);\n\n    try {\n      const response = await fetch(\"/api/messages\", {\n        method: \"POST\",\n        headers: {\n          \"Content-Type\": \"application/json\",\n        },\n        body: JSON.stringify({\n          toId: userId,\n          content: messageContent,\n          ...(adId && { adId }),\n        }),\n      });\n\n      const data = await response.json();\n\n      if (data.success) {\n        // Replace optimistic message with real one\n        setMessages((prev) =>\n          prev.map((msg) => (msg.id === tempId ? data.data : msg))\n        );\n\n        // Save user data if first message\n        if (messages.length === 0) {\n          setOtherUser(data.data.to);\n          if (data.data.ad) {\n            setAd(data.data.ad);\n          }\n        }\n      } else {\n        // Remove optimistic message on error\n        setMessages((prev) => prev.filter((msg) => msg.id !== tempId));\n        setNewMessage(messageContent); // Restore message\n        setError(data.error || \"حدث خطأ في إرسال الرسالة\");\n      }\n    } catch (error) {\n      console.error(\"Error sending message:\", error);\n      // Remove optimistic message on error\n      setMessages((prev) => prev.filter((msg) => msg.id !== tempId));\n      setNewMessage(messageContent); // Restore message\n      setError(\"حدث خطأ في إرسال الرسالة\");\n    } finally {\n      setSending(false);\n    }\n  }, [\n    newMessage,\n    sending,\n    session?.user,\n    userId,\n    adId,\n    otherUser,\n    ad,\n    messages.length,\n  ]);\n\n  // scrollToBottom function removed\n\n  // Fixed formatTime to avoid hydration mismatch\n  const formatTime = useCallback((dateString: string) => {\n    const date = new Date(dateString);\n    return date.toLocaleTimeString(\"ar-SY\", {\n      hour: \"2-digit\",\n      minute: \"2-digit\",\n      hour12: false,\n    });\n  }, []);\n\n  if (status === \"loading\" || loading) {\n    return (\n      <div className=\"min-h-screen bg-gray-50 flex items-center justify-center\">\n        <div className=\"text-center\">\n          <div className=\"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto\"></div>\n          <p className=\"mt-4 text-gray-600\">جاري تحميل المحادثة...</p>\n        </div>\n      </div>\n    );\n  }\n\n  return (\n    <ClientOnly\n      fallback={\n        <div className=\"min-h-screen bg-gray-50 flex items-center justify-center\">\n          <div className=\"text-center\">\n            <div className=\"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto\"></div>\n            <p className=\"mt-4 text-gray-600\">جاري تحميل المحادثة...</p>\n          </div>\n        </div>\n      }\n    >\n      <div className=\"min-h-screen bg-gray-50\">\n        <div className=\"max-w-4xl mx-auto\">\n          {/* Header */}\n          <div className=\"bg-white border-b border-gray-200 px-4 py-4\">\n            <div className=\"flex items-center gap-4\">\n              <Link\n                href=\"/messages\"\n                className=\"p-2 hover:bg-gray-100 rounded-lg transition-colors\"\n              >\n                <ArrowLeftIcon className=\"h-5 w-5 text-gray-600\" />\n              </Link>\n\n              <div className=\"flex items-center gap-3 flex-1\">\n                {otherUser?.avatar ? (\n                  <Image\n                    src={otherUser.avatar}\n                    alt={otherUser.name}\n                    width={40}\n                    height={40}\n                    className=\"rounded-full object-cover\"\n                  />\n                ) : (\n                  <div className=\"w-10 h-10 bg-gray-200 rounded-full flex items-center justify-center\">\n                    <UserIcon className=\"h-5 w-5 text-gray-500\" />\n                  </div>\n                )}\n\n                <div>\n                  <h1 className=\"font-medium text-gray-900\">\n                    {otherUser?.name || \"مستخدم غير معروف\"}\n                  </h1>\n                  {ad && (\n                    <p className=\"text-sm text-gray-600\">حول: {ad.title}</p>\n                  )}\n                </div>\n              </div>\n            </div>\n          </div>\n\n          {/* Ad Info */}\n          {ad && (\n            <div className=\"bg-white border-b border-gray-200 px-4 py-3\">\n              <Link\n                href={`/ads/${ad.id}`}\n                className=\"flex items-center gap-3 p-3 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors\"\n              >\n                {ad.imageUrls?.[0] && (\n                  <Image\n                    src={ad.imageUrls[0]}\n                    alt={ad.title}\n                    width={60}\n                    height={60}\n                    className=\"rounded object-cover\"\n                  />\n                )}\n                <div className=\"flex-1\">\n                  <h3 className=\"font-medium text-gray-900\">{ad.title}</h3>\n                  <p className=\"text-lg font-bold text-blue-600\">\n                    {ad.price.toLocaleString()} ل.س\n                  </p>\n                </div>\n              </Link>\n            </div>\n          )}\n\n          {/* Error State */}\n          {error && (\n            <div className=\"bg-red-50 border border-red-200 rounded-lg p-4 mx-4 mt-4\">\n              <div className=\"flex items-center gap-2\">\n                <ExclamationCircleIcon className=\"h-5 w-5 text-red-500\" />\n                <p className=\"text-red-700\">{error}</p>\n              </div>\n            </div>\n          )}\n\n          {/* Messages */}\n          <div className=\"flex-1 overflow-y-auto p-4 space-y-3 min-h-[400px] max-h-[500px]\">\n            {messages.length === 0 ? (\n              <div className=\"text-center py-12\">\n                <div className=\"w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4\">\n                  <svg\n                    className=\"w-8 h-8 text-gray-400\"\n                    fill=\"none\"\n                    stroke=\"currentColor\"\n                    viewBox=\"0 0 24 24\"\n                  >\n                    <path\n                      strokeLinecap=\"round\"\n                      strokeLinejoin=\"round\"\n                      strokeWidth={2}\n                      d=\"M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z\"\n                    />\n                  </svg>\n                </div>\n                <p className=\"text-gray-600 font-medium\">\n                  لا توجد رسائل حتى الآن\n                </p>\n                <p className=\"text-sm text-gray-500 mt-1\">\n                  ابدأ المحادثة بإرسال رسالة\n                </p>\n              </div>\n            ) : (\n              messages.map((message) => {\n                const isOwn = message.fromId === session?.user?.id;\n                const isTemp = message.id.startsWith(\"temp-\");\n\n                return (\n                  <div\n                    key={message.id}\n                    className={`flex ${\n                      isOwn ? \"justify-end\" : \"justify-start\"\n                    }`}\n                  >\n                    <div\n                      className={`max-w-xs lg:max-w-md px-4 py-3 rounded-2xl shadow-sm ${\n                        isOwn\n                          ? `bg-blue-600 text-white ${\n                              isTemp ? \"opacity-70\" : \"\"\n                            }`\n                          : \"bg-white text-gray-900 border border-gray-200\"\n                      } ${isTemp ? \"animate-pulse\" : \"\"}`}\n                    >\n                      <p className=\"text-sm leading-relaxed break-words\">\n                        {message.content}\n                      </p>\n                      <div\n                        className={`flex items-center justify-between mt-2 ${\n                          isOwn ? \"text-blue-100\" : \"text-gray-500\"\n                        }`}\n                      >\n                        <p className=\"text-xs\">\n                          {formatTime(message.createdAt)}\n                        </p>\n                        {isOwn && (\n                          <div className=\"flex items-center gap-1\">\n                            {isTemp ? (\n                              <div className=\"w-3 h-3 border border-current border-t-transparent rounded-full animate-spin\"></div>\n                            ) : (\n                              <svg\n                                className=\"w-3 h-3\"\n                                fill=\"currentColor\"\n                                viewBox=\"0 0 20 20\"\n                              >\n                                <path\n                                  fillRule=\"evenodd\"\n                                  d=\"M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z\"\n                                  clipRule=\"evenodd\"\n                                />\n                              </svg>\n                            )}\n                          </div>\n                        )}\n                      </div>\n                    </div>\n                  </div>\n                );\n              })\n            )}\n            {/* Scroll reference removed */}\n          </div>\n\n          {/* Send Message */}\n          <div className=\"bg-white border-t border-gray-200 p-4 sticky bottom-0\">\n            {error && (\n              <div className=\"mb-3 p-2 bg-red-50 border border-red-200 rounded-lg\">\n                <p className=\"text-red-700 text-sm\">{error}</p>\n              </div>\n            )}\n\n            <div className=\"flex gap-3 items-end\">\n              <div className=\"flex-1\">\n                <input\n                  ref={inputRef}\n                  type=\"text\"\n                  value={newMessage}\n                  onChange={(e) => {\n                    setNewMessage(e.target.value);\n                    setError(\"\"); // Clear error when typing\n                  }}\n                  onKeyDown={(e) => {\n                    if (e.key === \"Enter\" && !e.shiftKey) {\n                      e.preventDefault();\n                      sendMessage();\n                    }\n                  }}\n                  placeholder=\"اكتب رسالتك...\"\n                  className=\"w-full px-4 py-3 border border-gray-300 rounded-2xl focus:ring-2 focus:ring-blue-500 focus:border-transparent resize-none transition-all duration-200 placeholder-gray-500\"\n                  disabled={sending}\n                  maxLength={1000}\n                />\n                {newMessage.length > 800 && (\n                  <p className=\"text-xs text-gray-500 mt-1 text-right\">\n                    {1000 - newMessage.length} حرف متبقي\n                  </p>\n                )}\n              </div>\n\n              <button\n                onClick={sendMessage}\n                disabled={!newMessage.trim() || sending}\n                className=\"p-3 bg-blue-600 text-white rounded-2xl hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200 transform hover:scale-105 active:scale-95\"\n                title=\"إرسال الرسالة\"\n              >\n                {sending ? (\n                  <div className=\"animate-spin rounded-full h-5 w-5 border-2 border-white border-t-transparent\"></div>\n                ) : (\n                  <PaperAirplaneIcon className=\"h-5 w-5 transform rotate-180\" />\n                )}\n              </button>\n            </div>\n          </div>\n        </div>\n      </div>\n    </ClientOnly>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAMA;;;AAbA;;;;;;;;AAyCe,SAAS;;IACtB,MAAM,EAAE,MAAM,OAAO,EAAE,MAAM,EAAE,GAAG,CAAA,GAAA,iJAAA,CAAA,aAAU,AAAD;IAC3C,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,eAAe,CAAA,GAAA,qIAAA,CAAA,kBAAe,AAAD;IAEnC,MAAM,SAAS,OAAO,MAAM;IAC5B,MAAM,OAAO,aAAa,GAAG,CAAC;IAE9B,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAa,EAAE;IACtD,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACnC,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAO;IAChD,MAAM,CAAC,IAAI,MAAM,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAO;IAClC,mCAAmC;IACnC,MAAM,WAAW,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAoB;IAE1C,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;sCAAE;YACR,IAAI,WAAW,mBAAmB;gBAChC,OAAO,IAAI,CAAC;gBACZ;YACF;YAEA,IAAI,WAAW,mBAAmB,QAAQ;gBACxC;YACF;QACF;qCAAG;QAAC;QAAQ;QAAQ;QAAM;KAAO;IAEjC,mCAAmC;IAEnC,MAAM,gBAAgB;QACpB,IAAI;YACF,WAAW;YACX,MAAM,MAAM,IAAI,IAAI,iBAAiB,OAAO,QAAQ,CAAC,MAAM;YAC3D,IAAI,YAAY,CAAC,GAAG,CAAC,QAAQ;YAC7B,IAAI,MAAM,IAAI,YAAY,CAAC,GAAG,CAAC,QAAQ;YAEvC,MAAM,WAAW,MAAM,MAAM,IAAI,QAAQ;YACzC,MAAM,OAAO,MAAM,SAAS,IAAI;YAEhC,IAAI,KAAK,OAAO,EAAE;gBAChB,YAAY,KAAK,IAAI;gBAErB,sDAAsD;gBACtD,IAAI,KAAK,IAAI,CAAC,MAAM,GAAG,GAAG;oBACxB,MAAM,eAAe,KAAK,IAAI,CAAC,EAAE;oBACjC,aACE,aAAa,MAAM,KAAK,SAAS,MAAM,KACnC,aAAa,EAAE,GACf,aAAa,IAAI;oBAEvB,IAAI,aAAa,EAAE,EAAE;wBACnB,MAAM,aAAa,EAAE;oBACvB;gBACF;YACF,OAAO;gBACL,SAAS,KAAK,KAAK,IAAI;YACzB;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,4BAA4B;YAC1C,SAAS;QACX,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,cAAc,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;qDAAE;YAC9B,IAAI,CAAC,WAAW,IAAI,MAAM,SAAS;YAEnC,MAAM,iBAAiB,WAAW,IAAI;YACtC,MAAM,SAAS,CAAC,KAAK,EAAE,KAAK,GAAG,IAAI;YAEnC,8CAA8C;YAC9C,MAAM,oBAA6B;gBACjC,IAAI;gBACJ,SAAS;gBACT,QAAQ;gBACR,WAAW,IAAI,OAAO,WAAW;gBACjC,QAAQ,SAAS,MAAM,MAAM;gBAC7B,MAAM;gBACN,MAAM,QAAQ;gBACd,MAAM;oBACJ,IAAI,SAAS,MAAM,MAAM;oBACzB,MAAM,SAAS,MAAM,QAAQ;oBAC7B,QAAQ,SAAS,MAAM,SAAS;gBAClC;gBACA,IAAI,aAAa;oBACf,IAAI;oBACJ,MAAM;oBACN,QAAQ;gBACV;gBACA,IAAI,MAAM;YACZ;YAEA,+CAA+C;YAC/C;6DAAY,CAAC,OAAS;2BAAI;wBAAM;qBAAkB;;YAClD,cAAc;YACd,WAAW;YACX,SAAS;YAET,sBAAsB;YACtB;6DAAW;oBACT,SAAS,OAAO,EAAE;gBACpB;4DAAG;YAEH,IAAI;gBACF,MAAM,WAAW,MAAM,MAAM,iBAAiB;oBAC5C,QAAQ;oBACR,SAAS;wBACP,gBAAgB;oBAClB;oBACA,MAAM,KAAK,SAAS,CAAC;wBACnB,MAAM;wBACN,SAAS;wBACT,GAAI,QAAQ;4BAAE;wBAAK,CAAC;oBACtB;gBACF;gBAEA,MAAM,OAAO,MAAM,SAAS,IAAI;gBAEhC,IAAI,KAAK,OAAO,EAAE;oBAChB,2CAA2C;oBAC3C;qEAAY,CAAC,OACX,KAAK,GAAG;6EAAC,CAAC,MAAS,IAAI,EAAE,KAAK,SAAS,KAAK,IAAI,GAAG;;;oBAGrD,kCAAkC;oBAClC,IAAI,SAAS,MAAM,KAAK,GAAG;wBACzB,aAAa,KAAK,IAAI,CAAC,EAAE;wBACzB,IAAI,KAAK,IAAI,CAAC,EAAE,EAAE;4BAChB,MAAM,KAAK,IAAI,CAAC,EAAE;wBACpB;oBACF;gBACF,OAAO;oBACL,qCAAqC;oBACrC;qEAAY,CAAC,OAAS,KAAK,MAAM;6EAAC,CAAC,MAAQ,IAAI,EAAE,KAAK;;;oBACtD,cAAc,iBAAiB,kBAAkB;oBACjD,SAAS,KAAK,KAAK,IAAI;gBACzB;YACF,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,0BAA0B;gBACxC,qCAAqC;gBACrC;iEAAY,CAAC,OAAS,KAAK,MAAM;yEAAC,CAAC,MAAQ,IAAI,EAAE,KAAK;;;gBACtD,cAAc,iBAAiB,kBAAkB;gBACjD,SAAS;YACX,SAAU;gBACR,WAAW;YACb;QACF;oDAAG;QACD;QACA;QACA,SAAS;QACT;QACA;QACA;QACA;QACA,SAAS,MAAM;KAChB;IAED,kCAAkC;IAElC,+CAA+C;IAC/C,MAAM,aAAa,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;oDAAE,CAAC;YAC9B,MAAM,OAAO,IAAI,KAAK;YACtB,OAAO,KAAK,kBAAkB,CAAC,SAAS;gBACtC,MAAM;gBACN,QAAQ;gBACR,QAAQ;YACV;QACF;mDAAG,EAAE;IAEL,IAAI,WAAW,aAAa,SAAS;QACnC,qBACE,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;;;;;kCACf,6LAAC;wBAAE,WAAU;kCAAqB;;;;;;;;;;;;;;;;;IAI1C;IAEA,qBACE,6LAAC,mIAAA,CAAA,aAAU;QACT,wBACE,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;;;;;kCACf,6LAAC;wBAAE,WAAU;kCAAqB;;;;;;;;;;;;;;;;;kBAKxC,cAAA,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC;gBAAI,WAAU;;kCAEb,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,+JAAA,CAAA,UAAI;oCACH,MAAK;oCACL,WAAU;8CAEV,cAAA,6LAAC,4NAAA,CAAA,gBAAa;wCAAC,WAAU;;;;;;;;;;;8CAG3B,6LAAC;oCAAI,WAAU;;wCACZ,WAAW,uBACV,6LAAC,gIAAA,CAAA,UAAK;4CACJ,KAAK,UAAU,MAAM;4CACrB,KAAK,UAAU,IAAI;4CACnB,OAAO;4CACP,QAAQ;4CACR,WAAU;;;;;iEAGZ,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC,kNAAA,CAAA,WAAQ;gDAAC,WAAU;;;;;;;;;;;sDAIxB,6LAAC;;8DACC,6LAAC;oDAAG,WAAU;8DACX,WAAW,QAAQ;;;;;;gDAErB,oBACC,6LAAC;oDAAE,WAAU;;wDAAwB;wDAAM,GAAG,KAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;oBAQ5D,oBACC,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC,+JAAA,CAAA,UAAI;4BACH,MAAM,CAAC,KAAK,EAAE,GAAG,EAAE,EAAE;4BACrB,WAAU;;gCAET,GAAG,SAAS,EAAE,CAAC,EAAE,kBAChB,6LAAC,gIAAA,CAAA,UAAK;oCACJ,KAAK,GAAG,SAAS,CAAC,EAAE;oCACpB,KAAK,GAAG,KAAK;oCACb,OAAO;oCACP,QAAQ;oCACR,WAAU;;;;;;8CAGd,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAG,WAAU;sDAA6B,GAAG,KAAK;;;;;;sDACnD,6LAAC;4CAAE,WAAU;;gDACV,GAAG,KAAK,CAAC,cAAc;gDAAG;;;;;;;;;;;;;;;;;;;;;;;;oBAQpC,uBACC,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,4OAAA,CAAA,wBAAqB;oCAAC,WAAU;;;;;;8CACjC,6LAAC;oCAAE,WAAU;8CAAgB;;;;;;;;;;;;;;;;;kCAMnC,6LAAC;wBAAI,WAAU;kCACZ,SAAS,MAAM,KAAK,kBACnB,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC;wCACC,WAAU;wCACV,MAAK;wCACL,QAAO;wCACP,SAAQ;kDAER,cAAA,6LAAC;4CACC,eAAc;4CACd,gBAAe;4CACf,aAAa;4CACb,GAAE;;;;;;;;;;;;;;;;8CAIR,6LAAC;oCAAE,WAAU;8CAA4B;;;;;;8CAGzC,6LAAC;oCAAE,WAAU;8CAA6B;;;;;;;;;;;mCAK5C,SAAS,GAAG,CAAC,CAAC;4BACZ,MAAM,QAAQ,QAAQ,MAAM,KAAK,SAAS,MAAM;4BAChD,MAAM,SAAS,QAAQ,EAAE,CAAC,UAAU,CAAC;4BAErC,qBACE,6LAAC;gCAEC,WAAW,CAAC,KAAK,EACf,QAAQ,gBAAgB,iBACxB;0CAEF,cAAA,6LAAC;oCACC,WAAW,CAAC,qDAAqD,EAC/D,QACI,CAAC,uBAAuB,EACtB,SAAS,eAAe,IACxB,GACF,gDACL,CAAC,EAAE,SAAS,kBAAkB,IAAI;;sDAEnC,6LAAC;4CAAE,WAAU;sDACV,QAAQ,OAAO;;;;;;sDAElB,6LAAC;4CACC,WAAW,CAAC,uCAAuC,EACjD,QAAQ,kBAAkB,iBAC1B;;8DAEF,6LAAC;oDAAE,WAAU;8DACV,WAAW,QAAQ,SAAS;;;;;;gDAE9B,uBACC,6LAAC;oDAAI,WAAU;8DACZ,uBACC,6LAAC;wDAAI,WAAU;;;;;6EAEf,6LAAC;wDACC,WAAU;wDACV,MAAK;wDACL,SAAQ;kEAER,cAAA,6LAAC;4DACC,UAAS;4DACT,GAAE;4DACF,UAAS;;;;;;;;;;;;;;;;;;;;;;;;;;;;+BAtClB,QAAQ,EAAE;;;;;wBAgDrB;;;;;;kCAMJ,6LAAC;wBAAI,WAAU;;4BACZ,uBACC,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAE,WAAU;8CAAwB;;;;;;;;;;;0CAIzC,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDACC,KAAK;gDACL,MAAK;gDACL,OAAO;gDACP,UAAU,CAAC;oDACT,cAAc,EAAE,MAAM,CAAC,KAAK;oDAC5B,SAAS,KAAK,0BAA0B;gDAC1C;gDACA,WAAW,CAAC;oDACV,IAAI,EAAE,GAAG,KAAK,WAAW,CAAC,EAAE,QAAQ,EAAE;wDACpC,EAAE,cAAc;wDAChB;oDACF;gDACF;gDACA,aAAY;gDACZ,WAAU;gDACV,UAAU;gDACV,WAAW;;;;;;4CAEZ,WAAW,MAAM,GAAG,qBACnB,6LAAC;gDAAE,WAAU;;oDACV,OAAO,WAAW,MAAM;oDAAC;;;;;;;;;;;;;kDAKhC,6LAAC;wCACC,SAAS;wCACT,UAAU,CAAC,WAAW,IAAI,MAAM;wCAChC,WAAU;wCACV,OAAM;kDAEL,wBACC,6LAAC;4CAAI,WAAU;;;;;iEAEf,6LAAC,oOAAA,CAAA,oBAAiB;4CAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAS/C;GA1ZwB;;QACY,iJAAA,CAAA,aAAU;QAC7B,qIAAA,CAAA,YAAS;QACT,qIAAA,CAAA,YAAS;QACH,qIAAA,CAAA,kBAAe;;;KAJd", "debugId": null}}]}