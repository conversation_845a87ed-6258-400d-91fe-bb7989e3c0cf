import { CheckIcon, ClockIcon } from "@heroicons/react/24/outline";
import { formatMessageTime, isTempMessage } from "@/lib/messaging";

interface Message {
  id: string;
  content: string;
  isRead: boolean;
  createdAt: string;
  fromId: string;
  toId: string;
  from: {
    id: string;
    name: string;
    avatar?: string;
  };
  to: {
    id: string;
    name: string;
    avatar?: string;
  };
}

interface MessageBubbleProps {
  message: Message;
  isOwn: boolean;
  showAvatar?: boolean;
  isLastInGroup?: boolean;
}

export function MessageBubble({
  message,
  isOwn,
  showAvatar = false,
  isLastInGroup = true,
}: MessageBubbleProps) {
  const isTemp = isTempMessage(message.id);

  return (
    <div className={`flex ${isOwn ? "justify-end" : "justify-start"} mb-2`}>
      <div className="flex items-end space-x-2 space-x-reverse max-w-xs lg:max-w-md">
        {/* Avatar للرسائل الواردة */}
        {!isOwn && showAvatar && (
          <div className="w-6 h-6 rounded-full bg-primary-100 flex items-center justify-center flex-shrink-0">
            {message.from.avatar ? (
              <img
                src={message.from.avatar}
                alt={message.from.name}
                className="w-full h-full rounded-full object-cover"
              />
            ) : (
              <span className="text-xs font-medium text-primary-600">
                {message.from.name.charAt(0)}
              </span>
            )}
          </div>
        )}

        {/* فقاعة الرسالة */}
        <div
          className={`px-4 py-2 rounded-2xl shadow-sm relative ${
            isOwn
              ? `bg-primary-500 text-white ${isTemp ? "opacity-70" : ""}`
              : "bg-white text-gray-900 border border-gray-200"
          } ${isTemp ? "animate-pulse" : ""} ${
            isLastInGroup ? (isOwn ? "rounded-br-md" : "rounded-bl-md") : ""
          }`}
        >
          {/* محتوى الرسالة */}
          <p className="text-sm leading-relaxed break-words whitespace-pre-wrap">
            {message.content}
          </p>

          {/* معلومات الرسالة */}
          <div
            className={`flex items-center justify-between mt-2 space-x-2 space-x-reverse ${
              isOwn ? "text-primary-100" : "text-gray-500"
            }`}
          >
            <span className="text-xs">
              {formatMessageTime(message.createdAt)}
            </span>

            {/* حالة الرسالة للرسائل المرسلة */}
            {isOwn && (
              <div className="flex items-center">
                {isTemp ? (
                  <ClockIcon className="w-3 h-3" />
                ) : (
                  <div className="flex">
                    <CheckIcon className="w-3 h-3" />
                    {message.isRead && <CheckIcon className="w-3 h-3 -mr-1" />}
                  </div>
                )}
              </div>
            )}
          </div>

          {/* مؤشر التحميل للرسائل المؤقتة */}
          {isTemp && (
            <div className="absolute -bottom-1 -right-1">
              <div className="w-2 h-2 bg-current rounded-full animate-ping"></div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}

// مكون لتجميع الرسائل حسب المرسل
interface MessageGroupProps {
  messages: Message[];
  currentUserId: string;
}

export function MessageGroup({ messages, currentUserId }: MessageGroupProps) {
  if (messages.length === 0) return null;

  const firstMessage = messages[0];
  const isOwn = firstMessage.fromId === currentUserId;

  return (
    <div
      className={`flex flex-col space-y-1 ${
        isOwn ? "items-end" : "items-start"
      }`}
    >
      {messages.map((message, index) => (
        <MessageBubble
          key={`${message.id}-${index}-${message.createdAt}`}
          message={message}
          isOwn={isOwn}
          showAvatar={!isOwn && index === messages.length - 1}
          isLastInGroup={index === messages.length - 1}
        />
      ))}
    </div>
  );
}

// دالة لتجميع الرسائل المتتالية من نفس المرسل
export function groupConsecutiveMessages(messages: Message[]): Message[][] {
  if (messages.length === 0) return [];

  const groups: Message[][] = [];
  let currentGroup: Message[] = [messages[0]];

  for (let i = 1; i < messages.length; i++) {
    const currentMessage = messages[i];
    const previousMessage = messages[i - 1];

    // تحقق من كون الرسالة من نفس المرسل والوقت قريب (أقل من 5 دقائق)
    const timeDiff =
      new Date(currentMessage.createdAt).getTime() -
      new Date(previousMessage.createdAt).getTime();
    const isSameSender = currentMessage.fromId === previousMessage.fromId;
    const isWithinTimeLimit = timeDiff < 5 * 60 * 1000; // 5 دقائق

    if (isSameSender && isWithinTimeLimit) {
      currentGroup.push(currentMessage);
    } else {
      groups.push(currentGroup);
      currentGroup = [currentMessage];
    }
  }

  groups.push(currentGroup);
  return groups;
}

// مكون لعرض فاصل التاريخ
interface DateSeparatorProps {
  date: string;
}

export function DateSeparator({ date }: DateSeparatorProps) {
  return (
    <div className="flex items-center justify-center my-4">
      <div className="bg-gray-100 text-gray-600 text-xs px-3 py-1 rounded-full">
        {date}
      </div>
    </div>
  );
}

// مكون لعرض مؤشر الكتابة
interface TypingIndicatorProps {
  userName: string;
}

export function TypingIndicator({ userName }: TypingIndicatorProps) {
  return (
    <div className="flex items-start space-x-2 space-x-reverse mb-2">
      <div className="w-6 h-6 rounded-full bg-primary-100 flex items-center justify-center flex-shrink-0">
        <span className="text-xs font-medium text-primary-600">
          {userName.charAt(0)}
        </span>
      </div>

      <div className="bg-white border border-gray-200 rounded-2xl rounded-bl-md px-4 py-2 shadow-sm">
        <div className="flex space-x-1">
          <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce"></div>
          <div
            className="w-2 h-2 bg-gray-400 rounded-full animate-bounce"
            style={{ animationDelay: "0.1s" }}
          ></div>
          <div
            className="w-2 h-2 bg-gray-400 rounded-full animate-bounce"
            style={{ animationDelay: "0.2s" }}
          ></div>
        </div>
      </div>
    </div>
  );
}
