import { NextRequest, NextResponse } from "next/server";
import { prisma } from "@/lib/prisma";

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const page = parseInt(searchParams.get("page") || "1");
    const limit = parseInt(searchParams.get("limit") || "12");
    const category = searchParams.get("category");
    const city = searchParams.get("city");
    const condition = searchParams.get("condition");
    const minPrice = searchParams.get("minPrice");
    const maxPrice = searchParams.get("maxPrice");
    const search = searchParams.get("search");
    const sortBy = searchParams.get("sortBy") || "newest";
    const featured = searchParams.get("featured") === "true";
    const skip = (page - 1) * limit;

    // بناء شروط البحث
    const where: any = {
      isActive: true,
      OR: [{ expiresAt: null }, { expiresAt: { gte: new Date() } }],
    };

    // فلترة حسب الفئة
    if (category) {
      where.category = category;
    }

    // فلترة حسب المدينة
    if (city) {
      where.city = city;
    }

    // فلترة حسب الحالة
    if (condition) {
      where.condition = condition;
    }

    // فلترة الإعلانات المميزة
    if (featured) {
      where.isPromoted = true;
    }

    // فلترة حسب السعر
    if (minPrice || maxPrice) {
      where.price = {};
      if (minPrice) where.price.gte = parseFloat(minPrice);
      if (maxPrice) where.price.lte = parseFloat(maxPrice);
    }

    // البحث في العنوان والوصف
    if (search) {
      where.AND = [
        where.AND || {},
        {
          OR: [
            { title: { contains: search, mode: "insensitive" } },
            { description: { contains: search, mode: "insensitive" } },
          ],
        },
      ];
    }

    // ترتيب النتائج
    let orderBy: any = [];

    switch (sortBy) {
      case "newest":
        orderBy = [{ isPromoted: "desc" }, { createdAt: "desc" }];
        break;
      case "oldest":
        orderBy = [{ isPromoted: "desc" }, { createdAt: "asc" }];
        break;
      case "price_low":
        orderBy = [{ isPromoted: "desc" }, { price: "asc" }];
        break;
      case "price_high":
        orderBy = [{ isPromoted: "desc" }, { price: "desc" }];
        break;
      case "most_viewed":
        orderBy = [{ isPromoted: "desc" }, { views: "desc" }];
        break;
      default:
        orderBy = [{ isPromoted: "desc" }, { createdAt: "desc" }];
    }

    // جلب الإعلانات
    const [ads, total] = await Promise.all([
      prisma.ad.findMany({
        where,
        include: {
          user: {
            select: {
              id: true,
              name: true,
              avatar: true,
              ratingAverage: true,
              ratingCount: true,
            },
          },
        },
        orderBy,
        skip,
        take: limit,
      }),
      prisma.ad.count({ where }),
    ]);

    const totalPages = Math.ceil(total / limit);

    return NextResponse.json({
      success: true,
      data: ads,
      pagination: {
        page,
        limit,
        total,
        totalPages,
        hasNext: page < totalPages,
        hasPrev: page > 1,
      },
    });
  } catch (error) {
    console.error("Error fetching ads:", error);
    return NextResponse.json(
      { success: false, error: "حدث خطأ في جلب الإعلانات" },
      { status: 500 }
    );
  }
}
