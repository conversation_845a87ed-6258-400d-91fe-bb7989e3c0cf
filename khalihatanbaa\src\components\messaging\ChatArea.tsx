import { useState, useRef, useEffect } from "react";
import {
  PaperAirplaneIcon,
  UserIcon,
  PhotoIcon,
  FaceSmileIcon,
  ArrowUpIcon,
} from "@heroicons/react/24/outline";
import {
  MessageGroup,
  groupConsecutiveMessages,
  DateSeparator,
  TypingIndicator,
} from "./MessageBubble";
import { groupMessagesByDate, validateMessage } from "@/lib/messaging";

interface Message {
  id: string;
  content: string;
  isRead: boolean;
  createdAt: string;
  fromId: string;
  toId: string;
  from: {
    id: string;
    name: string;
    avatar?: string;
  };
  to: {
    id: string;
    name: string;
    avatar?: string;
  };
}

interface ChatAreaProps {
  messages: Message[];
  currentUserId: string;
  otherUser?: {
    id: string;
    name: string;
    avatar?: string;
  };
  ad?: {
    id: string;
    title: string;
    price: number;
    imageUrls: string[];
  };
  onSendMessage: (content: string) => Promise<boolean>;
  sending: boolean;
  hasMore?: boolean;
  onLoadMore?: () => void;
  loading?: boolean;
}

export function ChatArea({
  messages,
  currentUserId,
  otherUser,
  ad,
  onSendMessage,
  sending,
  hasMore = false,
  onLoadMore,
  loading = false,
}: ChatAreaProps) {
  const [newMessage, setNewMessage] = useState("");
  const [error, setError] = useState<string | null>(null);
  const [showScrollButton, setShowScrollButton] = useState(false);
  const [isTyping, setIsTyping] = useState(false);

  const messagesContainerRef = useRef<HTMLDivElement>(null);
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const inputRef = useRef<HTMLInputElement>(null);

  // تجميع الرسائل حسب التاريخ
  const messagesByDate = groupMessagesByDate(messages);

  // التمرير إلى أسفل
  const scrollToBottom = (smooth = true) => {
    messagesEndRef.current?.scrollIntoView({
      behavior: smooth ? "smooth" : "auto",
    });
  };

  // مراقبة التمرير
  const handleScroll = () => {
    if (!messagesContainerRef.current) return;

    const { scrollTop, scrollHeight, clientHeight } =
      messagesContainerRef.current;
    const isNearBottom = scrollHeight - scrollTop - clientHeight < 100;

    setShowScrollButton(!isNearBottom);

    // تحميل المزيد من الرسائل عند الوصول للأعلى (مع تحسين الأداء)
    if (scrollTop < 100 && hasMore && onLoadMore && !loading) {
      const currentScrollHeight = scrollHeight;
      onLoadMore();

      // الحفاظ على موضع التمرير بعد تحميل الرسائل الجديدة
      setTimeout(() => {
        if (messagesContainerRef.current) {
          const newScrollHeight = messagesContainerRef.current.scrollHeight;
          const scrollDiff = newScrollHeight - currentScrollHeight;
          messagesContainerRef.current.scrollTop = scrollTop + scrollDiff;
        }
      }, 100);
    }
  };

  // إرسال الرسالة
  const handleSendMessage = async () => {
    const validation = validateMessage(newMessage);
    if (!validation.isValid) {
      setError(validation.error || "رسالة غير صحيحة");
      return;
    }

    setError(null);
    const success = await onSendMessage(newMessage.trim());

    if (success) {
      setNewMessage("");
      // التركيز على حقل الإدخال
      setTimeout(() => {
        inputRef.current?.focus();
      }, 100);
    }
  };

  // معالجة الضغط على Enter
  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === "Enter" && !e.shiftKey) {
      e.preventDefault();
      handleSendMessage();
    }
  };

  // التمرير التلقائي للرسائل الجديدة
  useEffect(() => {
    if (messages.length > 0) {
      const lastMessage = messages[messages.length - 1];
      const messageTime = new Date(lastMessage.createdAt).getTime();
      const now = Date.now();
      const isRecent = now - messageTime < 10000; // 10 ثوان

      // التمرير التلقائي في الحالات التالية:
      // 1. إذا كانت الرسالة من المستخدم الحالي
      // 2. إذا كانت رسالة حديثة (آخر 10 ثوان)
      if (lastMessage.fromId === currentUserId || isRecent) {
        setTimeout(() => scrollToBottom(false), 100);
      }
    }
  }, [messages, currentUserId]);

  // التركيز على حقل الإدخال عند تحديد محادثة والتمرير للأسفل
  useEffect(() => {
    if (otherUser && messages.length > 0) {
      inputRef.current?.focus();
      // التمرير للأسفل عند تحميل محادثة جديدة
      setTimeout(() => {
        scrollToBottom(false); // بدون animation للسرعة
      }, 200);
    }
  }, [otherUser, messages.length]);

  if (!otherUser) {
    return (
      <div
        className="lg:col-span-2 bg-white rounded-lg shadow-sm border border-gray-200 flex items-center justify-center"
        style={{ height: "calc(100vh - 250px)" }}
      >
        <div className="text-center text-gray-500">
          <UserIcon className="h-16 w-16 mx-auto mb-4 text-gray-300" />
          <p className="text-lg font-medium">اختر محادثة لبدء المراسلة</p>
          <p className="text-sm mt-1">اختر محادثة من القائمة الجانبية</p>
        </div>
      </div>
    );
  }

  return (
    <div
      className="lg:col-span-2 bg-white rounded-lg shadow-sm border border-gray-200 flex flex-col"
      style={{ height: "calc(100vh - 250px)" }}
    >
      {/* رأس المحادثة */}
      <div className="p-4 border-b border-gray-200 bg-gray-50">
        <div className="flex items-center space-x-3 space-x-reverse">
          <div className="relative w-10 h-10 bg-primary-100 rounded-full flex items-center justify-center">
            {otherUser.avatar ? (
              <img
                src={otherUser.avatar}
                alt={otherUser.name}
                className="w-full h-full rounded-full object-cover"
              />
            ) : (
              <UserIcon className="h-5 w-5 text-primary-600" />
            )}
          </div>
          <div className="flex-1">
            <p className="font-medium text-dark-800">{otherUser.name}</p>
            {ad && <p className="text-sm text-gray-500">حول: {ad.title}</p>}
          </div>
        </div>
      </div>

      {/* منطقة الرسائل */}
      <div
        ref={messagesContainerRef}
        onScroll={handleScroll}
        className="flex-1 overflow-y-auto p-4 space-y-4 relative"
      >
        {/* مؤشر التحميل للرسائل القديمة */}
        {loading && (
          <div className="flex justify-center py-4">
            <div className="w-6 h-6 border-2 border-primary-500 border-t-transparent rounded-full animate-spin"></div>
          </div>
        )}

        {/* عرض الرسائل مجمعة حسب التاريخ */}
        {Object.entries(messagesByDate).map(([date, dateMessages]) => {
          const messageGroups = groupConsecutiveMessages(dateMessages);

          return (
            <div key={date}>
              <DateSeparator date={date} />
              <div className="space-y-4">
                {messageGroups.map((group, groupIndex) => (
                  <MessageGroup
                    key={`${date}-${groupIndex}`}
                    messages={group}
                    currentUserId={currentUserId}
                  />
                ))}
              </div>
            </div>
          );
        })}

        {/* مؤشر الكتابة */}
        {isTyping && <TypingIndicator userName={otherUser.name} />}

        <div ref={messagesEndRef} />

        {/* زر التمرير للأسفل */}
        {showScrollButton && (
          <button
            onClick={() => scrollToBottom()}
            className="fixed bottom-55 left-[35%] transform -translate-x-1/2 bg-primary-500 text-white p-2 rounded-full shadow-lg hover:bg-primary-600 transition-colors z-10"
          >
            <ArrowUpIcon className="h-5 w-5 transform rotate-180" />
          </button>
        )}
      </div>

      {/* منطقة إرسال الرسالة */}
      <div className="p-4 border-t border-gray-200 bg-gray-50">
        {/* عرض الأخطاء */}
        {error && (
          <div className="mb-3 p-2 bg-red-50 border border-red-200 rounded text-red-700 text-sm">
            {error}
          </div>
        )}

        <div className="flex items-end space-x-2 space-x-reverse">
          {/* حقل إدخال الرسالة */}
          <div className="flex-1">
            <input
              ref={inputRef}
              type="text"
              value={newMessage}
              onChange={(e) => {
                setNewMessage(e.target.value);
                setError(null);
              }}
              onKeyDown={handleKeyDown}
              placeholder="اكتب رسالتك..."
              className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent resize-none"
              disabled={sending}
              maxLength={1000}
            />

            {/* عداد الأحرف */}
            <div className="flex justify-between items-center mt-1 px-1">
              <span className="text-xs text-gray-400">
                {newMessage.length}/1000
              </span>
            </div>
          </div>

          {/* أزرار إضافية */}
          <div className="flex items-center space-x-1 space-x-reverse">
            {/* زر الصور (مستقبلاً) */}
            <button
              type="button"
              className="p-2 text-gray-400 hover:text-gray-600 transition-colors"
              disabled
            >
              <PhotoIcon className="h-5 w-5" />
            </button>

            {/* زر الإيموجي (مستقبلاً) */}
            <button
              type="button"
              className="p-2 text-gray-400 hover:text-gray-600 transition-colors"
              disabled
            >
              <FaceSmileIcon className="h-5 w-5" />
            </button>

            {/* زر الإرسال */}
            <button
              onClick={handleSendMessage}
              disabled={!newMessage.trim() || sending}
              className="px-4 py-3 bg-primary-500 text-white rounded-lg hover:bg-primary-600 disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center transition-colors"
            >
              {sending ? (
                <div className="w-5 h-5 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
              ) : (
                <PaperAirplaneIcon className="h-5 w-5" />
              )}
            </button>
          </div>
        </div>
      </div>
    </div>
  );
}
