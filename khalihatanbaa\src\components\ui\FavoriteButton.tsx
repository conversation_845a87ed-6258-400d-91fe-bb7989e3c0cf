'use client'

import { useState, useEffect } from 'react'
import { useSession } from 'next-auth/react'
import { useRouter } from 'next/navigation'
import { HeartIcon } from '@heroicons/react/24/outline'
import { HeartIcon as HeartSolidIcon } from '@heroicons/react/24/solid'

interface FavoriteButtonProps {
  adId: string
  initialIsFavorite?: boolean
  size?: 'sm' | 'md' | 'lg'
  showText?: boolean
  className?: string
}

export function FavoriteButton({ 
  adId, 
  initialIsFavorite = false, 
  size = 'md',
  showText = false,
  className = ''
}: FavoriteButtonProps) {
  const { data: session, status } = useSession()
  const router = useRouter()
  const [isFavorite, setIsFavorite] = useState(initialIsFavorite)
  const [loading, setLoading] = useState(false)

  // التحقق من حالة المفضلة عند تحميل المكون
  useEffect(() => {
    if (status === 'authenticated' && session?.user?.id) {
      checkFavoriteStatus()
    }
  }, [status, session, adId])

  const checkFavoriteStatus = async () => {
    try {
      const response = await fetch(`/api/favorites/check?adId=${adId}`)
      const data = await response.json()
      
      if (data.success) {
        setIsFavorite(data.isFavorite)
      }
    } catch (error) {
      console.error('Error checking favorite status:', error)
    }
  }

  const toggleFavorite = async (e: React.MouseEvent) => {
    e.preventDefault()
    e.stopPropagation()

    if (status !== 'authenticated') {
      router.push('/auth/login')
      return
    }

    if (loading) return

    setLoading(true)
    
    try {
      if (isFavorite) {
        // إزالة من المفضلة
        const response = await fetch(`/api/favorites?adId=${adId}`, {
          method: 'DELETE'
        })
        
        const data = await response.json()
        
        if (data.success) {
          setIsFavorite(false)
        } else {
          console.error('Error removing from favorites:', data.error)
        }
      } else {
        // إضافة إلى المفضلة
        const response = await fetch('/api/favorites', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json'
          },
          body: JSON.stringify({ adId })
        })
        
        const data = await response.json()
        
        if (data.success) {
          setIsFavorite(true)
        } else {
          console.error('Error adding to favorites:', data.error)
        }
      }
    } catch (error) {
      console.error('Error toggling favorite:', error)
    } finally {
      setLoading(false)
    }
  }

  const getSizeClasses = () => {
    switch (size) {
      case 'sm':
        return 'h-4 w-4'
      case 'lg':
        return 'h-8 w-8'
      default:
        return 'h-6 w-6'
    }
  }

  const getButtonClasses = () => {
    const baseClasses = 'inline-flex items-center justify-center rounded-lg transition-all duration-200'
    const sizeClasses = size === 'sm' ? 'p-1' : size === 'lg' ? 'p-3' : 'p-2'
    
    if (isFavorite) {
      return `${baseClasses} ${sizeClasses} bg-red-50 text-red-600 hover:bg-red-100 border border-red-200`
    } else {
      return `${baseClasses} ${sizeClasses} bg-gray-50 text-gray-600 hover:bg-gray-100 border border-gray-200 hover:border-red-200 hover:text-red-600`
    }
  }

  if (status === 'loading') {
    return (
      <div className={`${getButtonClasses()} ${className}`}>
        <div className={`${getSizeClasses()} animate-pulse bg-gray-300 rounded`}></div>
        {showText && <span className="mr-2 text-sm">...</span>}
      </div>
    )
  }

  return (
    <button
      onClick={toggleFavorite}
      disabled={loading}
      className={`${getButtonClasses()} ${className} ${loading ? 'opacity-50 cursor-not-allowed' : ''}`}
      title={isFavorite ? 'إزالة من المفضلة' : 'إضافة إلى المفضلة'}
    >
      {loading ? (
        <div className={`${getSizeClasses()} animate-spin border-2 border-current border-t-transparent rounded-full`}></div>
      ) : isFavorite ? (
        <HeartSolidIcon className={`${getSizeClasses()} text-red-600`} />
      ) : (
        <HeartIcon className={getSizeClasses()} />
      )}
      
      {showText && (
        <span className="mr-2 text-sm font-medium">
          {isFavorite ? 'مضاف للمفضلة' : 'إضافة للمفضلة'}
        </span>
      )}
    </button>
  )
}
