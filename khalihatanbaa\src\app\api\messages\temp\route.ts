import { NextRequest, NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import { authOptions } from "@/lib/auth";

// API مؤقت للاختبار مع بيانات وهمية
export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user?.id) {
      return NextResponse.json(
        { success: false, error: "يجب تسجيل الدخول أولاً" },
        { status: 401 }
      );
    }

    const { searchParams } = new URL(request.url);
    const conversationWith = searchParams.get("with");

    if (conversationWith) {
      // إرجاع رسائل وهمية للمحادثة
      const mockMessages = [
        {
          id: "1",
          content: "مرحبا، كيف حالك؟",
          isRead: true,
          createdAt: new Date(Date.now() - 60000).toISOString(),
          fromId: conversationWith,
          toId: session.user.id,
          adId: null,
          from: {
            id: conversationWith,
            name: "أحمد محمد",
            avatar: null,
          },
          to: {
            id: session.user.id,
            name: session.user.name || "أنت",
            avatar: session.user.avatar || null,
          },
          ad: null,
        },
        {
          id: "2",
          content: "أهلاً وسهلاً، بخير والحمد لله",
          isRead: true,
          createdAt: new Date(Date.now() - 30000).toISOString(),
          fromId: session.user.id,
          toId: conversationWith,
          adId: null,
          from: {
            id: session.user.id,
            name: session.user.name || "أنت",
            avatar: session.user.avatar || null,
          },
          to: {
            id: conversationWith,
            name: "أحمد محمد",
            avatar: null,
          },
          ad: null,
        },
      ];

      return NextResponse.json({
        success: true,
        data: mockMessages,
        hasMore: false,
        lastMessageId: "2",
      });
    } else {
      // إرجاع قائمة محادثات وهمية
      const mockConversations = [
        {
          id: "1",
          content: "أهلاً وسهلاً، بخير والحمد لله",
          createdAt: new Date(Date.now() - 30000).toISOString(),
          adId: null,
          otherUser: {
            id: "user1",
            name: "أحمد محمد",
            avatar: null,
          },
          ad: null,
          unreadCount: 0,
          isOnline: true,
        },
        {
          id: "2",
          content: "شكراً لك على الاستفسار",
          createdAt: new Date(Date.now() - 120000).toISOString(),
          adId: "ad1",
          otherUser: {
            id: "user2",
            name: "فاطمة أحمد",
            avatar: null,
          },
          ad: {
            id: "ad1",
            title: "هاتف ذكي مستعمل",
            price: 500,
            imageUrls: [],
          },
          unreadCount: 2,
          isOnline: false,
        },
      ];

      return NextResponse.json({
        success: true,
        data: mockConversations,
      });
    }
  } catch (error) {
    console.error("Error in temp messages API:", error);
    return NextResponse.json(
      { success: false, error: "حدث خطأ في الخادم" },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user?.id) {
      return NextResponse.json(
        { success: false, error: "يجب تسجيل الدخول أولاً" },
        { status: 401 }
      );
    }

    const body = await request.json();
    const { toId, content, adId } = body;

    // إنشاء رسالة وهمية
    const mockMessage = {
      id: `temp-${Date.now()}`,
      content,
      isRead: false,
      createdAt: new Date().toISOString(),
      fromId: session.user.id,
      toId,
      adId: adId || null,
      from: {
        id: session.user.id,
        name: session.user.name || "أنت",
        avatar: session.user.avatar || null,
      },
      to: {
        id: toId,
        name: "مستخدم آخر",
        avatar: null,
      },
      ad: adId ? {
        id: adId,
        title: "إعلان تجريبي",
        price: 100,
        imageUrls: [],
      } : null,
    };

    return NextResponse.json({
      success: true,
      data: mockMessage,
      message: "تم إرسال الرسالة بنجاح",
    });
  } catch (error) {
    console.error("Error sending temp message:", error);
    return NextResponse.json(
      { success: false, error: "حدث خطأ في إرسال الرسالة" },
      { status: 500 }
    );
  }
}
