import { NextRequest, NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import { authOptions } from "@/lib/auth";
import { prisma } from "@/lib/prisma";
import { z } from "zod";

// مخطط التحقق من إنشاء محادثة
const createConversationSchema = z.object({
  withUserId: z.string().min(1, "معرف المستخدم مطلوب"),
  adId: z.string().min(1, "معرف الإعلان مطلوب"),
});

export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user?.id) {
      return NextResponse.json(
        { success: false, error: "يجب تسجيل الدخول أولاً" },
        { status: 401 }
      );
    }

    const body = await request.json();
    const validatedData = createConversationSchema.parse(body);

    // التحقق من عدم إنشاء محادثة مع النفس
    if (validatedData.withUserId === session.user.id) {
      return NextResponse.json(
        { success: false, error: "لا يمكن إنشاء محادثة مع نفسك" },
        { status: 400 }
      );
    }

    // التحقق من وجود المستخدم المستهدف
    const targetUser = await prisma.user.findUnique({
      where: { id: validatedData.withUserId },
      select: { id: true, name: true, avatar: true },
    });

    if (!targetUser) {
      return NextResponse.json(
        { success: false, error: "المستخدم غير موجود" },
        { status: 404 }
      );
    }

    // التحقق من وجود الإعلان
    const ad = await prisma.ad.findUnique({
      where: { id: validatedData.adId },
      select: {
        id: true,
        title: true,
        price: true,
        imageUrls: true,
        userId: true,
      },
    });

    if (!ad) {
      return NextResponse.json(
        { success: false, error: "الإعلان غير موجود" },
        { status: 404 }
      );
    }

    // التحقق من وجود محادثة سابقة
    const existingMessage = await prisma.message.findFirst({
      where: {
        OR: [
          { fromId: session.user.id, toId: validatedData.withUserId },
          { fromId: validatedData.withUserId, toId: session.user.id },
        ],
        adId: validatedData.adId,
      },
      include: {
        from: { select: { id: true, name: true, avatar: true } },
        to: { select: { id: true, name: true, avatar: true } },
        ad: { select: { id: true, title: true, price: true, imageUrls: true } },
      },
    });

    if (existingMessage) {
      // إذا كانت المحادثة موجودة، أرجع البيانات الموجودة
      return NextResponse.json({
        success: true,
        data: {
          conversation: existingMessage,
          isNew: false,
        },
        message: "المحادثة موجودة بالفعل",
      });
    }

    // إنشاء رسالة ترحيبية تلقائية
    const welcomeMessage = `مرحباً! أنا مهتم بإعلانك "${ad.title}". هل يمكننا التحدث حول التفاصيل؟`;

    const newMessage = await prisma.message.create({
      data: {
        fromId: session.user.id,
        toId: validatedData.withUserId,
        adId: validatedData.adId,
        content: welcomeMessage,
      },
      include: {
        from: { select: { id: true, name: true, avatar: true } },
        to: { select: { id: true, name: true, avatar: true } },
        ad: { select: { id: true, title: true, price: true, imageUrls: true } },
      },
    });

    // إرسال تحديث لحظي للمستقبل
    try {
      const { sendMessageToUser } = await import("../messages/sse/route");
      sendMessageToUser(validatedData.withUserId, {
        type: "new_message",
        data: newMessage,
        timestamp: new Date().toISOString(),
      });
    } catch (error) {
      console.error("Error sending real-time update:", error);
    }

    return NextResponse.json({
      success: true,
      data: {
        conversation: newMessage,
        isNew: true,
      },
      message: "تم إنشاء المحادثة بنجاح",
    });
  } catch (error) {
    console.error("Error creating conversation:", error);

    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { success: false, error: error.errors[0].message },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { success: false, error: "حدث خطأ في إنشاء المحادثة" },
      { status: 500 }
    );
  }
}
