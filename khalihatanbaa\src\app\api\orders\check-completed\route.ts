import { NextRequest, NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import { authOptions } from "@/lib/auth";
import { prisma } from "@/lib/prisma";

export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user?.id) {
      return NextResponse.json(
        { success: false, error: "يجب تسجيل الدخول أولاً" },
        { status: 401 }
      );
    }

    const { searchParams } = new URL(request.url);
    const sellerId = searchParams.get("sellerId");
    const adId = searchParams.get("adId");

    if (!sellerId) {
      return NextResponse.json(
        { success: false, error: "معرف البائع مطلوب" },
        { status: 400 }
      );
    }

    // التحقق من وجود طلب مكتمل بين المستخدم والبائع
    const completedOrder = await prisma.order.findFirst({
      where: {
        buyerId: session.user.id,
        sellerId: sellerId,
        status: "completed",
        completedAt: {
          not: null, // التأكد من أن الطلب مكتمل فعلاً
        },
        ...(adId && { adId: adId }),
      },
      include: {
        ad: {
          select: {
            id: true,
            title: true,
            price: true,
          },
        },
        seller: {
          select: {
            id: true,
            name: true,
            avatar: true,
          },
        },
      },
    });

    // التحقق من وجود تقييم سابق
    let existingRating = null;
    if (completedOrder) {
      existingRating = await prisma.rating.findUnique({
        where: {
          userId_sellerId: {
            userId: session.user.id,
            sellerId: sellerId,
          },
        },
      });
    }

    return NextResponse.json({
      success: true,
      data: {
        hasCompletedOrder: !!completedOrder,
        order: completedOrder,
        hasExistingRating: !!existingRating,
        existingRating: existingRating,
        canRate: !!completedOrder && !existingRating,
      },
    });
  } catch (error) {
    console.error("Error checking completed order:", error);
    return NextResponse.json(
      { success: false, error: "حدث خطأ في التحقق من الطلبات المكتملة" },
      { status: 500 }
    );
  }
}
