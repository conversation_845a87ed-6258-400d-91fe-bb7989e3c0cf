const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

// بيانات إعلانات حقيقية ومتنوعة
const sampleAds = [
  {
    title: "تويوتا كامري 2020 فل كامل",
    description: "سيارة بحالة ممتازة، صيانة دورية منتظمة، لون أبيض لؤلؤي، فحص كامل، جميع الأوراق سليمة",
    price: 52000000,
    category: "سيارات",
    subCategory: "تويوتا",
    condition: "مستعمل",
    city: "دمشق",
    region: "المزة",
    imageUrls: ["https://images.unsplash.com/photo-1549924231-f129b911e442?w=800"],
    isPromoted: true,
    specifications: {
      year: 2020,
      mileage: 45000,
      fuel: "بنزين",
      color: "أبيض لؤلؤي",
      transmission: "أوتوماتيك"
    }
  },
  {
    title: "شقة 3 غرف في المالكي - إطلالة رائعة",
    description: "شقة مفروشة بالكامل، إطلالة على جبل قاسيون، قريبة من الخدمات والمواصلات، تدفئة مركزية",
    price: 150000000,
    category: "عقارات",
    subCategory: "شقة",
    condition: "ممتاز",
    city: "دمشق",
    region: "المالكي",
    imageUrls: ["https://images.unsplash.com/photo-1560448204-e02f11c3d0e2?w=800"],
    isPromoted: false,
    specifications: {
      rooms: 3,
      bathrooms: 2,
      area: 140,
      floor: 4,
      furnished: true
    }
  },
  {
    title: "آيفون 15 برو ماكس 256 جيجا - جديد",
    description: "جهاز جديد لم يستعمل، مع الكرتونة والشاحن الأصلي، ضمان سنة كاملة، جميع الألوان متوفرة",
    price: 12500000,
    category: "إلكترونيات",
    subCategory: "هاتف ذكي",
    condition: "جديد",
    city: "حلب",
    region: "الفرقان",
    imageUrls: ["https://images.unsplash.com/photo-1592750475338-74b7b21085ab?w=800"],
    isPromoted: true,
    specifications: {
      brand: "Apple",
      model: "iPhone 15 Pro Max",
      storage: "256GB",
      color: "تيتانيوم طبيعي"
    }
  },
  {
    title: "طقم غرفة نوم خشب زان طبيعي",
    description: "طقم كامل: سرير كينغ + دولاب 6 أبواب + تسريحة + كومودينتين، خشب زان طبيعي 100%",
    price: 18000000,
    category: "أثاث",
    subCategory: "غرفة نوم",
    condition: "جديد",
    city: "حمص",
    region: "الوعر",
    imageUrls: ["https://images.unsplash.com/photo-1586023492125-27b2c045efd7?w=800"],
    isPromoted: false,
    specifications: {
      material: "خشب زان طبيعي",
      pieces: 5,
      bedSize: "كينغ"
    }
  },
  {
    title: "لابتوب Dell XPS 13 - حالة ممتازة",
    description: "لابتوب للبرمجة والتصميم، معالج Intel i7، ذاكرة 16GB، SSD 512GB، شاشة 4K",
    price: 8500000,
    category: "إلكترونيات",
    subCategory: "لابتوب",
    condition: "مستعمل",
    city: "دمشق",
    region: "أبو رمانة",
    imageUrls: ["https://images.unsplash.com/photo-1496181133206-80ce9b88a853?w=800"],
    isPromoted: true,
    specifications: {
      brand: "Dell",
      model: "XPS 13",
      processor: "Intel i7",
      ram: "16GB",
      storage: "512GB SSD"
    }
  },
  {
    title: "دراجة هوائية جبلية - Trek",
    description: "دراجة هوائية جبلية احترافية، 21 سرعة، إطارات مقاومة للثقب، مناسبة للطرق الوعرة",
    price: 2500000,
    category: "رياضة",
    subCategory: "دراجات",
    condition: "مستعمل",
    city: "اللاذقية",
    region: "الرمل الشمالي",
    imageUrls: ["https://images.unsplash.com/photo-1558618047-3c8c76ca7d13?w=800"],
    isPromoted: false,
    specifications: {
      brand: "Trek",
      type: "جبلية",
      speeds: 21,
      wheelSize: "26 إنش"
    }
  },
  {
    title: "كاميرا Canon EOS R5 - للمصورين المحترفين",
    description: "كاميرا احترافية للتصوير الفوتوغرافي والفيديو، دقة 45 ميجابكسل، تصوير 8K، مع عدسة 24-70mm",
    price: 25000000,
    category: "إلكترونيات",
    subCategory: "كاميرا",
    condition: "جديد",
    city: "دمشق",
    region: "الشعلان",
    imageUrls: ["https://images.unsplash.com/photo-1606983340126-99ab4feaa64a?w=800"],
    isPromoted: true,
    specifications: {
      brand: "Canon",
      model: "EOS R5",
      megapixels: 45,
      videoResolution: "8K"
    }
  },
  {
    title: "مكتب خشبي للدراسة والعمل",
    description: "مكتب خشبي عملي مع أدراج، مناسب للدراسة والعمل من المنزل، تصميم عصري وأنيق",
    price: 3500000,
    category: "أثاث",
    subCategory: "مكتب",
    condition: "جديد",
    city: "حلب",
    region: "الجميلية",
    imageUrls: ["https://images.unsplash.com/photo-1586023492125-27b2c045efd7?w=800"],
    isPromoted: false,
    specifications: {
      material: "خشب MDF",
      drawers: 3,
      dimensions: "120x60x75 سم"
    }
  }
];

async function seedAds() {
  try {
    console.log('🌱 بدء إضافة الإعلانات...');

    // الحصول على أول مستخدم من قاعدة البيانات
    const users = await prisma.user.findMany({
      take: 3,
      select: { id: true, name: true }
    });

    if (users.length === 0) {
      console.log('❌ لا يوجد مستخدمين في قاعدة البيانات');
      return;
    }

    console.log(`✅ تم العثور على ${users.length} مستخدمين`);

    // إضافة الإعلانات
    for (let i = 0; i < sampleAds.length; i++) {
      const adData = sampleAds[i];
      const user = users[i % users.length]; // توزيع الإعلانات على المستخدمين

      const ad = await prisma.ad.create({
        data: {
          ...adData,
          userId: user.id,
          isActive: true,
          publishedAt: new Date(),
          expiresAt: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000), // 30 يوم
          views: Math.floor(Math.random() * 200) + 10, // مشاهدات عشوائية
        }
      });

      console.log(`✅ تم إنشاء الإعلان: ${ad.title} للمستخدم: ${user.name}`);
    }

    console.log('🎉 تم إنشاء جميع الإعلانات بنجاح!');

  } catch (error) {
    console.error('❌ خطأ في إنشاء الإعلانات:', error);
  } finally {
    await prisma.$disconnect();
  }
}

// تشغيل السكريبت
seedAds();
