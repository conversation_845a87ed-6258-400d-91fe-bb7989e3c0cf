"use client";

import { useState, useEffect } from "react";
import { useSession } from "next-auth/react";
import { useRouter, useSearchParams } from "next/navigation";
import { Header } from "@/components/layout/Header";
import { PlaceholderImage } from "@/components/ui/PlaceholderImage";
import { ExclamationCircleIcon } from "@heroicons/react/24/outline";
import { ClientOnly } from "@/components/ClientOnly";
import { useMessages } from "@/hooks/useMessages";
import {
  ConversationList,
  ConversationStats,
} from "@/components/messaging/ConversationList";
import { ChatArea } from "@/components/messaging/ChatArea";
import { getTotalUnreadCount } from "@/lib/messaging";

export default function MessagesPage() {
  const { data: session, status } = useSession();
  const router = useRouter();
  const searchParams = useSearchParams();

  const [selectedConversation, setSelectedConversation] = useState<
    string | null
  >(null);

  // استخدام الـ hook المحسن
  const {
    conversations,
    messages,
    loading,
    sending,
    error,
    hasMore,
    loadMoreMessages,
    sendMessage: sendMessageHook,
  } = useMessages({
    conversationWith: selectedConversation || undefined,
    adId: searchParams.get("ad") || undefined,
    enableRealTime: true,
  });

  // دالة إرسال الرسالة المحسنة
  const handleSendMessage = async (content: string) => {
    if (!selectedConversation) return false;

    const adId = searchParams.get("ad");
    if (!adId) {
      // البحث عن adId من المحادثة الحالية
      const currentConversation = conversations.find(
        (conv) => conv.otherUser.id === selectedConversation
      );
      if (!currentConversation?.adId) {
        return false; // لا يمكن إرسال رسالة بدون adId
      }
      return await sendMessageHook(
        content,
        selectedConversation,
        currentConversation.adId
      );
    }

    return await sendMessageHook(content, selectedConversation, adId);
  };

  // Effects
  useEffect(() => {
    if (status === "authenticated") {
      // التحقق من وجود محادثة محددة في URL
      const withUser = searchParams.get("with") || searchParams.get("user");

      if (withUser) {
        setSelectedConversation(withUser);
      }
    }
  }, [searchParams, status]);

  useEffect(() => {
    if (status === "unauthenticated") {
      router.push("/auth/login");
    }
  }, [status, router]);

  // إعادة توجيه إذا لم يكن المستخدم مسجل دخول
  if (status === "loading") {
    return (
      <div className="min-h-screen flex items-center justify-center">
        جاري التحميل...
      </div>
    );
  }

  if (status === "unauthenticated") {
    return null;
  }

  const selectConversation = (conversation: any) => {
    setSelectedConversation(conversation.otherUser.id);

    // تحديث URL (محسن للمحادثات المتعددة)
    const url = new URL(window.location.href);
    url.searchParams.set("with", conversation.otherUser.id);

    // adId مطلوب دائماً للمحادثات المتعددة
    if (conversation.adId) {
      url.searchParams.set("ad", conversation.adId);
    } else {
      // إذا لم يكن هناك adId، احتفظ بالموجود في URL
      const currentAdId = url.searchParams.get("ad");
      if (!currentAdId) {
        console.warn("لا يوجد adId للمحادثة المحددة");
      }
    }

    window.history.pushState({}, "", url.toString());
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-background">
        <Header />
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <div className="animate-pulse">
            <div className="h-8 bg-gray-200 rounded w-1/4 mb-6"></div>
            <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
              <div className="h-96 bg-gray-200 rounded-lg"></div>
              <div className="lg:col-span-2 h-96 bg-gray-200 rounded-lg"></div>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <ClientOnly
      fallback={
        <div className="min-h-screen bg-background">
          <Header />
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
            <div className="animate-pulse">
              <div className="h-8 bg-gray-200 rounded w-1/4 mb-6"></div>
              <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
                <div className="h-96 bg-gray-200 rounded-lg"></div>
                <div className="lg:col-span-2 h-96 bg-gray-200 rounded-lg"></div>
              </div>
            </div>
          </div>
        </div>
      }
    >
      <div className="min-h-screen bg-background">
        <Header />

        <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <h1 className="text-2xl font-bold text-dark-800 mb-6">الرسائل</h1>

          {/* عرض الأخطاء */}
          {error && (
            <div className="mb-4 p-4 bg-red-50 border border-red-200 rounded-lg flex items-center space-x-2 space-x-reverse">
              <ExclamationCircleIcon className="h-5 w-5 text-red-500 flex-shrink-0" />
              <p className="text-red-700">{error}</p>
            </div>
          )}

          <div
            className="grid grid-cols-1 lg:grid-cols-3 gap-6"
            style={{ height: "calc(100vh - 200px)" }}
          >
            {/* قائمة المحادثات */}
            <div className="lg:col-span-1">
              <ConversationStats
                totalConversations={conversations.length}
                unreadCount={getTotalUnreadCount(conversations)}
              />
              <ConversationList
                conversations={conversations}
                selectedConversationId={selectedConversation || undefined}
                onSelectConversation={selectConversation}
                loading={loading}
              />
            </div>

            {/* منطقة المحادثة */}
            <ChatArea
              messages={messages}
              currentUserId={session?.user?.id || ""}
              otherUser={
                selectedConversation
                  ? conversations.find(
                      (c) => c.otherUser.id === selectedConversation
                    )?.otherUser
                  : undefined
              }
              ad={
                selectedConversation
                  ? conversations.find(
                      (c) => c.otherUser.id === selectedConversation
                    )?.ad
                  : undefined
              }
              onSendMessage={handleSendMessage}
              sending={sending}
              hasMore={hasMore}
              onLoadMore={loadMoreMessages}
              loading={loading}
            />
          </div>
        </main>
      </div>
    </ClientOnly>
  );
}
