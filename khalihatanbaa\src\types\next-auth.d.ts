import NextAuth from 'next-auth'

declare module 'next-auth' {
  interface Session {
    user: {
      id: string
      name: string
      email: string
      phone: string
      role: string
      avatar?: string
    }
  }

  interface User {
    id: string
    name: string
    email: string
    phone: string
    role: string
    avatar?: string
  }
}

declare module 'next-auth/jwt' {
  interface JWT {
    role: string
    phone: string
    avatar?: string
  }
}
