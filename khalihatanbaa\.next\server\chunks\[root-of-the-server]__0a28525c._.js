module.exports = {

"[project]/.next-internal/server/app/api/messages/route/actions.js [app-rsc] (server actions loader, ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
}}),
"[externals]/next/dist/compiled/next-server/app-route-turbo.runtime.dev.js [external] (next/dist/compiled/next-server/app-route-turbo.runtime.dev.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/next-server/app-route-turbo.runtime.dev.js", () => require("next/dist/compiled/next-server/app-route-turbo.runtime.dev.js"));

module.exports = mod;
}}),
"[externals]/next/dist/compiled/@opentelemetry/api [external] (next/dist/compiled/@opentelemetry/api, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/@opentelemetry/api", () => require("next/dist/compiled/@opentelemetry/api"));

module.exports = mod;
}}),
"[externals]/next/dist/compiled/next-server/app-page-turbo.runtime.dev.js [external] (next/dist/compiled/next-server/app-page-turbo.runtime.dev.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/next-server/app-page-turbo.runtime.dev.js", () => require("next/dist/compiled/next-server/app-page-turbo.runtime.dev.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/work-unit-async-storage.external.js [external] (next/dist/server/app-render/work-unit-async-storage.external.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/work-unit-async-storage.external.js", () => require("next/dist/server/app-render/work-unit-async-storage.external.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/work-async-storage.external.js [external] (next/dist/server/app-render/work-async-storage.external.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/work-async-storage.external.js", () => require("next/dist/server/app-render/work-async-storage.external.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/after-task-async-storage.external.js [external] (next/dist/server/app-render/after-task-async-storage.external.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/after-task-async-storage.external.js", () => require("next/dist/server/app-render/after-task-async-storage.external.js"));

module.exports = mod;
}}),
"[externals]/util [external] (util, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("util", () => require("util"));

module.exports = mod;
}}),
"[externals]/url [external] (url, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("url", () => require("url"));

module.exports = mod;
}}),
"[externals]/http [external] (http, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("http", () => require("http"));

module.exports = mod;
}}),
"[externals]/crypto [external] (crypto, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("crypto", () => require("crypto"));

module.exports = mod;
}}),
"[externals]/assert [external] (assert, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("assert", () => require("assert"));

module.exports = mod;
}}),
"[externals]/querystring [external] (querystring, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("querystring", () => require("querystring"));

module.exports = mod;
}}),
"[externals]/buffer [external] (buffer, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("buffer", () => require("buffer"));

module.exports = mod;
}}),
"[externals]/zlib [external] (zlib, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("zlib", () => require("zlib"));

module.exports = mod;
}}),
"[externals]/https [external] (https, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("https", () => require("https"));

module.exports = mod;
}}),
"[externals]/events [external] (events, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("events", () => require("events"));

module.exports = mod;
}}),
"[externals]/@prisma/client [external] (@prisma/client, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("@prisma/client", () => require("@prisma/client"));

module.exports = mod;
}}),
"[project]/src/lib/prisma.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "prisma": (()=>prisma)
});
var __TURBOPACK__imported__module__$5b$externals$5d2f40$prisma$2f$client__$5b$external$5d$__$2840$prisma$2f$client$2c$__cjs$29$__ = __turbopack_context__.i("[externals]/@prisma/client [external] (@prisma/client, cjs)");
;
const globalForPrisma = globalThis;
const prisma = globalForPrisma.prisma ?? new __TURBOPACK__imported__module__$5b$externals$5d2f40$prisma$2f$client__$5b$external$5d$__$2840$prisma$2f$client$2c$__cjs$29$__["PrismaClient"]({
    log: [
        'query'
    ]
});
if ("TURBOPACK compile-time truthy", 1) globalForPrisma.prisma = prisma;
}}),
"[project]/src/lib/auth.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "authOptions": (()=>authOptions)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$auth$2f$providers$2f$credentials$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next-auth/providers/credentials.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$bcryptjs$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/bcryptjs/index.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$prisma$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/prisma.ts [app-route] (ecmascript)");
;
;
;
const authOptions = {
    providers: [
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$auth$2f$providers$2f$credentials$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"])({
            name: "credentials",
            credentials: {
                emailOrPhone: {
                    label: "البريد الإلكتروني أو رقم الهاتف",
                    type: "text"
                },
                password: {
                    label: "كلمة المرور",
                    type: "password"
                }
            },
            async authorize (credentials) {
                if (!credentials?.emailOrPhone || !credentials?.password) {
                    return null;
                }
                // البحث عن المستخدم بالبريد الإلكتروني أو رقم الهاتف
                const user = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$prisma$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["prisma"].user.findFirst({
                    where: {
                        OR: [
                            {
                                email: credentials.emailOrPhone
                            },
                            {
                                phone: credentials.emailOrPhone
                            }
                        ],
                        isActive: true
                    }
                });
                if (!user) {
                    return null;
                }
                // التحقق من كلمة المرور
                const isPasswordValid = await __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$bcryptjs$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"].compare(credentials.password, user.passwordHash);
                if (!isPasswordValid) {
                    return null;
                }
                return {
                    id: user.id,
                    name: user.name,
                    email: user.email,
                    phone: user.phone,
                    role: user.role,
                    avatar: user.avatar || undefined
                };
            }
        })
    ],
    session: {
        strategy: "jwt"
    },
    callbacks: {
        async jwt ({ token, user }) {
            if (user) {
                token.role = user.role;
                token.phone = user.phone;
                token.avatar = user.avatar;
            }
            return token;
        },
        async session ({ session, token }) {
            if (token) {
                session.user.id = token.sub;
                session.user.role = token.role;
                session.user.phone = token.phone;
                session.user.avatar = token.avatar;
            }
            return session;
        }
    },
    pages: {
        signIn: "/auth/login"
    }
};
}}),
"[project]/src/app/api/messages/route.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "GET": (()=>GET),
    "POST": (()=>POST),
    "addActiveConnection": (()=>addActiveConnection),
    "isUserOnline": (()=>isUserOnline),
    "removeActiveConnection": (()=>removeActiveConnection)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/server.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$auth$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next-auth/index.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$auth$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/auth.ts [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$prisma$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/prisma.ts [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$dist$2f$esm$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/zod/dist/esm/index.js [app-route] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$dist$2f$esm$2f$v3$2f$external$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__ = __turbopack_context__.i("[project]/node_modules/zod/dist/esm/v3/external.js [app-route] (ecmascript) <export * as z>");
;
;
;
;
;
// مخطط التحقق من إرسال رسالة
const sendMessageSchema = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$dist$2f$esm$2f$v3$2f$external$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].object({
    toId: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$dist$2f$esm$2f$v3$2f$external$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].string().min(1, "معرف المستقبل مطلوب"),
    adId: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$dist$2f$esm$2f$v3$2f$external$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].string().min(1, "معرف الإعلان مطلوب"),
    content: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$dist$2f$esm$2f$v3$2f$external$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].string().min(1, "محتوى الرسالة مطلوب").max(1000, "الرسالة طويلة جداً")
});
// Cache للاتصالات النشطة
const activeConnections = new Map();
function addActiveConnection(userId, connectionId) {
    if (!activeConnections.has(userId)) {
        activeConnections.set(userId, new Set());
    }
    activeConnections.get(userId).add(connectionId);
}
function removeActiveConnection(userId, connectionId) {
    const connections = activeConnections.get(userId);
    if (connections) {
        connections.delete(connectionId);
        if (connections.size === 0) {
            activeConnections.delete(userId);
        }
    }
}
function isUserOnline(userId) {
    return activeConnections.has(userId) && activeConnections.get(userId).size > 0;
}
async function GET(request) {
    try {
        const session = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$auth$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["getServerSession"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$auth$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["authOptions"]);
        if (!session?.user?.id) {
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                success: false,
                error: "يجب تسجيل الدخول أولاً"
            }, {
                status: 401
            });
        }
        const { searchParams } = new URL(request.url);
        const conversationWith = searchParams.get("with");
        const adId = searchParams.get("adId") || searchParams.get("ad"); // دعم كلا الشكلين
        const lastMessageId = searchParams.get("lastMessageId");
        const limit = Math.min(parseInt(searchParams.get("limit") || "10"), 10); // حد أقصى 10 رسائل للتحميل السريع
        if (conversationWith) {
            // التحقق من عدم كون المستخدم يحاول مراسلة نفسه
            if (conversationWith === session.user.id) {
                return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                    success: false,
                    error: "لا يمكن مراسلة نفسك"
                }, {
                    status: 400
                });
            }
            // جلب محادثة محددة مع pagination
            const whereClause = {
                OR: [
                    {
                        fromId: session.user.id,
                        toId: conversationWith
                    },
                    {
                        fromId: conversationWith,
                        toId: session.user.id
                    }
                ],
                ...adId && {
                    adId
                }
            };
            // إضافة cursor pagination للرسائل الأقدم
            if (lastMessageId) {
                whereClause.id = {
                    lt: lastMessageId
                };
            }
            // إضافة فلترة زمنية فقط إذا لم يكن هناك lastMessageId (التحميل الأول فقط)
            if (!lastMessageId) {
                const thirtyDaysAgo = new Date();
                thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);
                whereClause.createdAt = {
                    gte: thirtyDaysAgo
                };
            }
            // جلب الرسائل بدون include لتقليل حجم البيانات
            const messages = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$prisma$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["prisma"].message.findMany({
                where: whereClause,
                select: {
                    id: true,
                    content: true,
                    isRead: true,
                    createdAt: true,
                    fromId: true,
                    toId: true,
                    adId: true
                },
                orderBy: {
                    createdAt: "desc"
                },
                take: limit
            });
            // جلب بيانات المستخدمين والإعلانات بشكل منفصل
            const userIds = [
                ...new Set([
                    ...messages.map((m)=>m.fromId),
                    ...messages.map((m)=>m.toId)
                ])
            ];
            const adIds = [
                ...new Set(messages.map((m)=>m.adId).filter(Boolean))
            ];
            const [users, ads] = await Promise.all([
                __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$prisma$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["prisma"].user.findMany({
                    where: {
                        id: {
                            in: userIds
                        }
                    },
                    select: {
                        id: true,
                        name: true,
                        avatar: true
                    }
                }),
                adIds.length > 0 ? __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$prisma$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["prisma"].ad.findMany({
                    where: {
                        id: {
                            in: adIds
                        }
                    },
                    select: {
                        id: true,
                        title: true,
                        price: true,
                        imageUrls: true
                    }
                }) : []
            ]);
            // دمج البيانات مع الحفاظ على الترتيب التنازلي
            const messagesWithData = messages.map((message)=>({
                    ...message,
                    from: users.find((u)=>u.id === message.fromId),
                    to: users.find((u)=>u.id === message.toId),
                    ad: message.adId ? ads.find((a)=>a.id === message.adId) : null
                }));
            // تحديد الرسائل كمقروءة فقط إذا لم يكن هناك lastMessageId (أول تحميل)
            if (!lastMessageId) {
                await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$prisma$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["prisma"].message.updateMany({
                    where: {
                        fromId: conversationWith,
                        toId: session.user.id,
                        isRead: false
                    },
                    data: {
                        isRead: true
                    }
                });
            }
            // التحقق من وجود رسائل أقدم
            let hasMore = false;
            if (messages.length === limit) {
                const oldestMessageId = messages[messages.length - 1].id;
                const olderMessagesCount = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$prisma$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["prisma"].message.count({
                    where: {
                        OR: [
                            {
                                fromId: session.user.id,
                                toId: conversationWith
                            },
                            {
                                fromId: conversationWith,
                                toId: session.user.id
                            }
                        ],
                        ...adId && {
                            adId
                        },
                        id: {
                            lt: oldestMessageId
                        }
                    }
                });
                hasMore = olderMessagesCount > 0;
            }
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                success: true,
                data: messagesWithData,
                hasMore,
                // آخر رسالة هي الأقدم في المجموعة (للـ pagination)
                lastMessageId: messages.length > 0 ? messages[messages.length - 1].id : null
            });
        } else {
            // جلب قائمة المحادثات - بطريقة محسنة وسريعة
            const threeDaysAgo = new Date();
            threeDaysAgo.setDate(threeDaysAgo.getDate() - 3);
            const recentMessages = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$prisma$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["prisma"].message.findMany({
                where: {
                    OR: [
                        {
                            fromId: session.user.id
                        },
                        {
                            toId: session.user.id
                        }
                    ],
                    createdAt: {
                        gte: threeDaysAgo
                    },
                    // منع الرسائل للنفس
                    NOT: {
                        AND: {
                            fromId: session.user.id,
                            toId: session.user.id
                        }
                    },
                    // فقط المحادثات التي لها adId
                    adId: {
                        not: null
                    }
                },
                include: {
                    from: {
                        select: {
                            id: true,
                            name: true,
                            avatar: true
                        }
                    },
                    to: {
                        select: {
                            id: true,
                            name: true,
                            avatar: true
                        }
                    },
                    ad: {
                        select: {
                            id: true,
                            title: true,
                            price: true
                        }
                    }
                },
                orderBy: {
                    createdAt: "desc"
                },
                take: 30
            });
            // تجميع المحادثات حسب الشخص فقط (بدون الإعلان)
            const groupedConversations = recentMessages.reduce((acc, message)=>{
                const otherUserId = message.fromId === session.user.id ? message.toId : message.fromId;
                const key = otherUserId; // فقط معرف الشخص، بدون الإعلان
                if (!acc[key] || acc[key].createdAt < message.createdAt) {
                    acc[key] = {
                        ...message,
                        otherUser: message.fromId === session.user.id ? message.to : message.from,
                        unreadCount: 0,
                        isOnline: isUserOnline(otherUserId)
                    };
                }
                return acc;
            }, {});
            // حساب الرسائل غير المقروءة لكل محادثة
            const conversationsList = Object.values(groupedConversations);
            for (const conversation of conversationsList){
                const unreadCount = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$prisma$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["prisma"].message.count({
                    where: {
                        fromId: conversation.otherUser.id,
                        toId: session.user.id,
                        isRead: false
                    }
                });
                conversation.unreadCount = unreadCount;
            }
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                success: true,
                data: conversationsList
            });
        }
    } catch (error) {
        console.error("Error fetching messages:", error);
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
            success: false,
            error: "حدث خطأ في جلب الرسائل"
        }, {
            status: 500
        });
    }
}
async function POST(request) {
    try {
        const session = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$auth$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["getServerSession"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$auth$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["authOptions"]);
        if (!session?.user?.id) {
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                success: false,
                error: "يجب تسجيل الدخول أولاً"
            }, {
                status: 401
            });
        }
        const body = await request.json();
        const validatedData = sendMessageSchema.parse(body);
        // التحقق من عدم إرسال رسالة للنفس
        if (validatedData.toId === session.user.id) {
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                success: false,
                error: "لا يمكن إرسال رسالة لنفسك"
            }, {
                status: 400
            });
        }
        // التحقق من وجود المستقبل
        const recipient = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$prisma$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["prisma"].user.findUnique({
            where: {
                id: validatedData.toId
            }
        });
        if (!recipient) {
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                success: false,
                error: "المستخدم المستقبل غير موجود"
            }, {
                status: 404
            });
        }
        // التحقق من وجود الإعلان
        const ad = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$prisma$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["prisma"].ad.findUnique({
            where: {
                id: validatedData.adId
            }
        });
        if (!ad) {
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                success: false,
                error: "الإعلان غير موجود"
            }, {
                status: 404
            });
        }
        // إنشاء الرسالة
        const message = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$prisma$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["prisma"].message.create({
            data: {
                fromId: session.user.id,
                toId: validatedData.toId,
                adId: validatedData.adId,
                content: validatedData.content
            },
            include: {
                from: {
                    select: {
                        id: true,
                        name: true,
                        avatar: true
                    }
                },
                to: {
                    select: {
                        id: true,
                        name: true,
                        avatar: true
                    }
                },
                ad: {
                    select: {
                        id: true,
                        title: true,
                        price: true,
                        imageUrls: true
                    }
                }
            }
        });
        // إرسال تحديث لحظي للمستقبل
        try {
            const { sendMessageToUser } = await __turbopack_context__.r("[project]/src/app/api/messages/sse/route.ts [app-route] (ecmascript, async loader)")(__turbopack_context__.i);
            sendMessageToUser(validatedData.toId, {
                type: "new_message",
                data: message,
                timestamp: new Date().toISOString()
            });
        } catch (error) {
            console.error("Error sending real-time update:", error);
        // لا نريد أن يفشل إرسال الرسالة بسبب مشكلة في التحديث اللحظي
        }
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
            success: true,
            data: message,
            message: "تم إرسال الرسالة بنجاح"
        });
    } catch (error) {
        console.error("Error sending message:", error);
        if (error instanceof __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$dist$2f$esm$2f$v3$2f$external$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].ZodError) {
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                success: false,
                error: error.errors[0].message
            }, {
                status: 400
            });
        }
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
            success: false,
            error: "حدث خطأ في إرسال الرسالة"
        }, {
            status: 500
        });
    }
}
}}),

};

//# sourceMappingURL=%5Broot-of-the-server%5D__0a28525c._.js.map