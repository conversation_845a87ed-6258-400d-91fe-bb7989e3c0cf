import { NextRequest, NextResponse } from 'next/server'
import bcrypt from 'bcryptjs'
import { prisma } from '@/lib/prisma'
import { z } from 'zod'

// مخطط التحقق من البيانات
const registerSchema = z.object({
  name: z.string().min(2, 'الاسم يجب أن يكون أكثر من حرفين'),
  email: z.string().email('البريد الإلكتروني غير صحيح'),
  phone: z.string().min(10, 'رقم الهاتف غير صحيح'),
  password: z.string().min(6, 'كلمة المرور يجب أن تكون 6 أحرف على الأقل')
})

export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    
    // التحقق من صحة البيانات
    const validatedData = registerSchema.parse(body)
    
    // التحقق من عدم وجود مستخدم بنفس البريد الإلكتروني أو رقم الهاتف
    const existingUser = await prisma.user.findFirst({
      where: {
        OR: [
          { email: validatedData.email },
          { phone: validatedData.phone }
        ]
      }
    })

    if (existingUser) {
      return NextResponse.json(
        { 
          success: false, 
          error: 'المستخدم موجود بالفعل بهذا البريد الإلكتروني أو رقم الهاتف' 
        },
        { status: 400 }
      )
    }

    // تشفير كلمة المرور
    const hashedPassword = await bcrypt.hash(validatedData.password, 12)

    // إنشاء المستخدم الجديد
    const user = await prisma.user.create({
      data: {
        name: validatedData.name,
        email: validatedData.email,
        phone: validatedData.phone,
        passwordHash: hashedPassword,
        freeAdsExpiresAt: new Date(Date.now() + 15 * 24 * 60 * 60 * 1000) // 15 يوم من الآن
      },
      select: {
        id: true,
        name: true,
        email: true,
        phone: true,
        role: true,
        freeAdsCount: true,
        freeAdsExpiresAt: true,
        createdAt: true
      }
    })

    return NextResponse.json({
      success: true,
      data: user,
      message: 'تم إنشاء الحساب بنجاح'
    })

  } catch (error) {
    console.error('Registration error:', error)
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { 
          success: false, 
          error: error.errors[0].message 
        },
        { status: 400 }
      )
    }

    return NextResponse.json(
      { 
        success: false, 
        error: 'حدث خطأ في إنشاء الحساب' 
      },
      { status: 500 }
    )
  }
}
