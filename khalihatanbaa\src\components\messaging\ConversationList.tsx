import { useState } from "react";
import {
  ChatBubbleLeftRightIcon,
  UserIcon,
  ClockIcon,
  MagnifyingGlassIcon,
  XMarkIcon,
} from "@heroicons/react/24/outline";
import {
  formatMessageTime,
  searchConversations,
  sortConversations,
} from "@/lib/messaging";

interface Conversation {
  id: string;
  content: string;
  createdAt: string;
  adId?: string;
  otherUser: {
    id: string;
    name: string;
    avatar?: string;
  };
  ad?: {
    id: string;
    title: string;
    price: number;
    imageUrls: string[];
  };
  unreadCount: number;
  isOnline?: boolean;
}

interface ConversationListProps {
  conversations: Conversation[];
  selectedConversationId?: string;
  onSelectConversation: (conversation: Conversation) => void;
  loading?: boolean;
}

export function ConversationList({
  conversations,
  selectedConversationId,
  onSelectConversation,
  loading = false,
}: ConversationListProps) {
  const [searchQuery, setSearchQuery] = useState("");

  // تصفية وترتيب المحادثات
  const filteredConversations = searchConversations(conversations, searchQuery);
  const sortedConversations = sortConversations(filteredConversations);

  if (loading) {
    return (
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden">
        <div className="p-4 border-b border-gray-200">
          <h2 className="font-semibold text-dark-800">المحادثات</h2>
        </div>
        <div className="p-6">
          <div className="animate-pulse space-y-4">
            {[...Array(5)].map((_, i) => (
              <div
                key={i}
                className="flex items-center space-x-3 space-x-reverse"
              >
                <div className="w-10 h-10 bg-gray-200 rounded-full"></div>
                <div className="flex-1 space-y-2">
                  <div className="h-4 bg-gray-200 rounded w-3/4"></div>
                  <div className="h-3 bg-gray-200 rounded w-1/2"></div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>
    );
  }

  return (
    <div
      className="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden flex flex-col"
      style={{ height: "calc(100vh - 250px)" }}
    >
      {/* رأس قائمة المحادثات */}
      <div className="p-4 border-b border-gray-200">
        <h2 className="font-semibold text-dark-800 mb-3">المحادثات</h2>

        {/* شريط البحث */}
        <div className="relative">
          <MagnifyingGlassIcon className="absolute right-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
          <input
            type="text"
            placeholder="البحث في المحادثات..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className="w-full pr-10 pl-8 py-2 text-sm border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent"
          />
          {searchQuery && (
            <button
              onClick={() => setSearchQuery("")}
              className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600"
            >
              <XMarkIcon className="h-4 w-4" />
            </button>
          )}
        </div>
      </div>

      {/* قائمة المحادثات */}
      <div className="flex-1 overflow-y-auto">
        {sortedConversations.length === 0 ? (
          <div className="p-6 text-center text-gray-500">
            {searchQuery ? (
              <>
                <MagnifyingGlassIcon className="h-12 w-12 mx-auto mb-3 text-gray-300" />
                <p>لا توجد نتائج للبحث</p>
                <p className="text-sm mt-1">جرب كلمات مختلفة</p>
              </>
            ) : (
              <>
                <ChatBubbleLeftRightIcon className="h-12 w-12 mx-auto mb-3 text-gray-300" />
                <p>لا توجد محادثات حتى الآن</p>
                <p className="text-sm mt-1">
                  ابدأ محادثة من خلال التواصل مع بائع
                </p>
              </>
            )}
          </div>
        ) : (
          sortedConversations.map((conversation) => (
            <ConversationItem
              key={`${conversation.otherUser.id}-${
                conversation.adId || "general"
              }`}
              conversation={conversation}
              isSelected={selectedConversationId === conversation.otherUser.id}
              onClick={() => onSelectConversation(conversation)}
            />
          ))
        )}
      </div>
    </div>
  );
}

// مكون عنصر المحادثة
interface ConversationItemProps {
  conversation: Conversation;
  isSelected: boolean;
  onClick: () => void;
}

function ConversationItem({
  conversation,
  isSelected,
  onClick,
}: ConversationItemProps) {
  return (
    <button
      onClick={onClick}
      className={`w-full p-4 border-b border-gray-100 hover:bg-gray-50 text-right transition-colors ${
        isSelected ? "bg-primary-50 border-primary-100" : ""
      }`}
    >
      <div className="flex items-start space-x-3 space-x-reverse">
        {/* صورة المستخدم مع مؤشر الحالة */}
        <div className="relative w-10 h-10 bg-primary-100 rounded-full flex items-center justify-center flex-shrink-0">
          {conversation.otherUser.avatar ? (
            <img
              src={conversation.otherUser.avatar}
              alt={conversation.otherUser.name}
              className="w-full h-full rounded-full object-cover"
            />
          ) : (
            <UserIcon className="h-5 w-5 text-primary-600" />
          )}

          {/* مؤشر الحالة المتصلة */}
          {conversation.isOnline && (
            <div className="absolute -bottom-0.5 -right-0.5 w-3 h-3 bg-green-500 border-2 border-white rounded-full"></div>
          )}
        </div>

        {/* محتوى المحادثة */}
        <div className="flex-1 min-w-0">
          {/* اسم المستخدم وعدد الرسائل غير المقروءة */}
          <div className="flex items-center justify-between mb-1">
            <p
              className={`font-medium truncate ${
                conversation.unreadCount > 0 ? "text-dark-800" : "text-dark-600"
              }`}
            >
              {conversation.otherUser.name}
            </p>
            {conversation.unreadCount > 0 && (
              <span className="bg-primary-500 text-white text-xs rounded-full px-2 py-1 min-w-[20px] text-center">
                {conversation.unreadCount > 99
                  ? "99+"
                  : conversation.unreadCount}
              </span>
            )}
          </div>

          {/* عنوان الإعلان إن وجد */}
          {conversation.ad && (
            <p className="text-sm text-primary-600 truncate mb-1">
              📦 {conversation.ad.title}
            </p>
          )}

          {/* آخر رسالة */}
          <p
            className={`text-sm truncate ${
              conversation.unreadCount > 0
                ? "text-gray-700 font-medium"
                : "text-gray-500"
            }`}
          >
            {conversation.content}
          </p>

          {/* وقت آخر رسالة */}
          <div className="flex items-center mt-1">
            <ClockIcon className="h-3 w-3 text-gray-400 ml-1" />
            <span className="text-xs text-gray-400">
              {formatMessageTime(conversation.createdAt)}
            </span>
          </div>
        </div>
      </div>
    </button>
  );
}

// مكون لعرض إحصائيات المحادثات
interface ConversationStatsProps {
  totalConversations: number;
  unreadCount: number;
}

export function ConversationStats({
  totalConversations,
  unreadCount,
}: ConversationStatsProps) {
  return (
    <div className="px-4 py-2 bg-gray-50 border-b border-gray-200 text-sm text-gray-600">
      <div className="flex items-center justify-between">
        <span>{totalConversations} محادثة</span>
        {unreadCount > 0 && (
          <span className="text-primary-600 font-medium">
            {unreadCount} غير مقروءة
          </span>
        )}
      </div>
    </div>
  );
}
